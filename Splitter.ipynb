{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["#invoice_path=\"/home/<USER>/invoice/inv-collection/image_splitter/SOU_Carahsoft_IN859177.pdf\"\n", "invoice_path=\"/home/<USER>/invoice/inv-collection/image_splitter3/SOU_Carahsoft_IN859177-merged-merged.pdf\""]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["#Importing tensorflow libraries for prediction \n", "from tensorflow.keras.models import load_model\n", "from tensorflow.keras.preprocessing import image\n", "import numpy as np\n", "from Invoice.ML.TFPredictions import *"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import os \n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\""]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["TF_path_=\"model.h5\"\n", "TFmodel2 = load_model(TF_path_)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["from pdf2image import convert_from_path, convert_from_bytes\n", "from pdf2image.exceptions import (\n", "    PDFInfoNotInstalledError,\n", "    PDFPageCountError,\n", "    PDFSyntaxError\n", ")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["5  images length\n"]}], "source": ["images = convert_from_path(invoice_path, size=(2000, None))\n", "print(len(list(images)),\" images length\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["image saved /home/<USER>/invoice/inv-collection/image_splitter3/SOU_Carahsoft_IN859177-merged-merged.pdf_0.png\n", "image saved /home/<USER>/invoice/inv-collection/image_splitter3/SOU_Carahsoft_IN859177-merged-merged.pdf_1.png\n", "image saved /home/<USER>/invoice/inv-collection/image_splitter3/SOU_Carahsoft_IN859177-merged-merged.pdf_2.png\n", "image saved /home/<USER>/invoice/inv-collection/image_splitter3/SOU_Carahsoft_IN859177-merged-merged.pdf_3.png\n", "image saved /home/<USER>/invoice/inv-collection/image_splitter3/SOU_Carahsoft_IN859177-merged-merged.pdf_4.png\n"]}], "source": ["images_count=0 \n", "images_list={}\n", "for i in images:\n", "    i.save(invoice_path+\"_\"+str(images_count)+\".png\")\n", "    print(\"image saved\",invoice_path+\"_\"+str(images_count)+\".png\")\n", "    images_list[images_count]=invoice_path+\"_\"+str(images_count)+\".png\"\n", "    images_count=images_count+1"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["import webbrowser\n", "import os\n", "import json\n", "import boto3\n", "import io\n", "from io import BytesIO\n", "import sys\n", "import base64\n", "\n", "\n", "textract = boto3.client('textract','us-east-1')\n", "               \n", "\n", "# Instance of boto3 client \n", "client = boto3.client(\n", "    service_name='textract',\n", "    region_name='us-east-1',\n", "    endpoint_url='https://textract.us-east-1.amazonaws.com',\n", ")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["\n", "def reader( file):\n", "    with open(file, \"rb\") as image_file:\n", "        img_test = image_file.read()\n", "        bytes_test = bytearray(img_test)\n", "    return bytes_test"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1, 2, 3, 4, 5, 6, 7, 8, 17, 18, 19, 20, 21]\n", "regex_id  [1, 2, 3, 4]\n", "regex_id  [6]\n", "regex_id  [6]\n", "regex_id  [7]\n", "match threshold  95\n"]}], "source": ["config_name=\"global_config\" \n", "from Invoice.DbConfig import *\n", "from Invoice.FieldMatch import * \n", "\n", "field_match=FieldMatch()\n", "db_extrtaction_list=get_extraction_list(config_name,None)\n", "w_list=field_match.cerate_word_list(db_extrtaction_list)\n", "\n", "match_threshold=get_ai_fieldmatch_threshold(config_name)\n", "print(\"match threshold \",match_threshold)\n"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["NER 2 models laoded.....  \n", "TENSORFLOW INVOICE DETECTION MODEL LOADED......\n", "base diretory /home/<USER>/invoice/invoice-flow/inv-flow-dvc/db-repo/aavenir.invoiceflow.ai\n", "object detection API  <tensorflow.python.keras.engine.functional.Functional object at 0x7f11cc693690>\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "TENSORFLOW Object Detection API Model Loaded ......\n"]}], "source": ["#from Invoice.FieldSearch import *\n", "from Invoice.FormFieldExtractionWrapper import *"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0\n", "{'I': 0.9708055, 'NI': 0.029194534}\n", "fetching Boto Response .....\n", "\n", "\n", "== FOUND KEY : VALUE pairs ===\n", "\n", "[1, 2, 3, 4, 5, 6, 7, 8, 17, 18, 19, 20, 21]\n", "regex_id  [1, 2, 3, 4]\n", "regex_id  [6]\n", "regex_id  [6]\n", "top ....  0\n", "bottom......  0\n", "******************** Invoice_No ******************************\n", "word list \n", "['invoice no', 'invoice no.', 'invoice id', 'invoice #', 'invoice number', 'invoice: no', 'invoice: no.', 'invoice: id', 'invoice: #', 'invoice: number', 'Invoice ID no', 'Invoice ID no.', 'Invoice ID id', 'Invoice ID #', 'Invoice ID number', ' no', ' no.', ' id', ' #', ' number']\n", "--> invoice invoice # 88\n", "--> invoice number invoice number 100\n", "Best Match Found  ('Invoice Number', 0.7927073836326599, 0.06254645437002182, 0.891792356967926, 0.06254645437002182, (0.842249870300293, 0.06254645437002182), 99.85523986816406)\n", "###  carahsoft.  degrees : -176  match word : Invoice Number  | distance : 0.8487276642363247\n", "###  Date  degrees : -145  match word : Invoice Number  | distance : 0.1107635695849546\n", "###  Page  degrees : -29  match word : Invoice Number  | distance : 0.03247664037728651\n", "###  Invoice  degrees : -176  match word : Invoice Number  | distance : 0.4337688909874181\n", "###  Oct 19, 2020  degrees : -157  match word : Invoice Number  | distance : 0.12533322603000666\n", "###  1  degrees : -15  match word : Invoice Number  | distance : 0.017517865570436955\n", "###  Carahsoft Technology Corp  degrees : 179  match word : Invoice Number  | distance : 0.8521078851494054\n", "###  Invoice Number  degrees : 0  match word : Invoice Number  | distance : 0.09908497333526611\n", "###  IN859177  degrees : 96  match word : Invoice Number  | distance : 0.07929663164264408\n", "###  11493 Sunset Hills Road, Suite 100  degrees : 178  match word : Invoice Number  | distance : 0.8521258082535089\n", "###  <PERSON><PERSON>, VA 20190  degrees : 177  match word : Invoice Number  | distance : 0.8534980116934615\n", "###  USA  degrees : 176  match word : Invoice Number  | distance : 0.****************\n", "###  Sold To:  degrees : 169  match word : Invoice Number  | distance : 0.****************\n", "###  Ship To:  degrees : 149  match word : Invoice Number  | distance : 0.****************\n", "###  State of Utah  degrees : 168  match word : Invoice Number  | distance : 0.****************\n", "###  State of Utah - Dept of Administrative Services  degrees : 127  match word : Invoice Number  | distance : 0.****************\n", "###  Attn: Accounts Payable  degrees : 166  match word : Invoice Number  | distance : 0.***************\n", "###  Attn: <PERSON>  degrees : 139  match word : Invoice Number  | distance : 0.****************\n", "###  1 State Office Building, Flr 6  degrees : 165  match word : Invoice Number  | distance : 0.****************\n", "###  1 State Office Building  degrees : 137  match word : Invoice Number  | distance : 0.*****************\n", "###  Salt Lake City, UT 84114  degrees : 164  match word : Invoice Number  | distance : 0.****************\n", "###  Basement, B108  degrees : 137  match word : Invoice Number  | distance : 0.****************\n", "###  Salt Lake City, UT 84114  degrees : 132  match word : Invoice Number  | distance : 0.****************\n", "###  PO Number  degrees : 161  match word : Invoice Number  | distance : 0.****************\n", "###  Order Date  degrees : 156  match word : Invoice Number  | distance : 0.****************\n", "###  Customer No.  degrees : 150  match word : Invoice Number  | distance : 0.5505459929849298\n", "###  Salesperson  degrees : 143  match word : Invoice Number  | distance : 0.4527443992639466\n", "###  Order No.  degrees : 123  match word : Invoice Number  | distance : 0.3243495601215218\n", "###  Ship Via  degrees : 93  match word : Invoice Number  | distance : 0.244315469069626\n", "###  Terms  degrees : 75  match word : Invoice Number  | distance : 0.22845373669927568\n", "###  PO210064496  degrees : 160  match word : Invoice Number  | distance : 0.8007914780536006\n", "###  Oct 15, 2020  degrees : 154  match word : Invoice Number  | distance : 0.6480413978007125\n", "###  UTA001  degrees : 149  match word : Invoice Number  | distance : 0.5353863590412009\n", "###  BDRAKE  degrees : 141  match word : Invoice Number  | distance : 0.4478843235546835\n", "###  20094500  degrees : 121  match word : Invoice Number  | distance : 0.331945344039772\n", "###  GROUND  degrees : 92  match word : Invoice Number  | distance : 0.25857454349150905\n", "###  N30  degrees : 76  match word : Invoice Number  | distance : 0.24268206288836422\n", "###  Qty.  degrees : 160  match word : Invoice Number  | distance : 0.8596423168976716\n", "###  Qty.  degrees : 159  match word : Invoice Number  | distance : 0.7963960199423159\n", "###  Ord.  degrees : 160  match word : Invoice Number  | distance : 0.8641127818970751\n", "###  Shp.  degrees : 158  match word : Invoice Number  | distance : 0.8024912140671999\n", "###  Item Number  degrees : 154  match word : Invoice Number  | distance : 0.7224123535191672\n", "###  Line  degrees : 150  match word : Invoice Number  | distance : 0.6071173226231151\n", "###  Description  degrees : 140  match word : Invoice Number  | distance : 0.5014113389519136\n", "###  Unit Price  degrees : 115  match word : Invoice Number  | distance : 0.3440251274550101\n", "###  Extended Price  degrees : 86  match word : Invoice Number  | distance : 0.2852129880688682\n", "###  1.00  degrees : 157  match word : Invoice Number  | distance : 0.8525061428190618\n", "###  1.00  degrees : 155  match word : Invoice Number  | distance : 0.7899391643748906\n", "###  SW-001-4.0-PL04-2  degrees : 152  match word : Invoice Number  | distance : 0.7620420586430525\n", "###  1  degrees : 147  match word : Invoice Number  | distance : 0.6084552589156351\n", "###  Advanced Session Actual QTY 10  degrees : 139  match word : Invoice Number  | distance : 0.5885051800079053\n", "###  456.33  degrees : 103  match word : Invoice Number  | distance : 0.33626602698159186\n", "###  456.33  degrees : 78  match word : Invoice Number  | distance : 0.3036749470843733\n", "###  1.00  degrees : 155  match word : Invoice Number  | distance : 0.8648888149370935\n", "###  1.00  degrees : 153  match word : Invoice Number  | distance : 0.8033739691128794\n", "###  SW-001-4.0-PL03-2  degrees : 150  match word : Invoice Number  | distance : 0.774806373195995\n", "###  2  degrees : 144  match word : Invoice Number  | distance : 0.6261897737291102\n", "###  Basic Session Actual QTY 10  degrees : 137  match word : Invoice Number  | distance : 0.605221955936283\n", "###  182.53  degrees : 102  match word : Invoice Number  | distance : 0.36528673384328675\n", "###  182.53  degrees : 79  match word : Invoice Number  | distance : 0.33609886478199\n", "###  1.00  degrees : 153  match word : Invoice Number  | distance : 0.877912838536771\n", "###  1.00  degrees : 151  match word : Invoice Number  | distance : 0.8172911153145409\n", "###  SW-001-4.0-PL06-2  degrees : 148  match word : Invoice Number  | distance : 0.788266523259836\n", "###  3  degrees : 142  match word : Invoice Number  | distance : 0.643928456364669\n", "###  Conference Session Actual QTY 2  degrees : 133  match word : Invoice Number  | distance : 0.623984785600787\n", "###  14.60  degrees : 100  match word : Invoice Number  | distance : 0.39278412680566116\n", "###  14.60  degrees : 79  match word : Invoice Number  | distance : 0.368630019989969\n", "###  1.00  degrees : 151  match word : Invoice Number  | distance : 0.8920015183305033\n", "###  1.00  degrees : 149  match word : Invoice Number  | distance : 0.8325713898344491\n", "###  SW-001-4.0-AL04  degrees : 146  match word : Invoice Number  | distance : 0.8052487015007226\n", "###  4  degrees : 139  match word : Invoice Number  | distance : 0.6633323558895102\n", "###  Contact Center Level 1- Support Actual  degrees : 130  match word : Invoice Number  | distance : 0.6444326405178281\n", "###  394.2700  degrees : 101  match word : Invoice Number  | distance : 0.43112865261280375\n", "###  394.27  degrees : 80  match word : Invoice Number  | distance : 0.40097729844696073\n", "###  QTY 4  degrees : 136  match word : Invoice Number  | distance : 0.6527854675661162\n", "###  1.00  degrees : 149  match word : Invoice Number  | distance : 0.9096745917999464\n", "###  1.00  degrees : 146  match word : Invoice Number  | distance : 0.8514348871528186\n", "###  SW-001-4.0-AA17-2  degrees : 143  match word : Invoice Number  | distance : 0.82451717109606\n", "###  5  degrees : 137  match word : Invoice Number  | distance : 0.6866119472470454\n", "###  Interaction Report Assistant Actual QTY  degrees : 127  match word : Invoice Number  | distance : 0.6685693574182446\n", "###  0.00  degrees : 98  match word : Invoice Number  | distance : 0.45762343067259\n", "###  0.00  degrees : 80  match word : Invoice Number  | distance : 0.4390903004463593\n", "###  1  degrees : 134  match word : Invoice Number  | distance : 0.6772206200669698\n", "###  1.00  degrees : 147  match word : Invoice Number  | distance : 0.9286620987957458\n", "###  1.00  degrees : 144  match word : Invoice Number  | distance : 0.8716025591341265\n", "###  SW-001-4.0-AA01-2  degrees : 141  match word : Invoice Number  | distance : 0.8461094023309059\n", "###  6  degrees : 134  match word : Invoice Number  | distance : 0.7118706711704369\n", "###  Interaction Supervisor add-on Actual  degrees : 126  match word : Invoice Number  | distance : 0.694961766060703\n", "###  76.06  degrees : 98  match word : Invoice Number  | distance : 0.49631809930750126\n", "###  76.06  degrees : 81  match word : Invoice Number  | distance : 0.4770801864946981\n", "###  QTY 1  degrees : 131  match word : Invoice Number  | distance : 0.7035682543015217\n", "###  1.00  degrees : 144  match word : Invoice Number  | distance : 0.9489584494860496\n", "###  1.00  degrees : 142  match word : Invoice Number  | distance : 0.8934190016010454\n", "###  SW-001-4.0-PL09-2  degrees : 138  match word : Invoice Number  | distance : 0.8680568907348782\n", "###  7  degrees : 132  match word : Invoice Number  | distance : 0.7378721938497417\n", "###  Media Session Actual QTY 12  degrees : 125  match word : Invoice Number  | distance : 0.7205356335446104\n", "###  124.12  degrees : 98  match word : Invoice Number  | distance : 0.534747473116695\n", "###  124.12  degrees : 82  match word : Invoice Number  | distance : 0.5154127953489868\n", "###  START DATE  degrees : 127  match word : Invoice Number  | distance : 0.7319281265968901\n", "###  10/28/2020  degrees : 126  match word : Invoice Number  | distance : 0.740951902680323\n", "###  END DATE  degrees : 126  match word : Invoice Number  | distance : 0.7515745574025783\n", "###  6/30/2021  degrees : 125  match word : Invoice Number  | distance : 0.7616814860093937\n", "###  Dept of Administrative Services  degrees : 121  match word : Invoice Number  | distance : 0.7704062243966562\n", "###  Due Date  degrees : 124  match word : Invoice Number  | distance : 0.7927055913690026\n", "###  Amount Due  degrees : 112  match word : Invoice Number  | distance : 0.7061779575144442\n", "###  Nov 18, 2020  degrees : 123  match word : Invoice Number  | distance : 0.8045449872258298\n", "###  1,247.91  degrees : 113  match word : Invoice Number  | distance : 0.7190436163654006\n", "###  Currency in USD unless otherwise specified  degrees : 119  match word : Invoice Number  | distance : 0.9361456529597042\n", "###  Invoice continued on next page  degrees : 130  match word : Invoice Number  | distance : 1.119169397499672\n", "*************dist list ****************\n", "{0.07929663164264408: ('3083bbc5-73c8-4c5d-b178-0d3bb2353787', 96), 0.244315469069626: ('6b96fc7b-0162-45ff-80db-d89e4f88cdac', 93), 0.22845373669927568: ('db0cac4b-6565-450f-ad24-a94e1d6c05b7', 75), 0.24268206288836422: ('753c6f73-4801-400f-ba46-a49e7b58884d', 76)}\n", "Nearest matches  0.07929663164264408   96   Invoice Number   IN859177\n", "Nearest matches  0.22845373669927568   75   Invoice Number   Terms\n", "Nearest matches  0.24268206288836422   76   Invoice Number   N30\n", "Nearest matches  0.244315469069626   93   Invoice Number   Ship Via\n", "dst list  7.929663164264408 96 Invoice Number IN859177 conf  99.79399108886719\n", "*************************\n", "regex match  IN859177\n", "indices  0.07929663164264408 ['IN859177'] 99.79399108886719\n", "dst list  22.845373669927568 75 Invoice Number Terms conf  99.**********\n", "*************************\n", "dst list  24.26820628883642 76 Invoice Number N30 conf  99.33849334716797\n", "*************************\n", "regex match  N30\n", "indices  0.24268206288836422 ['N30'] 99.33849334716797\n", "dst list  24.431546906962602 93 Invoice Number Ship Via conf  99.74624633789062\n", "*************************\n", "************* Invoice_No ********************\n", "match list  {0: (['IN859177'], 99.79399108886719), 2: (['N30'], 79.33849334716797)}\n", "match list keys  [0, 2]\n", "Invoice_No  :  ['IN859177'] confidence : 99.79399108886719\n", "top ....  0\n", "bottom......  0\n", "******************** PO_no ******************************\n", "word list \n", "['po no', 'po number', 'po order', 'po ', 'purchase no', 'purchase number', 'purchase order', 'purchase ', 'P.O. no', 'P.O. number', 'P.O. order', 'P.O. ', ' no', ' number', ' order', ' ']\n", "--> po number po number 100\n", "Best Match Found  ('PO Number', 0.13119107484817505, 0.2906341850757599, 0.20425523817539215, 0.2906341850757599, (0.1677231565117836, 0.2906341850757599), 99.78593444824219)\n", "###  carahsoft.  degrees : -90  match word : PO Number  | distance : 0.311262497028081\n", "###  Date  degrees : -22  match word : PO Number  | distance : 0.6353873225837741\n", "###  Page  degrees : -19  match word : PO Number  | distance : 0.7214923295520126\n", "###  Invoice  degrees : -37  match word : PO Number  | distance : 0.35517424368627976\n", "###  Oct 19, 2020  degrees : -20  match word : PO Number  | distance : 0.6136849089408434\n", "###  1  degrees : -18  match word : PO Number  | distance : 0.7362794305623128\n", "###  Carahsoft Technology Corp  degrees : -100  match word : PO Number  | distance : 0.2799906409672391\n", "###  Invoice Number  degrees : -18  match word : PO Number  | distance : 0.6311100857946692\n", "###  IN859177  degrees : -17  match word : PO Number  | distance : 0.6465948381302976\n", "###  11493 Sunset Hills Road, Suite 100  degrees : -96  match word : PO Number  | distance : 0.26720399387648247\n", "###  <PERSON><PERSON>, VA 20190  degrees : -111  match word : PO Number  | distance : 0.25760401996651544\n", "###  USA  degrees : -122  match word : PO Number  | distance : 0.*****************\n", "###  Sold To:  degrees : -100  match word : PO Number  | distance : 0.*****************\n", "###  Ship To:  degrees : -12  match word : PO Number  | distance : 0.****************\n", "###  State of Utah  degrees : -91  match word : PO Number  | distance : 0.*****************\n", "###  State of Utah - Dept of Administrative Services  degrees : -8  match word : PO Number  | distance : 0.****************\n", "###  Attn: Accounts Payable  degrees : -70  match word : PO Number  | distance : 0.*****************\n", "###  Attn: <PERSON>  degrees : -8  match word : PO Number  | distance : 0.***************\n", "###  1 State Office Building, Flr 6  degrees : -56  match word : PO Number  | distance : 0.*****************\n", "###  1 State Office Building  degrees : -7  match word : PO Number  | distance : 0.*****************\n", "###  Salt Lake City, UT 84114  degrees : -58  match word : PO Number  | distance : 0.*****************\n", "###  Basement, B108  degrees : -5  match word : PO Number  | distance : 0.*****************\n", "###  Salt Lake City, UT 84114  degrees : -4  match word : PO Number  | distance : 0.*****************\n", "###  PO Number  degrees : 0  match word : PO Number  | distance : 0.****************\n", "###  Order Date  degrees : 0  match word : PO Number  | distance : 0.*****************\n", "###  Customer No.  degrees : 0  match word : PO Number  | distance : 0.****************\n", "###  Salesperson  degrees : 0  match word : PO Number  | distance : 0.2963559867364473\n", "###  Order No.  degrees : 0  match word : PO Number  | distance : 0.456816732920564\n", "###  Ship Via  degrees : 0  match word : PO Number  | distance : 0.5996022252807022\n", "###  Terms  degrees : 0  match word : PO Number  | distance : 0.675583826600404\n", "###  PO210064496  degrees : 78  match word : PO Number  | distance : 0.0769748435360574\n", "###  Oct 15, 2020  degrees : 5  match word : PO Number  | distance : 0.08779081645113505\n", "###  UTA001  degrees : 3  match word : PO Number  | distance : 0.21082873275504616\n", "###  BDRAKE  degrees : 2  match word : PO Number  | distance : 0.3115218150947595\n", "###  20094500  degrees : 1  match word : PO Number  | distance : 0.46147758428576263\n", "###  GROUND  degrees : 1  match word : PO Number  | distance : 0.5983429939301131\n", "###  N30  degrees : 1  match word : PO Number  | distance : 0.6859722455557177\n", "###  Qty.  degrees : 158  match word : PO Number  | distance : 0.13558887724236615\n", "###  Qty.  degrees : 115  match word : PO Number  | distance : 0.07272508551319357\n", "###  Ord.  degrees : 150  match word : PO Number  | distance : 0.1397393208284071\n", "###  Shp.  degrees : 109  match word : PO Number  | distance : 0.0809781047386887\n", "###  Item Number  degrees : 25  match word : PO Number  | distance : 0.04908169092938365\n", "###  Line  degrees : 13  match word : PO Number  | distance : 0.1524897766834466\n", "###  Description  degrees : 7  match word : PO Number  | distance : 0.2711386732856245\n", "###  Unit Price  degrees : 4  match word : PO Number  | distance : 0.4808101848244479\n", "###  Extended Price  degrees : 3  match word : PO Number  | distance : 0.608189200959328\n", "###  1.00  degrees : 128  match word : PO Number  | distance : 0.13269222247520376\n", "###  1.00  degrees : 84  match word : PO Number  | distance : 0.08628875509372341\n", "###  SW-001-4.0-PL04-2  degrees : 41  match word : PO Number  | distance : 0.07638276333434524\n", "###  1  degrees : 20  match word : PO Number  | distance : 0.17746341337201604\n", "###  Advanced Session Actual QTY 10  degrees : 13  match word : PO Number  | distance : 0.19836403651358003\n", "###  456.33  degrees : 7  match word : PO Number  | distance : 0.5483762108883053\n", "###  456.33  degrees : 5  match word : PO Number  | distance : 0.6861442325782292\n", "###  1.00  degrees : 119  match word : PO Number  | distance : 0.15377385549479927\n", "###  1.00  degrees : 86  match word : PO Number  | distance : 0.11602718288922231\n", "###  SW-001-4.0-PL03-2  degrees : 52  match word : PO Number  | distance : 0.10851123211540845\n", "###  2  degrees : 28  match word : PO Number  | distance : 0.19261652406810806\n", "###  Basic Session Actual QTY 10  degrees : 19  match word : PO Number  | distance : 0.2135401674794605\n", "###  182.53  degrees : 10  match word : PO Number  | distance : 0.5552157797353566\n", "###  182.53  degrees : 8  match word : PO Number  | distance : 0.6920656495111265\n", "###  1.00  degrees : 113  match word : PO Number  | distance : 0.1779850726515137\n", "###  1.00  degrees : 87  match word : PO Number  | distance : 0.14669676065911388\n", "###  SW-001-4.0-PL06-2  degrees : 58  match word : PO Number  | distance : 0.1407823484959624\n", "###  3  degrees : 35  match word : PO Number  | distance : 0.21273153518478544\n", "###  Conference Session Actual QTY 2  degrees : 23  match word : PO Number  | distance : 0.23150962873375872\n", "###  14.60  degrees : 13  match word : PO Number  | distance : 0.5694957673610728\n", "###  14.60  degrees : 10  match word : PO Number  | distance : 0.7046846568415485\n", "###  1.00  degrees : 109  match word : PO Number  | distance : 0.20463788767921862\n", "###  1.00  degrees : 87  match word : PO Number  | distance : 0.17811698990520455\n", "###  SW-001-4.0-AL04  degrees : 65  match word : PO Number  | distance : 0.17322934405313312\n", "###  4  degrees : 40  match word : PO Number  | distance : 0.23532461152850687\n", "###  Contact Center Level 1- Support Actual  degrees : 27  match word : PO Number  | distance : 0.2532512030113617\n", "###  394.2700  degrees : 16  match word : PO Number  | distance : 0.5565096035943993\n", "###  394.27  degrees : 13  match word : PO Number  | distance : 0.7037703177586995\n", "###  QTY 4  degrees : 38  match word : PO Number  | distance : 0.2623689089306562\n", "###  1.00  degrees : 106  match word : PO Number  | distance : 0.237395470859986\n", "###  1.00  degrees : 88  match word : PO Number  | distance : 0.21502696560204626\n", "###  SW-001-4.0-AA17-2  degrees : 67  match word : PO Number  | distance : 0.21082434999394817\n", "###  5  degrees : 46  match word : PO Number  | distance : 0.2643422758358086\n", "###  Interaction Report Assistant Actual QTY  degrees : 32  match word : PO Number  | distance : 0.2812125124213879\n", "###  0.00  degrees : 19  match word : PO Number  | distance : 0.5966097515095603\n", "###  0.00  degrees : 15  match word : PO Number  | distance : 0.7283425679526732\n", "###  1  degrees : 45  match word : PO Number  | distance : 0.29173149528705344\n", "###  1.00  degrees : 103  match word : PO Number  | distance : 0.2719805387261978\n", "###  1.00  degrees : 88  match word : PO Number  | distance : 0.2525545066595066\n", "###  SW-001-4.0-AA01-2  degrees : 71  match word : PO Number  | distance : 0.24917439849566667\n", "###  6  degrees : 51  match word : PO Number  | distance : 0.29585592817395023\n", "###  Interaction Supervisor add-on Actual  degrees : 37  match word : PO Number  | distance : 0.31072186015253295\n", "###  76.06  degrees : 22  match word : PO Number  | distance : 0.6042128454357716\n", "###  76.06  degrees : 18  match word : PO Number  | distance : 0.7331326475233455\n", "###  QTY 1  degrees : 47  match word : PO Number  | distance : 0.32134478306942615\n", "###  1.00  degrees : 101  match word : PO Number  | distance : 0.30711406524303575\n", "###  1.00  degrees : 88  match word : PO Number  | distance : 0.2903023213050106\n", "###  SW-001-4.0-PL09-2  degrees : 73  match word : PO Number  | distance : 0.2873188840853761\n", "###  7  degrees : 55  match word : PO Number  | distance : 0.328474849709999\n", "###  Media Session Actual QTY 12  degrees : 43  match word : PO Number  | distance : 0.3410286171727862\n", "###  124.12  degrees : 25  match word : PO Number  | distance : 0.6154487208971383\n", "###  124.12  degrees : 21  match word : PO Number  | distance : 0.7413063229140939\n", "###  START DATE  degrees : 49  match word : PO Number  | distance : 0.36322636295027055\n", "###  10/28/2020  degrees : 51  match word : PO Number  | distance : 0.3749377734773075\n", "###  END DATE  degrees : 52  match word : PO Number  | distance : 0.3862535040588103\n", "###  6/30/2021  degrees : 53  match word : PO Number  | distance : 0.3976750908388323\n", "###  Dept of Administrative Services  degrees : 48  match word : PO Number  | distance : 0.40815571106264587\n", "###  Due Date  degrees : 56  match word : PO Number  | distance : 0.4302422323457309\n", "###  Amount Due  degrees : 42  match word : PO Number  | distance : 0.5163127544504879\n", "###  Nov 18, 2020  degrees : 56  match word : PO Number  | distance : 0.443857265882987\n", "###  1,247.91  degrees : 44  match word : PO Number  | distance : 0.5287269994505799\n", "###  Currency in USD unless otherwise specified  degrees : 61  match word : PO Number  | distance : 0.50491645116774\n", "###  Invoice continued on next page  degrees : 88  match word : PO Number  | distance : 0.5499831847296807\n", "*************dist list ****************\n", "{0.*****************: ('889eb647-5265-4a6a-b3cb-3663daca4f9d', 0), 0.****************: ('8d755499-e1d4-4230-ae05-bd95375d8d5d', 0), 0.0769748435360574: ('acce70af-75ea-4fa3-ac66-130ca752b487', 78), 0.08779081645113505: ('4475a3cd-546a-49e0-9eff-1ab41329f049', 5), 0.21082873275504616: ('f0ee17c9-cdfc-4ea1-b3c6-5b96c2fc45e5', 3), 0.04908169092938365: ('a01cdf20-54ad-409e-819c-0daac81f4aaf', 25), 0.1524897766834466: ('1db7ad60-f0cc-465c-a4ec-6f4a38ea69d8', 13), 0.08628875509372341: ('d65c3770-d518-4fe6-b397-0ad568e911e8', 84), 0.07638276333434524: ('3dfe420c-c915-43be-9ce1-2efa02b1d2a1', 41), 0.17746341337201604: ('14f5e722-72c2-49b0-a0ce-22be69bb04d8', 20), 0.19836403651358003: ('6b0c42cf-b543-41b5-8605-ddceb37ac042', 13), 0.11602718288922231: ('9afbe552-8920-40b6-a80c-22c32cae430e', 86), 0.10851123211540845: ('fc269659-9a9c-47d1-800b-1972a405691f', 52), 0.19261652406810806: ('d10f98bf-1ded-4fb9-9666-b17db3c55d34', 28), 0.2135401674794605: ('9d2197de-f1d8-4eae-8f49-efc9dc788278', 19), 0.14669676065911388: ('bc428f6e-9e40-4398-ac28-c8ead6388bdf', 87), 0.1407823484959624: ('76d751cb-968d-48a6-929c-b3715915a633', 58), 0.21273153518478544: ('617e14d3-d1d8-42c3-983e-cd11b09112b6', 35), 0.23150962873375872: ('2e5e0a1f-5421-4725-b68f-3f8687247ce8', 23), 0.17811698990520455: ('918ba9d2-5047-412f-a50e-d882d15d362b', 87), 0.17322934405313312: ('d34c7ac4-9a6e-4698-b9d1-fe58a68c9ee1', 65), 0.23532461152850687: ('c884bcec-723d-4f02-b459-9cab73540e68', 40), 0.21502696560204626: ('a4296544-6eee-458e-9efd-9080171f593f', 88), 0.21082434999394817: ('a75338ed-0e53-46df-98c9-5b50893cf298', 67), 0.24917439849566667: ('2603eac9-0b79-4cbf-9763-3443865c9a37', 71)}\n", "Nearest matches  0.04908169092938365   25   PO Number   Item Number\n", "Nearest matches  0.07638276333434524   41   PO Number   SW-001-4.0-PL04-2\n", "Nearest matches  0.0769748435360574   78   PO Number   PO210064496\n", "Nearest matches  0.08628875509372341   84   PO Number   1.00\n", "Nearest matches  0.*****************   0   PO Number   Order Date\n", "Nearest matches  0.08779081645113505   5   PO Number   Oct 15, 2020\n", "Nearest matches  0.10851123211540845   52   PO Number   SW-001-4.0-PL03-2\n", "Nearest matches  0.11602718288922231   86   PO Number   1.00\n", "Nearest matches  0.1407823484959624   58   PO Number   SW-001-4.0-PL06-2\n", "Nearest matches  0.14669676065911388   87   PO Number   1.00\n", "Nearest matches  0.1524897766834466   13   PO Number   Line\n", "Nearest matches  0.17322934405313312   65   PO Number   SW-001-4.0-AL04\n", "Nearest matches  0.17746341337201604   20   PO Number   1\n", "Nearest matches  0.17811698990520455   87   PO Number   1.00\n", "Nearest matches  0.****************   0   PO Number   Customer No.\n", "Nearest matches  0.19261652406810806   28   PO Number   2\n", "Nearest matches  0.19836403651358003   13   PO Number   Advanced Session Actual QTY 10\n", "Nearest matches  0.21082434999394817   67   PO Number   SW-001-4.0-AA17-2\n", "Nearest matches  0.21082873275504616   3   PO Number   UTA001\n", "Nearest matches  0.21273153518478544   35   PO Number   3\n", "Nearest matches  0.2135401674794605   19   PO Number   Basic Session Actual QTY 10\n", "Nearest matches  0.21502696560204626   88   PO Number   1.00\n", "Nearest matches  0.23150962873375872   23   PO Number   Conference Session Actual QTY 2\n", "Nearest matches  0.23532461152850687   40   PO Number   4\n", "Nearest matches  0.24917439849566667   71   PO Number   SW-001-4.0-AA01-2\n", "dst list  4.9081690929383655 25 PO Number Item Number conf  99.28668212890625\n", "*************************\n", "dst list  7.638276333434524 41 PO Number SW-001-4.0-PL04-2 conf  82.2531967163086\n", "*************************\n", "regex match  SW-001-4\n", "indices  0.07638276333434524 ['SW-001-4'] 82.2531967163086\n", "dst list  7.69748435360574 78 PO Number PO210064496 conf  96.79901123046875\n", "*************************\n"]}, {"name": "stdout", "output_type": "stream", "text": ["regex match  PO210064496\n", "indices  0.0769748435360574 ['PO210064496'] 96.79901123046875\n", "dst list  8.62887550937234 84 PO Number 1.00 conf  99.90125274658203\n", "*************************\n", "regex match  00\n", "indices  0.08628875509372341 ['00'] 99.90125274658203\n", "dst list  8.676066324412417 0 PO Number Order Date conf  99.66044616699219\n", "*************************\n", "dst list  8.779081645113505 5 PO Number Oct 15, 2020 conf  99.49464416503906\n", "*************************\n", "regex match  15\n", "indices  0.08779081645113505 ['15'] 99.49464416503906\n", "dst list  10.851123211540845 52 PO Number SW-001-4.0-PL03-2 conf  93.15223693847656\n", "*************************\n", "regex match  SW-001-4\n", "indices  0.10851123211540845 ['SW-001-4'] 93.15223693847656\n", "dst list  11.602718288922231 86 PO Number 1.00 conf  99.85011291503906\n", "*************************\n", "regex match  00\n", "indices  0.11602718288922231 ['00'] 99.85011291503906\n", "dst list  14.07823484959624 58 PO Number SW-001-4.0-PL06-2 conf  95.05921173095703\n", "*************************\n", "regex match  SW-001-4\n", "indices  0.1407823484959624 ['SW-001-4'] 95.05921173095703\n", "dst list  14.66967606591139 87 PO Number 1.00 conf  99.89299774169922\n", "*************************\n", "regex match  00\n", "indices  0.14669676065911388 ['00'] 99.89299774169922\n", "************* PO_no ********************\n", "match list  {1: (['SW-001-4'], 72.2531967163086), 2: (['PO210064496'], 76.79901123046875), 3: (['00'], 69.90125274658203), 5: (['15'], 49.49464416503906), 6: (['SW-001-4'], 33.15223693847656), 7: (['00'], 29.850112915039062), 8: (['SW-001-4'], 15.059211730957031), 9: (['00'], 9.892997741699219)}\n", "match list keys  [1, 2, 3, 5, 6, 7, 8, 9]\n", "PO_no  :  ['SW-001-4'] confidence : 72.2531967163086\n", "top ....  0\n", "bottom......  0\n", "******************** Total ******************************\n", "word list \n", "['total due', 'total ', 'total ', 'balance due', 'balance ', 'balance ', 'TOTAL USD due', 'TOTAL USD ', 'TOTAL USD ', ' due', ' ', ' ']\n", "Best Match Found  None\n", "top ....  0\n", "bottom......  0\n", "******************** Issue_Date ******************************\n", "word list \n", "['issue ', 'issue date', 'issue ', ' ', ' date', ' ']\n", "--> date date 100\n", "Best Match Found  ('Date', 0.7850515842437744, 0.03296646475791931, 0.813474714756012, 0.03296646475791931, (0.7992631494998932, 0.03296646475791931), 99.68643188476562)\n", "###  carahsoft.  degrees : -179  match word : Date  | distance : 0.7695806333712948\n", "###  Date  degrees : 0  match word : Date  | distance : 0.02842313051223755\n", "###  Page  degrees : 0  match word : Date  | distance : 0.06472569246832355\n", "###  Invoice  degrees : 178  match word : Date  | distance : 0.35512359660287407\n", "###  Oct 19, 2020  degrees : 67  match word : Date  | distance : 0.04808050140061863\n", "###  1  degrees : 7  match word : Date  | distance : 0.08648879852219822\n", "###  Carahsoft Technology Corp  degrees : 177  match word : Date  | distance : 0.7744154169805324\n", "###  Invoice Number  degrees : 34  match word : Date  | distance : 0.03614218902389615\n", "###  IN859177  degrees : 44  match word : Date  | distance : 0.040324622187617336\n", "###  11493 Sunset Hills Road, Suite 100  degrees : 175  match word : Date  | distance : 0.7750577524478772\n", "###  <PERSON><PERSON>, VA 20190  degrees : 175  match word : Date  | distance : 0.****************\n", "###  USA  degrees : 174  match word : Date  | distance : 0.***************\n", "###  Sold To:  degrees : 166  match word : Date  | distance : 0.****************\n", "###  Ship To:  degrees : 138  match word : Date  | distance : 0.****************\n", "###  State of Utah  degrees : 164  match word : Date  | distance : 0.****************\n", "###  State of Utah - Dept of Administrative Services  degrees : 111  match word : Date  | distance : 0.****************\n", "###  Attn: Accounts Payable  degrees : 163  match word : Date  | distance : 0.****************\n", "###  Attn: <PERSON>  degrees : 127  match word : Date  | distance : 0.*****************\n", "###  1 State Office Building, Flr 6  degrees : 161  match word : Date  | distance : 0.****************\n", "###  1 State Office Building  degrees : 124  match word : Date  | distance : 0.*****************\n", "###  Salt Lake City, UT 84114  degrees : 160  match word : Date  | distance : 0.****************\n", "###  Basement, B108  degrees : 126  match word : Date  | distance : 0.****************\n", "###  Salt Lake City, UT 84114  degrees : 120  match word : Date  | distance : 0.*****************\n", "###  PO Number  degrees : 157  match word : Date  | distance : 0.****************\n", "###  Order Date  degrees : 151  match word : Date  | distance : 0.***************\n", "###  Customer No.  degrees : 144  match word : Date  | distance : 0.*****************\n", "###  Salesperson  degrees : 135  match word : Date  | distance : 0.40521356111881823\n", "###  Order No.  degrees : 112  match word : Date  | distance : 0.29926420509969875\n", "###  Ship Via  degrees : 83  match word : Date  | distance : 0.2577013857826317\n", "###  Terms  degrees : 68  match word : Date  | distance : 0.26612823324383805\n", "###  PO210064496  degrees : 156  match word : Date  | distance : 0.7369630099518858\n", "###  Oct 15, 2020  degrees : 149  match word : Date  | distance : 0.5892247591501857\n", "###  UTA001  degrees : 143  match word : Date  | distance : 0.48296646775536994\n", "###  BDRAKE  degrees : 133  match word : Date  | distance : 0.40375893495336296\n", "###  20094500  degrees : 110  match word : Date  | distance : 0.3100284622028608\n", "###  GROUND  degrees : 83  match word : Date  | distance : 0.2724431434856902\n", "###  N30  degrees : 69  match word : Date  | distance : 0.2828259239573272\n", "###  Qty.  degrees : 157  match word : Date  | distance : 0.7956930448245532\n", "###  Qty.  degrees : 155  match word : Date  | distance : 0.7340463559663931\n", "###  Ord.  degrees : 156  match word : Date  | distance : 0.8009509220882283\n", "###  Shp.  degrees : 154  match word : Date  | distance : 0.7409842417850196\n", "###  Item Number  degrees : 150  match word : Date  | distance : 0.6635691556821156\n", "###  Line  degrees : 145  match word : Date  | distance : 0.5539765271907265\n", "###  Description  degrees : 133  match word : Date  | distance : 0.45692505606745537\n", "###  Unit Price  degrees : 105  match word : Date  | distance : 0.3298770278441986\n", "###  Extended Price  degrees : 79  match word : Date  | distance : 0.30304893997212645\n", "###  1.00  degrees : 154  match word : Date  | distance : 0.7918152298952114\n", "###  1.00  degrees : 151  match word : Date  | distance : 0.731263880615031\n", "###  SW-001-4.0-PL04-2  degrees : 148  match word : Date  | distance : 0.7044137487489289\n", "###  1  degrees : 142  match word : Date  | distance : 0.5591465273714696\n", "###  Advanced Session Actual QTY 10  degrees : 133  match word : Date  | distance : 0.5406908652664087\n", "###  456.33  degrees : 95  match word : Date  | distance : 0.3397526197437752\n", "###  456.33  degrees : 72  match word : Date  | distance : 0.34105434971538395\n", "###  1.00  degrees : 152  match word : Date  | distance : 0.806298529013308\n", "###  1.00  degrees : 149  match word : Date  | distance : 0.7470079614535915\n", "###  SW-001-4.0-PL03-2  degrees : 146  match word : Date  | distance : 0.7196287930575768\n", "###  2  degrees : 139  match word : Date  | distance : 0.5798994789009105\n", "###  Basic Session Actual QTY 10  degrees : 131  match word : Date  | distance : 0.56066668030935\n", "###  182.53  degrees : 94  match word : Date  | distance : 0.3714035462874711\n", "###  182.53  degrees : 73  match word : Date  | distance : 0.3731418482123371\n", "###  1.00  degrees : 150  match word : Date  | distance : 0.821426496835122\n", "###  1.00  degrees : 147  match word : Date  | distance : 0.7632386727386842\n", "###  SW-001-4.0-PL06-2  degrees : 143  match word : Date  | distance : 0.7355503554832158\n", "###  3  degrees : 137  match word : Date  | distance : 0.6006458304057543\n", "###  Conference Session Actual QTY 2  degrees : 128  match word : Date  | distance : 0.5824961255292868\n", "###  14.60  degrees : 93  match word : Date  | distance : 0.4023026664568533\n", "###  14.60  degrees : 74  match word : Date  | distance : 0.4064147440283029\n", "###  1.00  degrees : 148  match word : Date  | distance : 0.8376270322492739\n", "###  1.00  degrees : 145  match word : Date  | distance : 0.7807982632271635\n", "###  SW-001-4.0-AL04  degrees : 142  match word : Date  | distance : 0.7548350300304698\n", "###  4  degrees : 135  match word : Date  | distance : 0.622914242445358\n", "###  Contact Center Level 1- Support Actual  degrees : 124  match word : Date  | distance : 0.6059831418219448\n", "###  394.2700  degrees : 95  match word : Date  | distance : 0.4379099233630271\n", "###  394.27  degrees : 75  match word : Date  | distance : 0.4366659341112094\n", "###  QTY 4  degrees : 131  match word : Date  | distance : 0.6154759619256871\n", "###  1.00  degrees : 145  match word : Date  | distance : 0.8577097352716516\n", "###  1.00  degrees : 143  match word : Date  | distance : 0.8022753866768935\n", "###  SW-001-4.0-AA17-2  degrees : 139  match word : Date  | distance : 0.7768087222285959\n", "###  5  degrees : 132  match word : Date  | distance : 0.6493847508532943\n", "###  Interaction Report Assistant Actual QTY  degrees : 122  match word : Date  | distance : 0.6334394891826661\n", "###  0.00  degrees : 92  match word : Date  | distance : 0.47129857238082046\n", "###  0.00  degrees : 76  match word : Date  | distance : 0.47674263115923265\n", "###  1  degrees : 130  match word : Date  | distance : 0.643228265781931\n", "###  1.00  degrees : 143  match word : Date  | distance : 0.8791288475559467\n", "###  1.00  degrees : 140  match word : Date  | distance : 0.825032417518042\n", "###  SW-001-4.0-AA01-2  degrees : 137  match word : Date  | distance : 0.8010415961724591\n", "###  6  degrees : 130  match word : Date  | distance : 0.6777045772913128\n", "###  Interaction Supervisor add-on Actual  degrees : 121  match word : Date  | distance : 0.6628596526492873\n", "###  76.06  degrees : 93  match word : Date  | distance : 0.5100035757788769\n", "###  76.06  degrees : 77  match word : Date  | distance : 0.5129856363142495\n", "###  QTY 1  degrees : 127  match word : Date  | distance : 0.6724841950201045\n", "###  1.00  degrees : 141  match word : Date  | distance : 0.9017502219745943\n", "###  1.00  degrees : 138  match word : Date  | distance : 0.849335810254466\n", "###  SW-001-4.0-PL09-2  degrees : 135  match word : Date  | distance : 0.8255775075824768\n", "###  7  degrees : 128  match word : Date  | distance : 0.7065578267770382\n", "###  Media Session Actual QTY 12  degrees : 120  match word : Date  | distance : 0.6912139506148488\n", "###  124.12  degrees : 93  match word : Date  | distance : 0.5486509288400007\n", "###  124.12  degrees : 78  match word : Date  | distance : 0.549996766906952\n", "###  START DATE  degrees : 122  match word : Date  | distance : 0.7047472657600974\n", "###  10/28/2020  degrees : 122  match word : Date  | distance : 0.7147631787373063\n", "###  END DATE  degrees : 122  match word : Date  | distance : 0.726237122337223\n", "###  6/30/2021  degrees : 121  match word : Date  | distance : 0.7371969912163098\n", "###  Dept of Administrative Services  degrees : 116  match word : Date  | distance : 0.7467056219596528\n", "###  Due Date  degrees : 120  match word : Date  | distance : 0.7703844886378589\n", "###  Amount Due  degrees : 108  match word : Date  | distance : 0.6984831657086172\n", "###  Nov 18, 2020  degrees : 119  match word : Date  | distance : 0.7831244551021487\n", "###  1,247.91  degrees : 109  match word : Date  | distance : 0.7122591844768458\n", "###  Currency in USD unless otherwise specified  degrees : 115  match word : Date  | distance : 0.9128447737662259\n", "###  Invoice continued on next page  degrees : 127  match word : Date  | distance : 1.0845388130572604\n", "*************dist list ****************\n", "{0.06472569246832355: ('c5c5b9dc-5d83-46fd-b5e5-03347f514b5c', 0), 0.04808050140061863: ('1f1fabae-0d0c-44ad-9ad5-72b35f288aac', 67), 0.08648879852219822: ('a2a92321-54e6-4a89-89c7-bad10b27c98a', 7), 0.03614218902389615: ('0887adf2-4073-4a57-b4e8-927fd5047b75', 34), 0.040324622187617336: ('3083bbc5-73c8-4c5d-b178-0d3bb2353787', 44)}\n", "Nearest matches  0.03614218902389615   34   Date   Invoice Number\n", "Nearest matches  0.040324622187617336   44   Date   IN859177\n", "Nearest matches  0.04808050140061863   67   Date   Oct 19, 2020\n", "Nearest matches  0.06472569246832355   0   Date   Page\n", "Nearest matches  0.08648879852219822   7   Date   1\n", "dst list  3.6142189023896147 34 Date Invoice Number conf  99.85523986816406\n", "*************************\n", "matches  [] length  0\n", "result  []\n", "dst list  4.032462218761734 44 Date IN859177 conf  99.79399108886719\n", "*************************\n", "matches  [] length  0\n", "result  []\n", "dst list  4.808050140061863 67 Date Oct 19, 2020 conf  99.**************\n", "*************************\n", "matches  [datetime.datetime(2020, 10, 19, 0, 0)] length  1\n", "M 2020-10-19 00:00:00\n", "result  [datetime.datetime(2020, 10, 19, 0, 0)]\n", "indices  0.04808050140061863 [datetime.datetime(2020, 10, 19, 0, 0)] 99.**************\n", "dst list  6.472569246832355 0 Date Page conf  99.13982391357422\n", "*************************\n", "matches  [] length  0\n", "result  []\n", "dst list  8.*************** 7 Date 1 conf  99.*************\n", "*************************\n", "matches  [] length  0\n", "result  []\n", "************* Issue_Date ********************\n", "match list  {2: ([datetime.datetime(2020, 10, 19, 0, 0)], 79.**************)}\n", "match list keys  [2]\n", "Issue_Date  :  [datetime.datetime(2020, 10, 19, 0, 0)] confidence : 79.**************\n", "top ....  0\n", "bottom......  0\n", "******************** bank_name ******************************\n", "word list \n", "['bank name', 'bank name name']\n", "Best Match Found  None\n", "top ....  0\n", "bottom......  0\n", "******************** account_number ******************************\n", "word list \n", "['account number']\n", "Best Match Found  None\n", "top ....  0\n", "bottom......  0\n", "******************** terms ******************************\n", "word list \n", "['terms ', 'terms ']\n", "--> terms terms 100\n", "Best Match Found  ('Terms', 0.****************, 0.****************, 0.****************, 0.****************, (0.****************, 0.****************), 99.**********)\n", "###  carahsoft.  degrees : -160  match word : Terms  | distance : 0.****************\n", "###  Date  degrees : -111  match word : Terms  | distance : 0.****************\n", "###  Page  degrees : -91  match word : Terms  | distance : 0.*****************\n", "###  Invoice  degrees : -148  match word : Terms  | distance : 0.****************\n", "###  Oct 19, 2020  degrees : -111  match word : Terms  | distance : 0.****************\n", "###  1  degrees : -89  match word : Terms  | distance : 0.*****************\n", "###  Carahsoft Technology Corp  degrees : -163  match word : Terms  | distance : 0.***********40191\n", "###  Invoice Number  degrees : -104  match word : Terms  | distance : 0.2604917584007672\n", "###  IN859177  degrees : -104  match word : Terms  | distance : 0.24151689530742063\n", "###  11493 Sunset Hills Road, Suite 100  degrees : -164  match word : Terms  | distance : 0.9034996671425474\n", "###  <PERSON><PERSON>, VA 20190  degrees : -166  match word : Terms  | distance : 0.****************\n", "###  USA  degrees : -167  match word : Terms  | distance : 0.****************\n", "###  Sold To:  degrees : -172  match word : Terms  | distance : 0.**************\n", "###  Ship To:  degrees : -160  match word : Terms  | distance : 0.*****************\n", "###  State of Utah  degrees : -173  match word : Terms  | distance : 0.****************\n", "###  State of Utah - Dept of Administrative Services  degrees : -153  match word : Terms  | distance : 0.****************\n", "###  Attn: Accounts Payable  degrees : -174  match word : Terms  | distance : 0.****************\n", "###  Attn: <PERSON>  degrees : -162  match word : Terms  | distance : 0.****************\n", "###  1 State Office Building, Flr 6  degrees : -174  match word : Terms  | distance : 0.****************\n", "###  1 State Office Building  degrees : -165  match word : Terms  | distance : 0.****************\n", "###  Salt Lake City, UT 84114  degrees : -175  match word : Terms  | distance : 0.****************\n", "###  Basement, B108  degrees : -168  match word : Terms  | distance : 0.****************\n", "###  Salt Lake City, UT 84114  degrees : -170  match word : Terms  | distance : 0.*****************\n", "###  PO Number  degrees : -179  match word : Terms  | distance : 0.****************\n", "###  Order Date  degrees : -179  match word : Terms  | distance : 0.****************\n", "###  Customer No.  degrees : -179  match word : Terms  | distance : 0.****************\n", "###  Salesperson  degrees : -179  match word : Terms  | distance : 0.4178250338335124\n", "###  Order No.  degrees : -179  match word : Terms  | distance : 0.2573642695487224\n", "###  Ship Via  degrees : -179  match word : Terms  | distance : 0.11457889659797305\n", "###  Terms  degrees : 0  match word : Terms  | distance : 0.03859710693359375\n", "###  PO210064496  degrees : 178  match word : Terms  | distance : 0.7898686474235103\n", "###  Oct 15, 2020  degrees : 178  match word : Terms  | distance : 0.6277525428439522\n", "###  UTA001  degrees : 178  match word : Terms  | distance : 0.5040696233888385\n", "###  BDRAKE  degrees : 177  match word : Terms  | distance : 0.4032731599889492\n", "###  20094500  degrees : 175  match word : Terms  | distance : 0.25336718567533906\n", "###  GROUND  degrees : 167  match word : Terms  | distance : 0.1169172752464188\n", "###  N30  degrees : 78  match word : Terms  | distance : 0.03187126212050131\n", "###  Qty.  degrees : 177  match word : Terms  | distance : 0.8463774054567994\n", "###  Qty.  degrees : 177  match word : Terms  | distance : 0.7797966093856238\n", "###  Ord.  degrees : 176  match word : Terms  | distance : 0.8474595663519396\n", "###  Shp.  degrees : 176  match word : Terms  | distance : 0.7822765245293888\n", "###  Item Number  degrees : 175  match word : Terms  | distance : 0.6967939383610695\n", "###  Line  degrees : 175  match word : Terms  | distance : 0.5704436920765937\n", "###  Description  degrees : 173  match word : Terms  | distance : 0.44917593934935957\n", "###  Unit Price  degrees : 166  match word : Terms  | distance : 0.23981063745783646\n", "###  Extended Price  degrees : 132  match word : Terms  | distance : 0.11683263748809022\n", "###  1.00  degrees : 174  match word : Terms  | distance : 0.8266775322837713\n", "###  1.00  degrees : 174  match word : Terms  | distance : 0.7596621975251483\n", "###  SW-001-4.0-PL04-2  degrees : 173  match word : Terms  | distance : 0.7295054440523043\n", "###  1  degrees : 171  match word : Terms  | distance : 0.558910214314804\n", "###  Advanced Session Actual QTY 10  degrees : 169  match word : Terms  | distance : 0.5361109474483776\n", "###  456.33  degrees : 150  match word : Terms  | distance : 0.18698057545623145\n", "###  456.33  degrees : 84  match word : Terms  | distance : 0.08206712239113809\n", "###  1.00  degrees : 172  match word : Terms  | distance : 0.830563209928285\n", "###  1.00  degrees : 171  match word : Terms  | distance : 0.7639659063687898\n", "###  SW-001-4.0-PL03-2  degrees : 170  match word : Terms  | distance : 0.732759504770042\n", "###  2  degrees : 168  match word : Terms  | distance : 0.5652973631869159\n"]}, {"name": "stdout", "output_type": "stream", "text": ["###  Basic Session Actual QTY 10  degrees : 165  match word : Terms  | distance : 0.5408510155851682\n", "###  182.53  degrees : 140  match word : Terms  | distance : 0.20106380846745966\n", "###  182.53  degrees : 85  match word : Terms  | distance : 0.11218654578789473\n", "###  1.00  degrees : 169  match word : Terms  | distance : 0.8353216205181375\n", "###  1.00  degrees : 169  match word : Terms  | distance : 0.7690050730902264\n", "###  SW-001-4.0-PL06-2  degrees : 167  match word : Terms  | distance : 0.736941799960095\n", "###  3  degrees : 165  match word : Terms  | distance : 0.5720981128209398\n", "###  Conference Session Actual QTY 2  degrees : 161  match word : Terms  | distance : 0.5484349391331033\n", "###  14.60  degrees : 132  match word : Terms  | distance : 0.21464684600655984\n", "###  14.60  degrees : 85  match word : Terms  | distance : 0.14245409466640396\n", "###  1.00  degrees : 167  match word : Terms  | distance : 0.8413243499949359\n", "###  1.00  degrees : 166  match word : Terms  | distance : 0.7757299741645681\n", "###  SW-001-4.0-AL04  degrees : 165  match word : Terms  | distance : 0.7452747122055587\n", "###  4  degrees : 161  match word : Terms  | distance : 0.5812273831882586\n", "###  Contact Center Level 1- Support Actual  degrees : 156  match word : Terms  | distance : 0.5580316877059798\n", "###  394.2700  degrees : 128  match word : Terms  | distance : 0.2532724602056524\n", "###  394.27  degrees : 87  match word : Terms  | distance : 0.17573333451631504\n", "###  QTY 4  degrees : 159  match word : Terms  | distance : 0.5623630432792979\n", "###  1.00  degrees : 165  match word : Terms  | distance : 0.8499985978189232\n", "###  1.00  degrees : 163  match word : Terms  | distance : 0.7850361058792807\n", "###  SW-001-4.0-AA17-2  degrees : 161  match word : Terms  | distance : 0.7547477875941268\n", "###  5  degrees : 158  match word : Terms  | distance : 0.5933609415980343\n", "###  Interaction Report Assistant Actual QTY  degrees : 151  match word : Terms  | distance : 0.5706394290812109\n", "###  0.00  degrees : 120  match word : Terms  | distance : 0.2622870578151733\n", "###  0.00  degrees : 85  match word : Terms  | distance : 0.2115319250663053\n", "###  1  degrees : 156  match word : Terms  | distance : 0.5754283643944554\n", "###  1.00  degrees : 162  match word : Terms  | distance : 0.8601628165956127\n", "###  1.00  degrees : 161  match word : Terms  | distance : 0.7959916024726382\n", "###  SW-001-4.0-AA01-2  degrees : 158  match word : Terms  | distance : 0.7669741075151602\n", "###  6  degrees : 154  match word : Terms  | distance : 0.6081658188078767\n", "###  Interaction Supervisor add-on Actual  degrees : 148  match word : Terms  | distance : 0.5866551369467634\n", "###  76.06  degrees : 117  match word : Terms  | distance : 0.2978702615006533\n", "###  76.06  degrees : 87  match word : Terms  | distance : 0.25015003753049186\n", "###  QTY 1  degrees : 151  match word : Terms  | distance : 0.5918972637518318\n", "###  1.00  degrees : 160  match word : Terms  | distance : 0.8722163775800207\n", "###  1.00  degrees : 158  match word : Terms  | distance : 0.8091536705853191\n", "###  SW-001-4.0-PL09-2  degrees : 156  match word : Terms  | distance : 0.7800238586679024\n", "###  7  degrees : 151  match word : Terms  | distance : 0.6246900808825474\n", "###  Media Session Actual QTY 12  degrees : 145  match word : Terms  | distance : 0.6030359987441845\n", "###  124.12  degrees : 114  match word : Terms  | distance : 0.33354943604640047\n", "###  124.12  degrees : 88  match word : Terms  | distance : 0.2889031985895069\n", "###  START DATE  degrees : 146  match word : Terms  | distance : 0.6081034723654095\n", "###  10/28/2020  degrees : 145  match word : Terms  | distance : 0.6140446263162648\n", "###  END DATE  degrees : 144  match word : Terms  | distance : 0.621908599587705\n", "###  6/30/2021  degrees : 143  match word : Terms  | distance : 0.6293279161207762\n", "###  Dept of Administrative Services  degrees : 138  match word : Terms  | distance : 0.6356509123755161\n", "###  Due Date  degrees : 141  match word : Terms  | distance : 0.6534938683835065\n", "###  Amount Due  degrees : 129  match word : Terms  | distance : 0.5376424283861604\n", "###  Nov 18, 2020  degrees : 139  match word : Terms  | distance : 0.662625398898882\n", "###  1,247.91  degrees : 129  match word : Terms  | distance : 0.5480099004699432\n", "###  Currency in USD unless otherwise specified  degrees : 133  match word : Terms  | distance : 0.7939359415995721\n", "###  Invoice continued on next page  degrees : 143  match word : Terms  | distance : 1.000659480474292\n", "*************dist list ****************\n", "{0.03187126212050131: ('753c6f73-4801-400f-ba46-a49e7b58884d', 78), 0.08206712239113809: ('a69e182d-21bf-4f9d-aa6c-477fba43f018', 84), 0.11218654578789473: ('75e935bf-0f71-4343-81a6-dad884701e34', 85), 0.14245409466640396: ('3a256240-422c-4a48-835e-324dae4e7ab9', 85), 0.17573333451631504: ('ccc32603-9acb-4e10-aecf-0596af433f37', 87), 0.2115319250663053: ('6edd1b5b-f3ac-4b6d-98ee-784ef22b1fe0', 85)}\n", "Nearest matches  0.03187126212050131   78   Terms   N30\n", "Nearest matches  0.08206712239113809   84   Terms   456.33\n", "Nearest matches  0.11218654578789473   85   Terms   182.53\n", "Nearest matches  0.14245409466640396   85   Terms   14.60\n", "Nearest matches  0.17573333451631504   87   Terms   394.27\n", "Nearest matches  0.2115319250663053   85   Terms   0.00\n", "dst list  3.187126212050131 78 Terms N30 conf  99.33849334716797\n", "*************************\n", "regex match  N30\n", "indices  0.03187126212050131 ['N30'] 99.33849334716797\n", "dst list  8.20671223911381 84 Terms 456.33 conf  98.92430114746094\n", "*************************\n", "regex match  456\n", "indices  0.08206712239113809 ['456'] 98.92430114746094\n", "dst list  11.218654578789474 85 Terms 182.53 conf  99.36373901367188\n", "*************************\n", "regex match  182\n", "indices  0.11218654578789473 ['182'] 99.36373901367188\n", "dst list  14.245409466640396 85 Terms 14.60 conf  99.76837158203125\n", "*************************\n", "regex match  14\n", "indices  0.14245409466640396 ['14'] 99.76837158203125\n", "dst list  17.573333451631505 87 Terms 394.27 conf  98.68486022949219\n", "*************************\n", "regex match  394\n", "indices  0.17573333451631504 ['394'] 98.68486022949219\n", "dst list  21.153192506630532 85 Terms 0.00 conf  99.56881713867188\n", "*************************\n", "regex match  00\n", "indices  0.2115319250663053 ['00'] 99.56881713867188\n", "************* terms ********************\n", "match list  {0: (['N30'], 99.33849334716797), 1: (['456'], 88.92430114746094), 2: (['182'], 79.36373901367188), 3: (['14'], 69.76837158203125), 4: (['394'], 58.68486022949219), 5: (['00'], 49.568817138671875)}\n", "match list keys  [0, 1, 2, 3, 4, 5]\n", "terms  :  ['N30'] confidence : 99.33849334716797\n", "top ....  0\n", "bottom......  0\n", "******************** ship_to ******************************\n", "word list \n", "['ship to ', 'ship to to', 'ship ', 'ship to']\n", "--> ship to: ship to 93\n", "Best Match Found  ('Ship To:', 0.5954750180244446, 0.19282667338848114, 0.6464519500732422, 0.19282667338848114, (0.6209634840488434, 0.19282667338848114), 99.87454986572266)\n", "###  carahsoft.  degrees : -159  match word : Ship To:  | distance : 0.6257567068295266\n", "###  Date  degrees : -41  match word : Ship To:  | distance : 0.21157775141921734\n", "###  Page  degrees : -30  match word : Ship To:  | distance : 0.28148824071553835\n", "###  Invoice  degrees : -130  match word : Ship To:  | distance : 0.24062140761285658\n", "###  Oct 19, 2020  degrees : -38  match word : Ship To:  | distance : 0.18959434649799967\n", "###  1  degrees : -27  match word : Ship To:  | distance : 0.29176798559544\n", "###  Carahsoft Technology Corp  degrees : -165  match word : Ship To:  | distance : 0.6202678036295856\n", "###  Invoice Number  degrees : -30  match word : Ship To:  | distance : 0.19586624852983472\n", "###  IN859177  degrees : -28  match word : Ship To:  | distance : 0.2051897538840326\n", "###  11493 Sunset Hills Road, Suite 100  degrees : -166  match word : Ship To:  | distance : 0.6170103863780035\n", "###  <PERSON><PERSON>, VA 20190  degrees : -169  match word : Ship To:  | distance : 0.6157296939547287\n", "###  USA  degrees : -171  match word : Ship To:  | distance : 0.****************\n", "###  Sold To:  degrees : -179  match word : Ship To:  | distance : 0.****************\n", "###  Ship To:  degrees : 0  match word : Ship To:  | distance : 0.*****************\n", "###  State of Utah  degrees : 178  match word : Ship To:  | distance : 0.****************\n", "###  State of Utah - Dept of Administrative Services  degrees : 6  match word : Ship To:  | distance : 0.050334851059761605\n", "###  Attn: Accounts Payable  degrees : 176  match word : Ship To:  | distance : 0.****************\n", "###  Attn: <PERSON>  degrees : 32  match word : Ship To:  | distance : 0.*****************\n", "###  1 State Office Building, Flr 6  degrees : 174  match word : Ship To:  | distance : 0.****************\n", "###  1 State Office Building  degrees : 40  match word : Ship To:  | distance : 0.059973452715623585\n", "###  Salt Lake City, UT 84114  degrees : 173  match word : Ship To:  | distance : 0.****************\n", "###  Basement, B108  degrees : 61  match word : Ship To:  | distance : 0.*****************\n", "###  Salt Lake City, UT 84114  degrees : 49  match word : Ship To:  | distance : 0.*****************\n", "###  PO Number  degrees : 167  match word : Ship To:  | distance : 0.****************\n", "###  Order Date  degrees : 161  match word : Ship To:  | distance : 0.*****************\n", "###  Customer No.  degrees : 152  match word : Ship To:  | distance : 0.*****************\n", "###  Salesperson  degrees : 129  match word : Ship To:  | distance : 0.17551684801645626\n", "###  Order No.  degrees : 54  match word : Ship To:  | distance : 0.09877861521133235\n", "###  Ship Via  degrees : 25  match word : Ship To:  | distance : 0.18524116637826216\n", "###  Terms  degrees : 19  match word : Ship To:  | distance : 0.2530735848273773\n", "###  PO210064496  degrees : 165  match word : Ship To:  | distance : 0.5298385452688129\n", "###  Oct 15, 2020  degrees : 159  match word : Ship To:  | distance : 0.3729012386534917\n", "###  UTA001  degrees : 148  match word : Ship To:  | distance : 0.25769081688726525\n", "###  BDRAKE  degrees : 125  match word : Ship To:  | distance : 0.17270600492909718\n", "###  20094500  degrees : 56  match word : Ship To:  | distance : 0.11417302567991454\n", "###  GROUND  degrees : 28  match word : Ship To:  | distance : 0.1922261367257857\n", "###  N30  degrees : 21  match word : Ship To:  | distance : 0.268297785048104\n", "###  Qty.  degrees : 166  match word : Ship To:  | distance : 0.5884362740206774\n", "###  Qty.  degrees : 164  match word : Ship To:  | distance : 0.5236644115265764\n", "###  Ord.  degrees : 164  match word : Ship To:  | distance : 0.5919097274542366\n", "###  Shp.  degrees : 162  match word : Ship To:  | distance : 0.5288344631942103\n", "###  Item Number  degrees : 158  match word : Ship To:  | distance : 0.4468769483370564\n", "###  Line  degrees : 150  match word : Ship To:  | distance : 0.32944151029420554\n", "###  Description  degrees : 128  match word : Ship To:  | distance : 0.22606928880099136\n", "###  Unit Price  degrees : 57  match word : Ship To:  | distance : 0.1476625916533462\n", "###  Extended Price  degrees : 31  match word : Ship To:  | distance : 0.21792961545680595\n", "###  1.00  degrees : 161  match word : Ship To:  | distance : 0.5778704704229722\n", "###  1.00  degrees : 158  match word : Ship To:  | distance : 0.5140272269441652\n", "###  SW-001-4.0-PL04-2  degrees : 154  match word : Ship To:  | distance : 0.4855959116365725\n", "###  1  degrees : 145  match word : Ship To:  | distance : 0.3309808290814785\n", "###  Advanced Session Actual QTY 10  degrees : 127  match word : Ship To:  | distance : 0.31148449344245766\n", "###  456.33  degrees : 49  match word : Ship To:  | distance : 0.20064590886081723\n", "###  456.33  degrees : 31  match word : Ship To:  | distance : 0.29587342481467904\n", "###  1.00  degrees : 158  match word : Ship To:  | distance : 0.5887157581532121\n", "###  1.00  degrees : 155  match word : Ship To:  | distance : 0.5262622898153135\n", "###  SW-001-4.0-PL03-2  degrees : 150  match word : Ship To:  | distance : 0.4973582394741814\n", "###  2  degrees : 140  match word : Ship To:  | distance : 0.34995130916634215\n", "###  Basic Session Actual QTY 10  degrees : 125  match word : Ship To:  | distance : 0.3300218782900722\n", "###  182.53  degrees : 54  match word : Ship To:  | distance : 0.22993798543976743\n", "###  182.53  degrees : 35  match word : Ship To:  | distance : 0.3172094652970993\n", "###  1.00  degrees : 155  match word : Ship To:  | distance : 0.6007383069013974\n", "###  1.00  degrees : 151  match word : Ship To:  | distance : 0.5395909461575207\n", "###  SW-001-4.0-PL06-2  degrees : 147  match word : Ship To:  | distance : 0.5104814658815323\n", "###  3  degrees : 136  match word : Ship To:  | distance : 0.369727994109544\n", "###  Conference Session Actual QTY 2  degrees : 119  match word : Ship To:  | distance : 0.3513224343768531\n", "###  14.60  degrees : 57  match word : Ship To:  | distance : 0.2623653667349036\n", "###  14.60  degrees : 39  match word : Ship To:  | distance : 0.34420093124231715\n", "###  1.00  degrees : 152  match word : Ship To:  | distance : 0.6143105385212038\n", "###  1.00  degrees : 148  match word : Ship To:  | distance : 0.5548303258552656\n", "###  SW-001-4.0-AL04  degrees : 144  match word : Ship To:  | distance : 0.527695205461723\n", "###  4  degrees : 133  match word : Ship To:  | distance : 0.3917176097926824\n", "###  Contact Center Level 1- Support Actual  degrees : 113  match word : Ship To:  | distance : 0.3749485379819318\n", "###  394.2700  degrees : 62  match word : Ship To:  | distance : 0.28421528149456726\n", "###  394.27  degrees : 43  match word : Ship To:  | distance : 0.3617532501655152\n", "###  QTY 4  degrees : 126  match word : Ship To:  | distance : 0.38464225420193\n", "###  1.00  degrees : 149  match word : Ship To:  | distance : 0.631935419273925\n", "###  1.00  degrees : 145  match word : Ship To:  | distance : 0.5742391788531167\n", "###  SW-001-4.0-AA17-2  degrees : 140  match word : Ship To:  | distance : 0.5478403475886003\n", "###  5  degrees : 129  match word : Ship To:  | distance : 0.41850951125188474\n", "###  Interaction Report Assistant Actual QTY  degrees : 110  match word : Ship To:  | distance : 0.403194282451556\n", "###  0.00  degrees : 63  match word : Ship To:  | distance : 0.3297055164445536\n", "###  0.00  degrees : 46  match word : Ship To:  | distance : 0.40036887009885125\n", "###  1  degrees : 125  match word : Ship To:  | distance : 0.4133882557508719\n", "###  1.00  degrees : 145  match word : Ship To:  | distance : 0.6514025553895988\n", "###  1.00  degrees : 142  match word : Ship To:  | distance : 0.595507055066846\n", "###  SW-001-4.0-AA01-2  degrees : 136  match word : Ship To:  | distance : 0.5708533460950899\n", "###  6  degrees : 126  match word : Ship To:  | distance : 0.44767161647023634\n", "###  Interaction Supervisor add-on Actual  degrees : 110  match word : Ship To:  | distance : 0.43381522467824196\n", "###  76.06  degrees : 66  match word : Ship To:  | distance : 0.3632892901583827\n", "###  76.06  degrees : 50  match word : Ship To:  | distance : 0.42597439876986837\n", "###  QTY 1  degrees : 120  match word : Ship To:  | distance : 0.44393900052337604\n", "###  1.00  degrees : 143  match word : Ship To:  | distance : 0.6726064455305348\n", "###  1.00  degrees : 139  match word : Ship To:  | distance : 0.6188515906968652\n", "###  SW-001-4.0-PL09-2  degrees : 133  match word : Ship To:  | distance : 0.5946755166526958\n", "###  7  degrees : 123  match word : Ship To:  | distance : 0.47771199621101434\n", "###  Media Session Actual QTY 12  degrees : 110  match word : Ship To:  | distance : 0.46362400174668716\n", "###  124.12  degrees : 69  match word : Ship To:  | distance : 0.3982705292792644\n", "###  124.12  degrees : 53  match word : Ship To:  | distance : 0.45439709449059573\n", "###  START DATE  degrees : 114  match word : Ship To:  | distance : 0.47855330591134077\n", "###  10/28/2020  degrees : 114  match word : Ship To:  | distance : 0.48924961954388324\n", "###  END DATE  degrees : 114  match word : Ship To:  | distance : 0.5013105595288112\n", "###  6/30/2021  degrees : 113  match word : Ship To:  | distance : 0.5128871831926376\n", "###  Dept of Administrative Services  degrees : 106  match word : Ship To:  | distance : 0.5229887703144842\n", "###  Due Date  degrees : 112  match word : Ship To:  | distance : 0.5476924595884589\n", "###  Amount Due  degrees : 94  match word : Ship To:  | distance : 0.49558068586966414\n", "###  Nov 18, 2020  degrees : 110  match word : Ship To:  | distance : 0.5611579781871385\n", "###  1,247.91  degrees : 95  match word : Ship To:  | distance : 0.5104108109102795\n", "###  Currency in USD unless otherwise specified  degrees : 107  match word : Ship To:  | distance : 0.6882831667472248\n", "###  Invoice continued on next page  degrees : 124  match word : Ship To:  | distance : 0.8537795556283697\n", "*************dist list ****************\n", "{0.050334851059761605: ('6d682bfe-2025-4652-8e3c-fb0d7961e455', 6), 0.*****************: ('3d961272-2ea6-4b92-a5c6-c2fd8361acab', 32), 0.059973452715623585: ('6c67be00-08ea-4047-b741-4b04e387e93b', 40), 0.*****************: ('64b5d491-1855-4d14-a210-e7ec570eca36', 61), 0.*****************: ('7d81da64-4574-4303-ae61-d5a6bacb9a2f', 49), 0.09877861521133235: ('73fdd756-0c54-46c1-b683-e5cb7d1c133a', 54), 0.18524116637826216: ('6b96fc7b-0162-45ff-80db-d89e4f88cdac', 25), 0.11417302567991454: ('71fadd80-9136-4dfa-81db-70c81aaa6a03', 56), 0.1922261367257857: ('681165d5-7cbd-4b03-b62a-95312bb09d88', 28), 0.1476625916533462: ('14c99f7e-c092-4f94-b475-5be7163dfe1c', 57), 0.21792961545680595: ('aaa88cf4-1b1a-4b98-98f7-f2a021fa3e7a', 31), 0.20064590886081723: ('4aa68453-119b-49fe-a607-f0a665480a25', 49), 0.22993798543976743: ('c1104834-bbbc-466b-9456-d93af90d2ec0', 54)}\n", "Nearest matches  0.050334851059761605   6   Ship To:   State of Utah - Dept of Administrative Services\n", "Nearest matches  0.*****************   32   Ship To:   Attn: <PERSON>\n", "Nearest matches  0.059973452715623585   40   Ship To:   1 State Office Building\n", "Nearest matches  0.*****************   61   Ship To:   Basement, B108\n", "Nearest matches  0.*****************   49   Ship To:   Salt Lake City, UT 84114\n", "Nearest matches  0.09877861521133235   54   Ship To:   Order No.\n", "Nearest matches  0.11417302567991454   56   Ship To:   20094500\n", "Nearest matches  0.1476625916533462   57   Ship To:   Unit Price\n", "Nearest matches  0.18524116637826216   25   Ship To:   Ship Via\n", "Nearest matches  0.1922261367257857   28   Ship To:   GROUND\n", "Nearest matches  0.20064590886081723   49   Ship To:   456.33\n", "Nearest matches  0.21792961545680595   31   Ship To:   Extended Price\n", "Nearest matches  0.22993798543976743   54   Ship To:   182.53\n", "dst list  5.033485105976161 6 Ship To: State of Utah - Dept of Administrative Services conf  99.22742462158203\n", "*************************\n", "regex match  State of Utah - Dept of Administrative Services\n", "indices  {'id': 6, 'name': 'string', 'description': '', 'regex': '[a-zA-Z0-9].*'} ['State of Utah - Dept of Administrative Services'] 99.22742462158203\n", "dst list  5.486954790459588 32 Ship To: Attn: <PERSON> conf  99.8255386352539\n", "*************************\n", "regex match  Attn: <PERSON>\n", "indices  {'id': 6, 'name': 'string', 'description': '', 'regex': '[a-zA-Z0-9].*'} ['Attn: <PERSON>'] 99.8255386352539\n", "dst list  5.9973452715623585 40 Ship To: 1 State Office Building conf  99.85684967041016\n", "*************************\n", "regex match  1 State Office Building\n", "indices  {'id': 6, 'name': 'string', 'description': '', 'regex': '[a-zA-Z0-9].*'} ['1 State Office Building'] 99.85684967041016\n", "dst list  6.762886132937587 61 Ship To: Basement, B108 conf  98.89552307128906\n", "*************************\n", "regex match  Basement B108\n", "indices  {'id': 6, 'name': 'string', 'description': '', 'regex': '[a-zA-Z0-9].*'} ['Basement B108'] 98.89552307128906\n", "dst list  7.567829069381295 49 Ship To: Salt Lake City, UT 84114 conf  99.66554260253906\n", "*************************\n", "regex match  Salt Lake City UT 84114\n", "indices  {'id': 6, 'name': 'string', 'description': '', 'regex': '[a-zA-Z0-9].*'} ['Salt Lake City UT 84114'] 99.66554260253906\n", "dst list  9.877861521133235 54 Ship To: Order No. conf  99.70960998535156\n", "*************************\n", "regex match  Order No.\n", "indices  {'id': 6, 'name': 'string', 'description': '', 'regex': '[a-zA-Z0-9].*'} ['Order No.'] 99.70960998535156\n", "dst list  11.417302567991454 56 Ship To: 20094500 conf  99.35338592529297\n", "*************************\n", "regex match  20094500\n", "indices  {'id': 6, 'name': 'string', 'description': '', 'regex': '[a-zA-Z0-9].*'} ['20094500'] 99.35338592529297\n", "dst list  14.766259165334619 57 Ship To: Unit Price conf  99.6187973022461\n", "*************************\n", "regex match  Unit Price\n", "indices  {'id': 6, 'name': 'string', 'description': '', 'regex': '[a-zA-Z0-9].*'} ['Unit Price'] 99.6187973022461\n", "dst list  18.524116637826214 25 Ship To: Ship Via conf  99.74624633789062\n", "*************************\n", "regex match  Ship Via\n", "indices  {'id': 6, 'name': 'string', 'description': '', 'regex': '[a-zA-Z0-9].*'} ['Ship Via'] 99.74624633789062\n", "dst list  19.22261367257857 28 Ship To: GROUND conf  99.863037109375\n", "*************************\n", "regex match  GROUND\n", "indices  {'id': 6, 'name': 'string', 'description': '', 'regex': '[a-zA-Z0-9].*'} ['GROUND'] 99.863037109375\n", "************* ship_to ********************\n", "match list  {0: (['State of Utah - Dept of Administrative Services'], 99.22742462158203), 1: (['Attn: <PERSON>'], 89.8255386352539), 2: (['1 State Office Building'], 79.85684967041016), 3: (['Basement B108'], 68.89552307128906), 4: (['Salt Lake City UT 84114'], 59.66554260253906), 5: (['Order No.'], 49.70960998535156), 6: (['20094500'], 39.35338592529297), 7: (['Unit Price'], 29.618797302246094), 8: (['Ship Via'], 19.746246337890625), 9: (['GROUND'], 9.863037109375)}\n", "match list keys  [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]\n", "ship_to  :  ['State of Utah - Dept of Administrative Services'] confidence : 99.22742462158203\n", "top ....  0\n", "bottom......  0\n", "******************** bill_to ******************************\n", "word list \n", "['bill to ', 'bill to to', 'bill ', 'bill to', 'Billing Address ', 'Billing Address to']\n", "Best Match Found  None\n", "top ....  0\n", "bottom......  0\n", "******************** balance_due ******************************\n", "word list \n", "['balance due']\n", "Best Match Found  None\n", "JSON File Written ...\n", "1\n"]}, {"name": "stdout", "output_type": "stream", "text": ["{'I': 0.98397344, 'NI': 0.016026499}\n", "fetching Boto Response .....\n", "\n", "\n", "== FOUND KEY : VALUE pairs ===\n", "\n", "[1, 2, 3, 4, 5, 6, 7, 8, 17, 18, 19, 20, 21]\n", "regex_id  [1, 2, 3, 4]\n", "regex_id  [6]\n", "regex_id  [6]\n", "top ....  0\n", "bottom......  0\n", "******************** Invoice_No ******************************\n", "word list \n", "['invoice no', 'invoice no.', 'invoice id', 'invoice #', 'invoice number', 'invoice: no', 'invoice: no.', 'invoice: id', 'invoice: #', 'invoice: number', 'Invoice ID no', 'Invoice ID no.', 'Invoice ID id', 'Invoice ID #', 'Invoice ID number', ' no', ' no.', ' id', ' #', ' number']\n", "--> invoice invoice # 88\n", "--> invoice number invoice number 100\n", "Best Match Found  ('Invoice Number', 0.7927899956703186, 0.06252347677946091, 0.8918015956878662, 0.06252347677946091, (0.8422957956790924, 0.06252347677946091), 99.86193084716797)\n", "###  carahsoft.  degrees : -176  match word : Invoice Number  | distance : 0.8487358454095156\n", "###  Date  degrees : -145  match word : Invoice Number  | distance : 0.11078608172122806\n", "###  Page  degrees : -29  match word : Invoice Number  | distance : 0.03248416341379966\n", "###  Invoice  degrees : -176  match word : Invoice Number  | distance : 0.4337770643376421\n", "###  Oct 19, 2020  degrees : -157  match word : Invoice Number  | distance : 0.12499557036578943\n", "###  1  degrees : -14  match word : Invoice Number  | distance : 0.01662156434277582\n", "###  Carahsoft Technology Corp  degrees : 179  match word : Invoice Number  | distance : 0.8521171664148924\n", "###  Invoice Number  degrees : 0  match word : Invoice Number  | distance : 0.09901160001754761\n", "###  IN859177  degrees : 94  match word : Invoice Number  | distance : 0.*****************\n", "###  11493 Sunset Hills Road, Suite 100  degrees : 178  match word : Invoice Number  | distance : 0.8521355159382021\n", "###  <PERSON><PERSON>, VA 20190  degrees : 177  match word : Invoice Number  | distance : 0.8535080687568773\n", "###  USA  degrees : 176  match word : Invoice Number  | distance : 0.****************\n", "###  Sold To:  degrees : 169  match word : Invoice Number  | distance : 0.****************\n", "###  Ship To:  degrees : 149  match word : Invoice Number  | distance : 0.****************\n", "###  State of Utah  degrees : 168  match word : Invoice Number  | distance : 0.****************\n", "###  State of Utah - Dept of Administrative Services  degrees : 127  match word : Invoice Number  | distance : 0.*****************\n", "###  Attn: Accounts Payable  degrees : 166  match word : Invoice Number  | distance : 0.****************\n", "###  Attn: <PERSON>  degrees : 139  match word : Invoice Number  | distance : 0.****************\n", "###  1 State Office Building, Flr 6  degrees : 165  match word : Invoice Number  | distance : 0.****************\n", "###  1 State Office Building  degrees : 137  match word : Invoice Number  | distance : 0.***************\n", "###  Salt Lake City, UT 84114  degrees : 164  match word : Invoice Number  | distance : 0.****************\n", "###  Basement, B108  degrees : 137  match word : Invoice Number  | distance : 0.****************\n", "###  Salt Lake City, UT 84114  degrees : 132  match word : Invoice Number  | distance : 0.*****************\n", "###  PO Number  degrees : 161  match word : Invoice Number  | distance : 0.****************\n", "###  Order Date  degrees : 156  match word : Invoice Number  | distance : 0.***************\n", "###  Customer No.  degrees : 150  match word : Invoice Number  | distance : 0.550586770796766\n", "###  Salesperson  degrees : 143  match word : Invoice Number  | distance : 0.45272296729996825\n", "###  Order No.  degrees : 123  match word : Invoice Number  | distance : 0.3243708736932406\n", "###  Ship Via  degrees : 93  match word : Invoice Number  | distance : 0.*****************\n", "###  Terms  degrees : 76  match word : Invoice Number  | distance : 0.****************\n", "###  PO210064496  degrees : 160  match word : Invoice Number  | distance : 0.7986339455694835\n", "###  Oct 15, 2020  degrees : 154  match word : Invoice Number  | distance : 0.6474364596506259\n", "###  UTA001  degrees : 149  match word : Invoice Number  | distance : 0.5355148252554928\n", "###  BDRAKE  degrees : 140  match word : Invoice Number  | distance : 0.4457727659422619\n", "###  20094500  degrees : 121  match word : Invoice Number  | distance : 0.33198782619901557\n", "###  GROUND  degrees : 92  match word : Invoice Number  | distance : 0.25851788660478814\n", "###  N30  degrees : 75  match word : Invoice Number  | distance : 0.*****************\n", "###  Qty.  degrees : 160  match word : Invoice Number  | distance : 0.8596955759561721\n", "###  Qty.  degrees : 159  match word : Invoice Number  | distance : 0.7965265133142143\n", "###  Ord.  degrees : 160  match word : Invoice Number  | distance : 0.864265485575741\n", "###  Shp.  degrees : 158  match word : Invoice Number  | distance : 0.802521337781075\n", "###  Item Number  degrees : 154  match word : Invoice Number  | distance : 0.7224272286367242\n", "###  Line  degrees : 150  match word : Invoice Number  | distance : 0.6071464159900005\n", "###  Description  degrees : 140  match word : Invoice Number  | distance : 0.5014669660911967\n", "###  Unit Price  degrees : 115  match word : Invoice Number  | distance : 0.3439806967807411\n", "###  Extended Price  degrees : 86  match word : Invoice Number  | distance : 0.2852239928419995\n", "###  Remit To:  degrees : 136  match word : Invoice Number  | distance : 1.1008417980261513\n", "###  Currency in USD unless otherwise specified  degrees : 119  match word : Invoice Number  | distance : 0.9361662787504537\n", "###  Carahsoft Technology Corporation  degrees : 132  match word : Invoice Number  | distance : 1.1097017672470102\n", "###  Subtotal  degrees : 101  match word : Invoice Number  | distance : 0.7669956633250209\n", "###  1,247.91  degrees : 85  match word : Invoice Number  | distance : 0.7324235247829853\n", "###  11493 Sunset Hills Road, Suite 100  degrees : 132  match word : Invoice Number  | distance : 1.117744092105339\n", "###  Total sales tax  degrees : 99  match word : Invoice Number  | distance : 0.7815958784808598\n", "###  0.00  degrees : 84  match word : Invoice Number  | distance : 0.747228448023121\n", "###  <PERSON><PERSON>, VA 20190  degrees : 133  match word : Invoice Number  | distance : 1.127420343898406\n", "###  FEIN 52-2189693 DUNS *********  degrees : 130  match word : Invoice Number  | distance : 1.1363629251410092\n", "###  Total amount  degrees : 99  match word : Invoice Number  | distance : 0.***************\n", "###  1,247.91  degrees : 85  match word : Invoice Number  | distance : 0.****************\n", "###  CA Sales Tax # SC OHB **********  degrees : 130  match word : Invoice Number  | distance : 1.****************\n", "###  Less payment  degrees : 99  match word : Invoice Number  | distance : 0.****************\n", "###  0.00  degrees : 84  match word : Invoice Number  | distance : 0.****************\n", "###  For questions on this invoice, please contact Accounts  degrees : 127  match word : Invoice Number  | distance : 1.****************\n", "###  Receivable: <EMAIL>, Ph: 703-581-6566  degrees : 127  match word : Invoice Number  | distance : 1.***************\n", "###  Amount due  degrees : 99  match word : Invoice Number  | distance : 0.****************\n", "###  1,247.91  degrees : 86  match word : Invoice Number  | distance : 0.****************\n", "###  Fax: 703-871-8505  degrees : 130  match word : Invoice Number  | distance : 1.****************\n", "*************dist list ****************\n", "{0.*****************: ('4a31f2b7-1a2c-47fe-841b-e6f805d675c0', 94), 0.*****************: ('eecf71e3-5b20-40a5-8d8d-317e0a20493b', 93), 0.****************: ('d183913b-e54d-460e-bc4e-638b253fc369', 76), 0.*****************: ('e1c43ce6-d85e-40be-b15d-55b111e17fb3', 75)}\n", "Nearest matches  0.*****************   94   Invoice Number   IN859177\n", "Nearest matches  0.****************   76   Invoice Number   Terms\n", "Nearest matches  0.*****************   75   Invoice Number   N30\n", "Nearest matches  0.*****************   93   Invoice Number   Ship Via\n", "dst list  7.905996910218612 94 Invoice Number IN859177 conf  99.86270904541016\n", "*************************\n", "regex match  IN859177\n", "indices  0.***************** ['IN859177'] 99.86270904541016\n", "dst list  22.84831820749349 76 Invoice Number Terms conf  99.**************\n", "*************************\n", "dst list  24.266150652619135 75 Invoice Number N30 conf  99.************22\n", "*************************\n", "regex match  N30\n", "indices  0.***************** ['N30'] 99.************22\n", "dst list  24.433963177714286 93 Invoice Number Ship Via conf  99.76663208007812\n", "*************************\n", "************* Invoice_No ********************\n", "match list  {0: (['IN859177'], 99.86270904541016), 2: (['N30'], 79.************22)}\n", "match list keys  [0, 2]\n", "Invoice_No  :  ['IN859177'] confidence : 99.86270904541016\n", "top ....  0\n", "bottom......  0\n", "******************** PO_no ******************************\n", "word list \n", "['po no', 'po number', 'po order', 'po ', 'purchase no', 'purchase number', 'purchase order', 'purchase ', 'P.O. no', 'P.O. number', 'P.O. order', 'P.O. ', ' no', ' number', ' order', ' ']\n", "--> po number po number 100\n", "Best Match Found  ('PO Number', 0.13119304180145264, 0.2906184494495392, 0.20427244901657104, 0.2906184494495392, (0.16773274540901184, 0.2906184494495392), 99.78492736816406)\n", "###  carahsoft.  degrees : -90  match word : PO Number  | distance : 0.31125787360025803\n", "###  Date  degrees : -22  match word : PO Number  | distance : 0.6353607577802346\n", "###  Page  degrees : -19  match word : PO Number  | distance : 0.7214538142516927\n", "###  Invoice  degrees : -37  match word : PO Number  | distance : 0.35515093591621855\n", "###  Oct 19, 2020  degrees : -20  match word : PO Number  | distance : 0.6137784017288506\n", "###  1  degrees : -18  match word : PO Number  | distance : 0.7359395417235793\n", "###  Carahsoft Technology Corp  degrees : -100  match word : PO Number  | distance : 0.2799880272997971\n", "###  Invoice Number  degrees : -18  match word : PO Number  | distance : 0.6311736839228246\n", "###  IN859177  degrees : -17  match word : PO Number  | distance : 0.646784242672168\n", "###  11493 Sunset Hills Road, Suite 100  degrees : -96  match word : PO Number  | distance : 0.2672021803607459\n", "###  <PERSON><PERSON>, VA 20190  degrees : -111  match word : PO Number  | distance : 0.25760300935923547\n", "###  USA  degrees : -122  match word : PO Number  | distance : 0.*****************\n", "###  Sold To:  degrees : -100  match word : PO Number  | distance : 0.*****************\n", "###  Ship To:  degrees : -12  match word : PO Number  | distance : 0.***************\n", "###  State of Utah  degrees : -91  match word : PO Number  | distance : 0.****************\n", "###  State of Utah - Dept of Administrative Services  degrees : -8  match word : PO Number  | distance : 0.***************\n", "###  Attn: Accounts Payable  degrees : -70  match word : PO Number  | distance : 0.*****************\n", "###  Attn: <PERSON>  degrees : -8  match word : PO Number  | distance : 0.*****************\n", "###  1 State Office Building, Flr 6  degrees : -56  match word : PO Number  | distance : 0.*****************\n", "###  1 State Office Building  degrees : -7  match word : PO Number  | distance : 0.****************\n", "###  Salt Lake City, UT 84114  degrees : -58  match word : PO Number  | distance : 0.****************\n", "###  Basement, B108  degrees : -5  match word : PO Number  | distance : 0.****************\n", "###  Salt Lake City, UT 84114  degrees : -4  match word : PO Number  | distance : 0.*****************\n", "###  PO Number  degrees : 0  match word : PO Number  | distance : 0.*****************\n", "###  Order Date  degrees : 0  match word : PO Number  | distance : 0.*****************\n", "###  Customer No.  degrees : 0  match word : PO Number  | distance : 0.****************\n", "###  Salesperson  degrees : 0  match word : PO Number  | distance : 0.2963850223908568\n", "###  Order No.  degrees : 0  match word : PO Number  | distance : 0.4567976118655639\n", "###  Ship Via  degrees : 0  match word : PO Number  | distance : 0.5995984208975735\n", "###  Terms  degrees : 0  match word : PO Number  | distance : 0.6755625646799899\n", "###  PO210064496  degrees : 70  match word : PO Number  | distance : 0.*****************\n", "###  Oct 15, 2020  degrees : 5  match word : PO Number  | distance : 0.*****************\n", "###  UTA001  degrees : 3  match word : PO Number  | distance : 0.*****************\n", "###  BDRAKE  degrees : 2  match word : PO Number  | distance : 0.31397306899445976\n", "###  20094500  degrees : 1  match word : PO Number  | distance : 0.4613993387537823\n", "###  GROUND  degrees : 1  match word : PO Number  | distance : 0.5984455655883565\n", "###  N30  degrees : 1  match word : PO Number  | distance : 0.6878835441417259\n", "###  Qty.  degrees : 158  match word : PO Number  | distance : 0.13564074462784198\n", "###  Qty.  degrees : 115  match word : PO Number  | distance : 0.07286377431489327\n", "###  Ord.  degrees : 150  match word : PO Number  | distance : 0.13989689502670127\n", "###  Shp.  degrees : 109  match word : PO Number  | distance : 0.08097699997816017\n", "###  Item Number  degrees : 25  match word : PO Number  | distance : 0.049052795752821836\n", "###  Line  degrees : 13  match word : PO Number  | distance : 0.****************\n", "###  Description  degrees : 7  match word : PO Number  | distance : 0.27105801831747767\n", "###  Unit Price  degrees : 4  match word : PO Number  | distance : 0.4807823706926153\n", "###  Extended Price  degrees : 3  match word : PO Number  | distance : 0.6081278581437047\n", "###  Remit To:  degrees : 98  match word : PO Number  | distance : 0.5160529083138535\n", "###  Currency in USD unless otherwise specified  degrees : 61  match word : PO Number  | distance : 0.5049492034295849\n", "###  Carahsoft Technology Corporation  degrees : 90  match word : PO Number  | distance : 0.5286620555432799\n", "###  Subtotal  degrees : 43  match word : PO Number  | distance : 0.6815013842271025\n", "###  1,247.91  degrees : 34  match word : PO Number  | distance : 0.834897696498057\n", "###  11493 Sunset Hills Road, Suite 100  degrees : 90  match word : PO Number  | distance : 0.5409706089102754\n", "###  Total sales tax  degrees : 43  match word : PO Number  | distance : 0.6925256838874426\n", "###  0.00  degrees : 34  match word : PO Number  | distance : 0.8664679012188417\n", "###  <PERSON><PERSON>, VA 20190  degrees : 95  match word : PO Number  | distance : 0.5543488692145706\n", "###  FEIN 52-2189693 DUNS *********  degrees : 89  match word : PO Number  | distance : 0.5669593647665286\n", "###  Total amount  degrees : 45  match word : PO Number  | distance : 0.****************\n", "###  1,247.91  degrees : 36  match word : PO Number  | distance : 0.****************\n", "###  CA Sales Tax # SC OHB **********  degrees : 89  match word : PO Number  | distance : 0.****************\n", "###  Less payment  degrees : 46  match word : PO Number  | distance : 0.****************\n", "###  0.00  degrees : 37  match word : PO Number  | distance : 0.****************\n", "###  For questions on this invoice, please contact Accounts  degrees : 84  match word : PO Number  | distance : 0.****************\n", "###  Receivable: <EMAIL>, Ph: 703-581-6566  degrees : 85  match word : PO Number  | distance : 0.****************\n", "###  Amount due  degrees : 47  match word : PO Number  | distance : 0.****************\n", "###  1,247.91  degrees : 39  match word : PO Number  | distance : 0.****************\n", "###  Fax: 703-871-8505  degrees : 94  match word : PO Number  | distance : 0.****************\n", "*************dist list ****************\n", "{0.*****************: ('1242533e-b495-409a-b0cb-452c97f4afb9', 0), 0.****************: ('39fd7a87-9634-4b41-b386-6cc8d085ab08', 0), 0.*****************: ('26a5cf5f-c370-43bb-b6bc-ca6f6f7ba63e', 70), 0.*****************: ('5e2764c3-7d32-4704-86ab-4afae69bf0c2', 5), 0.*****************: ('27d93f3a-997e-46a6-b3dc-a97090e00984', 3), 0.049052795752821836: ('a00e7717-8cde-4e95-8202-bedc417ee0ab', 25), 0.****************: ('e4c292ae-879a-4f39-bbe5-f6b709d7bd31', 13)}\n", "Nearest matches  0.049052795752821836   25   PO Number   Item Number\n", "Nearest matches  0.*****************   70   PO Number   PO210064496\n", "Nearest matches  0.*****************   0   PO Number   Order Date\n", "Nearest matches  0.*****************   5   PO Number   Oct 15, 2020\n", "Nearest matches  0.****************   13   PO Number   Line\n", "Nearest matches  0.****************   0   PO Number   Customer No.\n", "Nearest matches  0.*****************   3   PO Number   UTA001\n", "dst list  4.905279575282184 25 PO Number Item Number conf  99.23759460449219\n", "*************************\n", "dst list  7.477766443414986 70 PO Number PO210064496 conf  96.51687622070312\n", "*************************\n", "regex match  PO210064496\n", "indices  0.***************** ['PO210064496'] 96.51687622070312\n", "dst list  8.670930188580355 0 PO Number Order Date conf  99.63690185546875\n", "*************************\n", "dst list  8.854466369393817 5 PO Number Oct 15, 2020 conf  99.64461517333984\n", "*************************\n", "regex match  15\n", "indices  0.***************** ['15'] 99.64461517333984\n", "dst list  15.23759343893871 13 PO Number Line conf  99.94022369384766\n", "*************************\n", "dst list  18.61875676473978 0 PO Number Customer No. conf  99.80911254882812\n", "*************************\n", "dst list  21.06732558862536 3 PO Number UTA001 conf  99.80906677246094\n", "*************************\n", "regex match  UTA001\n", "indices  0.***************** ['UTA001'] 99.80906677246094\n", "************* PO_no ********************\n", "match list  {1: (['PO210064496'], 86.51687622070312), 3: (['15'], 69.64461517333984), 6: (['UTA001'], 39.80906677246094)}\n", "match list keys  [1, 3, 6]\n", "PO_no  :  ['PO210064496'] confidence : 86.51687622070312\n", "top ....  0\n", "bottom......  0\n", "******************** Total ******************************\n", "word list \n", "['total due', 'total ', 'total ', 'balance due', 'balance ', 'balance ', 'TOTAL USD due', 'TOTAL USD ', 'TOTAL USD ', ' due', ' ', ' ']\n", "Best Match Found  None\n", "top ....  0\n", "bottom......  0\n", "******************** Issue_Date ******************************\n", "word list \n", "['issue ', 'issue date', 'issue ', ' ', ' date', ' ']\n", "--> date date 100\n", "Best Match Found  ('Date', 0.785037100315094, 0.03294479474425316, 0.8134786486625671, 0.03294479474425316, (0.7992578744888306, 0.03294479474425316), 99.68639373779297)\n", "###  carahsoft.  degrees : -179  match word : Date  | distance : 0.7695843099573841\n", "###  Date  degrees : 0  match word : Date  | distance : 0.028441548347473145\n", "###  Page  degrees : 0  match word : Date  | distance : 0.*****************\n", "###  Invoice  degrees : 178  match word : Date  | distance : 0.3551281179876314\n", "###  Oct 19, 2020  degrees : 67  match word : Date  | distance : 0.*****************\n", "###  1  degrees : 8  match word : Date  | distance : 0.*****************\n", "###  Carahsoft Technology Corp  degrees : 177  match word : Date  | distance : 0.7744202195668946\n", "###  Invoice Number  degrees : 34  match word : Date  | distance : 0.*****************\n", "###  IN859177  degrees : 43  match word : Date  | distance : 0.*****************\n", "###  11493 Sunset Hills Road, Suite 100  degrees : 175  match word : Date  | distance : 0.7750629943825759\n", "###  <PERSON><PERSON>, VA 20190  degrees : 175  match word : Date  | distance : 0.****************\n", "###  USA  degrees : 174  match word : Date  | distance : 0.****************\n", "###  Sold To:  degrees : 166  match word : Date  | distance : 0.****************\n", "###  Ship To:  degrees : 138  match word : Date  | distance : 0.*****************\n", "###  State of Utah  degrees : 164  match word : Date  | distance : 0.****************\n", "###  State of Utah - Dept of Administrative Services  degrees : 111  match word : Date  | distance : 0.****************\n", "###  Attn: Accounts Payable  degrees : 163  match word : Date  | distance : 0.****************\n", "###  Attn: <PERSON>  degrees : 127  match word : Date  | distance : 0.****************\n", "###  1 State Office Building, Flr 6  degrees : 161  match word : Date  | distance : 0.****************\n", "###  1 State Office Building  degrees : 124  match word : Date  | distance : 0.*****************\n", "###  Salt Lake City, UT 84114  degrees : 160  match word : Date  | distance : 0.****************\n", "###  Basement, B108  degrees : 126  match word : Date  | distance : 0.*****************\n", "###  Salt Lake City, UT 84114  degrees : 120  match word : Date  | distance : 0.****************\n", "###  PO Number  degrees : 157  match word : Date  | distance : 0.****************\n", "###  Order Date  degrees : 151  match word : Date  | distance : 0.****************\n", "###  Customer No.  degrees : 144  match word : Date  | distance : 0.*****************\n", "###  Salesperson  degrees : 135  match word : Date  | distance : 0.4051933723129719\n", "###  Order No.  degrees : 112  match word : Date  | distance : 0.2992824437717339\n", "###  Ship Via  degrees : 83  match word : Date  | distance : 0.2577272140284467\n", "###  Terms  degrees : 68  match word : Date  | distance : 0.26615285520246945\n", "###  PO210064496  degrees : 156  match word : Date  | distance : 0.7348427330335816\n", "###  Oct 15, 2020  degrees : 149  match word : Date  | distance : 0.5886629126090019\n", "###  UTA001  degrees : 143  match word : Date  | distance : 0.4830795810164669\n", "###  BDRAKE  degrees : 133  match word : Date  | distance : 0.4018857459009138\n", "###  20094500  degrees : 110  match word : Date  | distance : 0.31005194958325794\n", "###  GROUND  degrees : 83  match word : Date  | distance : 0.27241771870780795\n", "###  N30  degrees : 68  match word : Date  | distance : 0.2833382298009361\n", "###  Qty.  degrees : 157  match word : Date  | distance : 0.795748223710102\n", "###  Qty.  degrees : 155  match word : Date  | distance : 0.7341741282411104\n", "###  Ord.  degrees : 156  match word : Date  | distance : 0.8010961426387045\n", "###  Shp.  degrees : 154  match word : Date  | distance : 0.7409992628922687\n", "###  Item Number  degrees : 150  match word : Date  | distance : 0.6635770216383361\n", "###  Line  degrees : 145  match word : Date  | distance : 0.5539869760790964\n", "###  Description  degrees : 133  match word : Date  | distance : 0.45696854683151283\n", "###  Unit Price  degrees : 105  match word : Date  | distance : 0.32981838161867244\n", "###  Extended Price  degrees : 79  match word : Date  | distance : 0.30304391986732765\n", "###  Remit To:  degrees : 133  match word : Date  | distance : 1.0638939858057064\n", "###  Currency in USD unless otherwise specified  degrees : 115  match word : Date  | distance : 0.9128644353869818\n", "###  Carahsoft Technology Corporation  degrees : 129  match word : Date  | distance : 1.073391817897398\n", "###  Subtotal  degrees : 97  match word : Date  | distance : 0.7763768923589432\n", "###  1,247.91  degrees : 82  match word : Date  | distance : 0.7637597865339787\n", "###  11493 Sunset Hills Road, Suite 100  degrees : 129  match word : Date  | distance : 1.0821216577524997\n", "###  Total sales tax  degrees : 96  match word : Date  | distance : 0.791331511944354\n", "###  0.00  degrees : 81  match word : Date  | distance : 0.7813688587510907\n", "###  <PERSON><PERSON>, VA 20190  degrees : 130  match word : Date  | distance : 1.092440994775463\n", "###  FEIN 52-2189693 DUNS *********  degrees : 128  match word : Date  | distance : 1.1020075617341913\n", "###  Total amount  degrees : 96  match word : Date  | distance : 0.****************\n", "###  1,247.91  degrees : 83  match word : Date  | distance : 0.****************\n", "###  CA Sales Tax # SC OHB **********  degrees : 127  match word : Date  | distance : 1.****************\n", "###  Less payment  degrees : 95  match word : Date  | distance : 0.****************\n", "###  0.00  degrees : 82  match word : Date  | distance : 0.****************\n", "###  For questions on this invoice, please contact Accounts  degrees : 124  match word : Date  | distance : 1.****************\n", "###  Receivable: <EMAIL>, Ph: 703-581-6566  degrees : 124  match word : Date  | distance : 1.****************\n", "###  Amount due  degrees : 95  match word : Date  | distance : 0.***************\n", "###  1,247.91  degrees : 83  match word : Date  | distance : 0.****************\n", "###  Fax: 703-871-8505  degrees : 127  match word : Date  | distance : 1.****************\n", "*************dist list ****************\n", "{0.*****************: ('753dbf3e-39d0-48e3-a359-16168fb591c1', 0), 0.*****************: ('9051821f-4506-4f9c-ab1c-18078350af89', 67), 0.*****************: ('012518fc-8278-43cf-8101-bbe7554b1629', 8), 0.*****************: ('17768cc9-3d28-4db9-a387-98c8617c4244', 34), 0.*****************: ('4a31f2b7-1a2c-47fe-841b-e6f805d675c0', 43)}\n", "Nearest matches  0.*****************   34   Date   Invoice Number\n", "Nearest matches  0.*****************   43   Date   IN859177\n", "Nearest matches  0.*****************   67   Date   Oct 19, 2020\n", "Nearest matches  0.*****************   0   Date   Page\n", "Nearest matches  0.*****************   8   Date   1\n", "dst list  3.609596644465411 34 Date Invoice Number conf  99.86193084716797\n", "*************************\n", "matches  [] length  0\n", "result  []\n", "dst list  4.047073402863125 43 Date IN859177 conf  99.86270904541016\n", "*************************\n", "matches  [] length  0\n", "result  []\n", "dst list  4.792293226715437 67 Date Oct 19, 2020 conf  99.**************\n", "*************************\n", "matches  [datetime.datetime(2020, 10, 19, 0, 0)] length  1\n", "M 2020-10-19 00:00:00\n", "result  [datetime.datetime(2020, 10, 19, 0, 0)]\n", "indices  0.***************** [datetime.datetime(2020, 10, 19, 0, 0)] 99.**************\n", "dst list  6.469768611012353 0 Date Page conf  99.14176177978516\n", "*************************\n", "matches  [] length  0\n", "result  []\n", "dst list  8.*************** 8 Date 1 conf  99.**************\n", "*************************\n", "matches  [] length  0\n", "result  []\n", "************* Issue_Date ********************\n", "match list  {2: ([datetime.datetime(2020, 10, 19, 0, 0)], 79.**************)}\n", "match list keys  [2]\n", "Issue_Date  :  [datetime.datetime(2020, 10, 19, 0, 0)] confidence : 79.**************\n", "top ....  0\n", "bottom......  0\n", "******************** bank_name ******************************\n"]}, {"name": "stdout", "output_type": "stream", "text": ["word list \n", "['bank name', 'bank name name']\n", "Best Match Found  None\n", "top ....  0\n", "bottom......  0\n", "******************** account_number ******************************\n", "word list \n", "['account number']\n", "Best Match Found  None\n", "top ....  0\n", "bottom......  0\n", "******************** terms ******************************\n", "word list \n", "['terms ', 'terms ']\n", "--> terms terms 100\n", "Best Match Found  ('Terms', 0.****************, 0.***************, 0.****************, 0.***************, (0.****************, 0.***************), 99.**************)\n", "###  carahsoft.  degrees : -160  match word : Terms  | distance : 0.****************\n", "###  Date  degrees : -111  match word : Terms  | distance : 0.*****************\n", "###  Page  degrees : -91  match word : Terms  | distance : 0.*****************\n", "###  Invoice  degrees : -148  match word : Terms  | distance : 0.****************\n", "###  Oct 19, 2020  degrees : -111  match word : Terms  | distance : 0.*****************\n", "###  1  degrees : -89  match word : Terms  | distance : 0.*****************\n", "###  Carahsoft Technology Corp  degrees : -163  match word : Terms  | distance : 0.********65259269\n", "###  Invoice Number  degrees : -103  match word : Terms  | distance : 0.26045264918866834\n", "###  IN859177  degrees : -104  match word : Terms  | distance : 0.24127045127183538\n", "###  11493 Sunset Hills Road, Suite 100  degrees : -164  match word : Terms  | distance : 0.903451697350449\n", "###  <PERSON><PERSON>, VA 20190  degrees : -166  match word : Terms  | distance : 0.****************\n", "###  USA  degrees : -167  match word : Terms  | distance : 0.****************\n", "###  Sold To:  degrees : -172  match word : Terms  | distance : 0.****************\n", "###  Ship To:  degrees : -160  match word : Terms  | distance : 0.*****************\n", "###  State of Utah  degrees : -173  match word : Terms  | distance : 0.**************\n", "###  State of Utah - Dept of Administrative Services  degrees : -153  match word : Terms  | distance : 0.*****************\n", "###  Attn: Accounts Payable  degrees : -174  match word : Terms  | distance : 0.****************\n", "###  Attn: <PERSON>  degrees : -162  match word : Terms  | distance : 0.****************\n", "###  1 State Office Building, Flr 6  degrees : -174  match word : Terms  | distance : 0.****************\n", "###  1 State Office Building  degrees : -165  match word : Terms  | distance : 0.****************\n", "###  Salt Lake City, UT 84114  degrees : -175  match word : Terms  | distance : 0.****************\n", "###  Basement, B108  degrees : -168  match word : Terms  | distance : 0.****************\n", "###  Salt Lake City, UT 84114  degrees : -170  match word : Terms  | distance : 0.****************\n", "###  PO Number  degrees : -179  match word : Terms  | distance : 0.****************\n", "###  Order Date  degrees : -179  match word : Terms  | distance : 0.****************\n", "###  Customer No.  degrees : -179  match word : Terms  | distance : 0.****************\n", "###  Salesperson  degrees : -179  match word : Terms  | distance : 0.41772806100611654\n", "###  Order No.  degrees : -179  match word : Terms  | distance : 0.2573154599746064\n", "###  Ship Via  degrees : -179  match word : Terms  | distance : 0.1145147643302521\n", "###  Terms  degrees : 0  match word : Terms  | distance : 0.03855043649673462\n", "###  PO210064496  degrees : 178  match word : Terms  | distance : 0.7875897083241252\n", "###  Oct 15, 2020  degrees : 178  match word : Terms  | distance : 0.6269589675497821\n", "###  UTA001  degrees : 178  match word : Terms  | distance : 0.5041556169042417\n", "###  BDRAKE  degrees : 177  match word : Terms  | distance : 0.4007452076211362\n", "###  20094500  degrees : 175  match word : Terms  | distance : 0.2533749857723132\n", "###  GROUND  degrees : 167  match word : Terms  | distance : 0.11674143554227835\n", "###  N30  degrees : 71  match word : Terms  | distance : 0.*****************\n", "###  Qty.  degrees : 177  match word : Terms  | distance : 0.8463373142446938\n", "###  Qty.  degrees : 177  match word : Terms  | distance : 0.7798485892682484\n", "###  Ord.  degrees : 176  match word : Terms  | distance : 0.8475523868558414\n", "###  Shp.  degrees : 176  match word : Terms  | distance : 0.7822813039386958\n", "###  Item Number  degrees : 175  match word : Terms  | distance : 0.6967523901245031\n", "###  Line  degrees : 175  match word : Terms  | distance : 0.5704537456207114\n", "###  Description  degrees : 173  match word : Terms  | distance : 0.4491835605909504\n", "###  Unit Price  degrees : 166  match word : Terms  | distance : 0.2397467290919564\n", "###  Extended Price  degrees : 132  match word : Terms  | distance : 0.11681316507228685\n", "###  Remit To:  degrees : 148  match word : Terms  | distance : 0.9889986801583465\n", "###  Currency in USD unless otherwise specified  degrees : 133  match word : Terms  | distance : 0.7938836819631023\n", "###  Carahsoft Technology Corporation  degrees : 145  match word : Terms  | distance : 0.9958863331972466\n", "###  Subtotal  degrees : 112  match word : Terms  | distance : 0.5649232984155489\n", "###  1,247.91  degrees : 90  match word : Terms  | distance : 0.5062546515215895\n", "###  11493 Sunset Hills Road, Suite 100  degrees : 144  match word : Terms  | distance : 1.0018615267663729\n", "###  Total sales tax  degrees : 109  match word : Terms  | distance : 0.5786627945643686\n", "###  0.00  degrees : 88  match word : Terms  | distance : 0.5194320793770485\n", "###  <PERSON><PERSON>, VA 20190  degrees : 145  match word : Terms  | distance : 1.0095731995290735\n", "###  FEIN 52-2189693 DUNS *********  degrees : 143  match word : Terms  | distance : 1.0166398140584973\n", "###  Total amount  degrees : 109  match word : Terms  | distance : 0.****************\n", "###  1,247.91  degrees : 90  match word : Terms  | distance : 0.****************\n", "###  CA Sales Tax # SC OHB **********  degrees : 142  match word : Terms  | distance : 1.****************\n", "###  Less payment  degrees : 108  match word : Terms  | distance : 0.***************\n", "###  0.00  degrees : 88  match word : Terms  | distance : 0.****************\n", "###  For questions on this invoice, please contact Accounts  degrees : 139  match word : Terms  | distance : 1.****************\n", "###  Receivable: <EMAIL>, Ph: 703-581-6566  degrees : 138  match word : Terms  | distance : 1.****************\n", "###  Amount due  degrees : 107  match word : Terms  | distance : 0.****************\n", "###  1,247.91  degrees : 90  match word : Terms  | distance : 0.****************\n", "###  Fax: 703-871-8505  degrees : 141  match word : Terms  | distance : 1.****************\n", "*************dist list ****************\n", "{0.*****************: ('e1c43ce6-d85e-40be-b15d-55b111e17fb3', 71)}\n", "Nearest matches  0.*****************   71   Terms   N30\n", "dst list  3.*************** 71 Terms N30 conf  99.************22\n", "*************************\n", "regex match  N30\n", "indices  0.***************** ['N30'] 99.************22\n", "************* terms ********************\n", "match list  {0: (['N30'], 99.************22)}\n", "match list keys  [0]\n", "terms  :  ['N30'] confidence : 99.************22\n", "top ....  0\n", "bottom......  0\n", "******************** ship_to ******************************\n", "word list \n", "['ship to ', 'ship to to', 'ship ', 'ship to']\n", "--> ship to: ship to 93\n", "Best Match Found  ('Ship To:', 0.5954749584197998, 0.19282673299312592, 0.646452009677887, 0.19282673299312592, (0.6209634840488434, 0.19282673299312592), 99.87456512451172)\n", "###  carahsoft.  degrees : -159  match word : Ship To:  | distance : 0.6257567803168347\n", "###  Date  degrees : -41  match word : Ship To:  | distance : 0.21158464387837614\n", "###  Page  degrees : -30  match word : Ship To:  | distance : 0.2814774199455577\n", "###  Invoice  degrees : -130  match word : Ship To:  | distance : 0.24062149138625996\n", "###  Oct 19, 2020  degrees : -38  match word : Ship To:  | distance : 0.18947950437728978\n", "###  1  degrees : -27  match word : Ship To:  | distance : 0.29128759409496596\n", "###  Carahsoft Technology Corp  degrees : -165  match word : Ship To:  | distance : 0.6202678743054901\n", "###  Invoice Number  degrees : -30  match word : Ship To:  | distance : 0.19594321811230198\n", "###  IN859177  degrees : -28  match word : Ship To:  | distance : 0.20533526194213267\n", "###  11493 Sunset Hills Road, Suite 100  degrees : -166  match word : Ship To:  | distance : 0.61701045587708\n", "###  <PERSON><PERSON>, VA 20190  degrees : -169  match word : Ship To:  | distance : 0.615729762421945\n", "###  USA  degrees : -171  match word : Ship To:  | distance : 0.****************\n", "###  Sold To:  degrees : -179  match word : Ship To:  | distance : 0.****************\n", "###  Ship To:  degrees : 0  match word : Ship To:  | distance : 0.*****************\n", "###  State of Utah  degrees : 178  match word : Ship To:  | distance : 0.****************\n", "###  State of Utah - Dept of Administrative Services  degrees : 6  match word : Ship To:  | distance : 0.*****************\n", "###  Attn: Accounts Payable  degrees : 176  match word : Ship To:  | distance : 0.****************\n", "###  Attn: <PERSON>  degrees : 32  match word : Ship To:  | distance : 0.*****************\n", "###  1 State Office Building, Flr 6  degrees : 174  match word : Ship To:  | distance : 0.***************\n", "###  1 State Office Building  degrees : 40  match word : Ship To:  | distance : 0.*****************\n", "###  Salt Lake City, UT 84114  degrees : 173  match word : Ship To:  | distance : 0.***************\n", "###  Basement, B108  degrees : 61  match word : Ship To:  | distance : 0.*****************\n", "###  Salt Lake City, UT 84114  degrees : 49  match word : Ship To:  | distance : 0.*****************\n", "###  PO Number  degrees : 167  match word : Ship To:  | distance : 0.****************\n", "###  Order Date  degrees : 161  match word : Ship To:  | distance : 0.****************\n", "###  Customer No.  degrees : 152  match word : Ship To:  | distance : 0.*****************\n", "###  Salesperson  degrees : 129  match word : Ship To:  | distance : 0.1754773056687323\n", "###  Order No.  degrees : 54  match word : Ship To:  | distance : 0.*****************\n", "###  Ship Via  degrees : 25  match word : Ship To:  | distance : 0.****************\n", "###  Terms  degrees : 19  match word : Ship To:  | distance : 0.25307201629965825\n", "###  PO210064496  degrees : 165  match word : Ship To:  | distance : 0.5276270768421439\n", "###  Oct 15, 2020  degrees : 158  match word : Ship To:  | distance : 0.3722457409368031\n", "###  UTA001  degrees : 148  match word : Ship To:  | distance : 0.25780232398244574\n", "###  BDRAKE  degrees : 124  match word : Ship To:  | distance : 0.17077062412734872\n", "###  20094500  degrees : 56  match word : Ship To:  | distance : 0.11413326830010681\n", "###  GROUND  degrees : 28  match word : Ship To:  | distance : 0.19229903386040764\n", "###  N30  degrees : 21  match word : Ship To:  | distance : 0.2700352419516528\n", "###  Qty.  degrees : 166  match word : Ship To:  | distance : 0.5884656477228937\n", "###  Qty.  degrees : 164  match word : Ship To:  | distance : 0.5237764645421055\n", "###  Ord.  degrees : 164  match word : Ship To:  | distance : 0.5920495293379527\n", "###  Shp.  degrees : 162  match word : Ship To:  | distance : 0.528859018731004\n", "###  Item Number  degrees : 158  match word : Ship To:  | distance : 0.44687690524467566\n", "###  Line  degrees : 150  match word : Ship To:  | distance : 0.32945441688890953\n", "###  Description  degrees : 128  match word : Ship To:  | distance : 0.22609795614830103\n", "###  Unit Price  degrees : 57  match word : Ship To:  | distance : 0.1475754616703746\n", "###  Extended Price  degrees : 31  match word : Ship To:  | distance : 0.21787895554365333\n", "###  Remit To:  degrees : 131  match word : Ship To:  | distance : 0.8327653518405135\n", "###  Currency in USD unless otherwise specified  degrees : 107  match word : Ship To:  | distance : 0.6882847345773582\n", "###  Carahsoft Technology Corporation  degrees : 127  match word : Ship To:  | distance : 0.8423322048152001\n", "###  Subtotal  degrees : 83  match word : Ship To:  | distance : 0.602003195024846\n", "###  1,247.91  degrees : 65  match word : Ship To:  | distance : 0.6419309754614348\n", "###  11493 Sunset Hills Road, Suite 100  degrees : 126  match word : Ship To:  | distance : 0.8511590027731053\n", "###  Total sales tax  degrees : 81  match word : Ship To:  | distance : 0.6171481097173336\n", "###  0.00  degrees : 64  match word : Ship To:  | distance : 0.6662238158204824\n", "###  <PERSON><PERSON>, VA 20190  degrees : 128  match word : Ship To:  | distance : 0.8615882806802456\n", "###  FEIN 52-2189693 DUNS *********  degrees : 124  match word : Ship To:  | distance : 0.8712799261547338\n", "###  Total amount  degrees : 82  match word : Ship To:  | distance : 0.***************\n", "###  1,247.91  degrees : 66  match word : Ship To:  | distance : 0.****************\n", "###  CA Sales Tax # SC OHB **********  degrees : 124  match word : Ship To:  | distance : 0.****************\n", "###  Less payment  degrees : 82  match word : Ship To:  | distance : 0.***************\n", "###  0.00  degrees : 66  match word : Ship To:  | distance : 0.****************\n", "###  For questions on this invoice, please contact Accounts  degrees : 120  match word : Ship To:  | distance : 0.****************\n", "###  Receivable: <EMAIL>, Ph: 703-581-6566  degrees : 120  match word : Ship To:  | distance : 0.****************\n", "###  Amount due  degrees : 82  match word : Ship To:  | distance : 0.****************\n", "###  1,247.91  degrees : 68  match word : Ship To:  | distance : 0.****************\n", "###  Fax: 703-871-8505  degrees : 125  match word : Ship To:  | distance : 0.****************\n", "*************dist list ****************\n", "{0.*****************: ('594b3a6b-7a54-4eb5-864e-f8a26b9a7adf', 6), 0.*****************: ('01816d17-4fda-4046-9633-4c15071fd26e', 32), 0.*****************: ('c1728538-e2df-4c71-9566-6c9b67d55403', 40), 0.*****************: ('d96b921a-19be-4fd4-b1ed-4c0617be6b9b', 61), 0.*****************: ('f4005691-35a0-4642-8f8a-6997e407c4ab', 49), 0.*****************: ('16bb9498-42fb-4b13-901e-4ff63c728863', 54), 0.****************: ('eecf71e3-5b20-40a5-8d8d-317e0a20493b', 25), 0.11413326830010681: ('0b6af143-351e-441e-a074-10d647c53c71', 56), 0.19229903386040764: ('ab854f02-2121-401f-b89a-48df0a0d36eb', 28), 0.1475754616703746: ('aa3a7d52-1dca-46ed-99f6-56d46ebc040c', 57), 0.21787895554365333: ('284f964e-78d9-4f66-8335-a0dc489ec53c', 31)}\n", "Nearest matches  0.*****************   6   Ship To:   State of Utah - Dept of Administrative Services\n", "Nearest matches  0.*****************   32   Ship To:   Attn: <PERSON>\n", "Nearest matches  0.*****************   40   Ship To:   1 State Office Building\n", "Nearest matches  0.*****************   61   Ship To:   Basement, B108\n", "Nearest matches  0.*****************   49   Ship To:   Salt Lake City, UT 84114\n", "Nearest matches  0.*****************   54   Ship To:   Order No.\n", "Nearest matches  0.11413326830010681   56   Ship To:   20094500\n", "Nearest matches  0.1475754616703746   57   Ship To:   Unit Price\n", "Nearest matches  0.****************   25   Ship To:   Ship Via\n", "Nearest matches  0.19229903386040764   28   Ship To:   GROUND\n", "Nearest matches  0.21787895554365333   31   Ship To:   Extended Price\n", "dst list  5.033470059147479 6 Ship To: State of Utah - Dept of Administrative Services conf  99.22753143310547\n", "*************************\n", "regex match  State of Utah - Dept of Administrative Services\n", "indices  {'id': 6, 'name': 'string', 'description': '', 'regex': '[a-zA-Z0-9].*'} ['State of Utah - Dept of Administrative Services'] 99.22753143310547\n", "dst list  5.486886776979663 32 Ship To: Attn: <PERSON> conf  99.8256607055664\n", "*************************\n", "regex match  Attn: <PERSON>\n", "indices  {'id': 6, 'name': 'string', 'description': '', 'regex': '[a-zA-Z0-9].*'} ['Attn: <PERSON>'] 99.8256607055664\n", "dst list  5.997245873452799 40 Ship To: 1 State Office Building conf  99.85861206054688\n", "*************************\n", "regex match  1 State Office Building\n", "indices  {'id': 6, 'name': 'string', 'description': '', 'regex': '[a-zA-Z0-9].*'} ['1 State Office Building'] 99.85861206054688\n", "dst list  6.763218123234986 61 Ship To: Basement, B108 conf  98.89541625976562\n", "*************************\n", "regex match  Basement B108\n", "indices  {'id': 6, 'name': 'string', 'description': '', 'regex': '[a-zA-Z0-9].*'} ['Basement B108'] 98.89541625976562\n", "dst list  7.568074217711753 49 Ship To: Salt Lake City, UT 84114 conf  99.66700744628906\n", "*************************\n", "regex match  Salt Lake City UT 84114\n", "indices  {'id': 6, 'name': 'string', 'description': '', 'regex': '[a-zA-Z0-9].*'} ['Salt Lake City UT 84114'] 99.66700744628906\n", "dst list  9.877437456873984 54 Ship To: Order No. conf  99.73684692382812\n", "*************************\n", "regex match  Order No.\n", "indices  {'id': 6, 'name': 'string', 'description': '', 'regex': '[a-zA-Z0-9].*'} ['Order No.'] 99.73684692382812\n", "dst list  11.41332683001068 56 Ship To: 20094500 conf  99.35526275634766\n", "*************************\n", "regex match  20094500\n", "indices  {'id': 6, 'name': 'string', 'description': '', 'regex': '[a-zA-Z0-9].*'} ['20094500'] 99.35526275634766\n", "dst list  14.75754616703746 57 Ship To: Unit Price conf  99.51513671875\n", "*************************\n", "regex match  Unit Price\n", "indices  {'id': 6, 'name': 'string', 'description': '', 'regex': '[a-zA-Z0-9].*'} ['Unit Price'] 99.51513671875\n", "dst list  18.525486839188808 25 Ship To: Ship Via conf  99.76663208007812\n", "*************************\n", "regex match  Ship Via\n", "indices  {'id': 6, 'name': 'string', 'description': '', 'regex': '[a-zA-Z0-9].*'} ['Ship Via'] 99.76663208007812\n", "dst list  19.229903386040764 28 Ship To: GROUND conf  99.8648910522461\n", "*************************\n", "regex match  GROUND\n", "indices  {'id': 6, 'name': 'string', 'description': '', 'regex': '[a-zA-Z0-9].*'} ['GROUND'] 99.8648910522461\n", "************* ship_to ********************\n", "match list  {0: (['State of Utah - Dept of Administrative Services'], 99.22753143310547), 1: (['Attn: <PERSON>'], 89.8256607055664), 2: (['1 State Office Building'], 79.85861206054688), 3: (['Basement B108'], 68.89541625976562), 4: (['Salt Lake City UT 84114'], 59.66700744628906), 5: (['Order No.'], 49.736846923828125), 6: (['20094500'], 39.355262756347656), 7: (['Unit Price'], 29.51513671875), 8: (['Ship Via'], 19.766632080078125), 9: (['GROUND'], 9.864891052246094)}\n", "match list keys  [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]\n", "ship_to  :  ['State of Utah - Dept of Administrative Services'] confidence : 99.22753143310547\n", "top ....  0\n", "bottom......  0\n", "******************** bill_to ******************************\n", "word list \n", "['bill to ', 'bill to to', 'bill ', 'bill to', 'Billing Address ', 'Billing Address to']\n", "Best Match Found  None\n", "top ....  0\n", "bottom......  0\n", "******************** balance_due ******************************\n", "word list \n", "['balance due']\n", "Best Match Found  None\n", "JSON File Written ...\n", "2\n"]}, {"name": "stdout", "output_type": "stream", "text": ["{'I': 0.93305224, 'NI': 0.06694776}\n", "fetching Boto Response .....\n", "\n", "\n", "== FOUND KEY : VALUE pairs ===\n", "\n", "[1, 2, 3, 4, 5, 6, 7, 8, 17, 18, 19, 20, 21]\n", "regex_id  [1, 2, 3, 4]\n", "regex_id  [6]\n", "regex_id  [6]\n", "top ....  0\n", "bottom......  0\n", "******************** Invoice_No ******************************\n", "word list \n", "['invoice no', 'invoice no.', 'invoice id', 'invoice #', 'invoice number', 'invoice: no', 'invoice: no.', 'invoice: id', 'invoice: #', 'invoice: number', 'Invoice ID no', 'Invoice ID no.', 'Invoice ID id', 'Invoice ID #', 'Invoice ID number', ' no', ' no.', ' id', ' #', ' number']\n", "--> invoice number: invoice number 97\n", "Best Match Found  ('Invoice Number:', 0.6145089268684387, 0.08466693013906479, 0.7127385139465332, 0.08466693013906479, (0.663623720407486, 0.08466693013906479), 99.4202651977539)\n", "###  P.O. Number:  degrees : -119  match word : Invoice Number:  | distance : 0.09934194958640712\n", "###  Invoice Number:  degrees : 0  match word : Invoice Number:  | distance : 0.09822958707809448\n", "###  *********  degrees : 0  match word : Invoice Number:  | distance : 0.033892710581702075\n", "###  Invoice Date:  degrees : 124  match word : Invoice Number:  | distance : 0.09914417948968855\n", "###  27-Jun-2020  degrees : 6  match word : Invoice Number:  | distance : 0.036502273905648655\n", "###  Payment Terms:  degrees : 89  match word : Invoice Number:  | distance : 0.*****************\n", "###  Net Terms 30  degrees : 13  match word : Invoice Number:  | distance : 0.*****************\n", "###  A  degrees : 176  match word : Invoice Number:  | distance : 0.6092097384484318\n", "###  Due Date:  degrees : 113  match word : Invoice Number:  | distance : 0.*****************\n", "###  27-Jul-2020  degrees : 20  match word : Invoice Number:  | distance : 0.*****************\n", "###  Billing Period:  degrees : 98  match word : Invoice Number:  | distance : 0.*****************\n", "###  27-Jun-2020 - 26-Jun-2021  degrees : 19  match word : Invoice Number:  | distance : 0.*****************\n", "###  Adobe  degrees : 171  match word : Invoice Number:  | distance : 0.****************\n", "###  Billing Account ID:  degrees : 85  match word : Invoice Number:  | distance : 0.***************\n", "###  ********  degrees : 32  match word : Invoice Number:  | distance : 0.*****************\n", "###  Customer Tax ID:  degrees : 87  match word : Invoice Number:  | distance : 0.****************\n", "###  BILL TO:  degrees : 167  match word : Invoice Number:  | distance : 0.****************\n", "###  WellDyneRx, Inc.  degrees : 165  match word : Invoice Number:  | distance : 0.****************\n", "###  500 Eagles Landing Drive  degrees : 163  match word : Invoice Number:  | distance : 0.****************\n", "###  Lakeland FL 33810  degrees : 163  match word : Invoice Number:  | distance : 0.****************\n", "###  US  degrees : 163  match word : Invoice Number:  | distance : 0.****************\n", "###  Description  degrees : 153  match word : Invoice Number:  | distance : 0.****************\n", "###  Unit Price  degrees : 132  match word : Invoice Number:  | distance : 0.*****************\n", "###  Qty  degrees : 112  match word : Invoice Number:  | distance : 0.24955498438359544\n", "###  Proration Factor  degrees : 79  match word : Invoice Number:  | distance : 0.*****************\n", "###  Net Price  degrees : 43  match word : Invoice Number:  | distance : 0.*****************\n", "###  Adobe Sign - Enterprise Site License Annual  degrees : 151  match word : Invoice Number:  | distance : 0.6588074927715019\n", "###  4.800 USD  degrees : 130  match word : Invoice Number:  | distance : 0.3690221692127619\n", "###  1500  degrees : 114  match word : Invoice Number:  | distance : 0.29277301403360406\n", "###  100.00%  degrees : 90  match word : Invoice Number:  | distance : 0.2506563067956233\n", "###  7,200.00 USD  degrees : 56  match word : Invoice Number:  | distance : 0.***************\n", "###  Check (applied 31-Jul-2019)  degrees : 152  match word : Invoice Number:  | distance : 0.6633198558828182\n", "###  -7,200.00 USD  degrees : 57  match word : Invoice Number:  | distance : 0.2580243151776022\n", "###  RECEIVED 06.29.29 RF  degrees : 120  match word : Invoice Number:  | distance : 0.4943635583849368\n", "###  Invoice Total  degrees : 88  match word : Invoice Number:  | distance : 0.42632630996163984\n", "###  $7,200.00 USD  degrees : 64  match word : Invoice Number:  | distance : 0.4313311685475331\n", "###  Payment by Check  degrees : 137  match word : Invoice Number:  | distance : 0.7840700115289891\n", "###  Payment by Wire or ACH  degrees : 103  match word : Invoice Number:  | distance : 0.5352973257536352\n", "###  Billing Questions  degrees : 70  match word : Invoice Number:  | distance : 0.*****************\n", "###  Adobe Inc.  degrees : 137  match word : Invoice Number:  | distance : 0.****************\n", "###  Beneficiary: Adobe Inc.  degrees : 104  match word : Invoice Number:  | distance : 0.****************\n", "###  <EMAIL>  degrees : 70  match word : Invoice Number:  | distance : 0.****************\n", "###  29322 Network Place  degrees : 134  match word : Invoice Number:  | distance : 0.****************\n", "###  Bank: JPMorgan Chase Bank, N.A.  degrees : 100  match word : Invoice Number:  | distance : 0.****************\n", "###  Chicago, IL 60673-1293  degrees : 133  match word : Invoice Number:  | distance : 0.****************\n", "###  Bank ABA: *********  degrees : 103  match word : Invoice Number:  | distance : 0.****************\n", "###  USA  degrees : 135  match word : Invoice Number:  | distance : 0.****************\n", "###  SWIFT: CHASUS33  degrees : 103  match word : Invoice Number:  | distance : 0.****************\n", "###  Bank account: *********  degrees : 101  match word : Invoice Number:  | distance : 0.****************\n", "###  Remittance information should be sent to  degrees : 125  match word : Invoice Number:  | distance : 0.****************\n", "###  <EMAIL>  degrees : 127  match word : Invoice Number:  | distance : 0.****************\n", "*************dist list ****************\n", "{0.033892710581702075: ('60f5c054-a3aa-4124-b385-1a315b44a969', 0), 0.036502273905648655: ('3b0a7251-2ae8-45ad-ae75-88d0f108031b', 6), 0.*****************: ('ba30348d-31ef-47ed-ae53-2f0d4a9a75b4', 89), 0.*****************: ('41b09a32-d264-4fe8-9624-96a33901f760', 13), 0.*****************: ('cde801ef-1b9b-4079-836c-c2c8acfc0212', 20), 0.*****************: ('********-9f58-4c08-8988-680a28953d5b', 98), 0.*****************: ('31c9a15b-0f4a-4353-bc08-0c122e63b2f6', 19), 0.***************: ('572822a7-b21d-4f1c-bc58-26a5222eb107', 85), 0.*****************: ('758e33ba-5f66-4c53-a0a5-7d536cbd7448', 32), 0.****************: ('b7fa429e-dd21-4135-a9f3-e7b21e4e9412', 87), 0.*****************: ('135ef65f-8ea4-4271-a22e-4bb34363d9c9', 79), 0.*****************: ('09ef9b29-60f2-4971-b352-97a6ca3d892d', 43), 0.***************: ('994ad8f5-de24-41a9-926b-c2f4969f2e3a', 56)}\n", "Nearest matches  0.033892710581702075   0   Invoice Number:   *********\n", "Nearest matches  0.036502273905648655   6   Invoice Number:   27-Jun-2020\n", "Nearest matches  0.*****************   13   Invoice Number:   Net Terms 30\n", "Nearest matches  0.*****************   20   Invoice Number:   27-Jul-2020\n", "Nearest matches  0.*****************   19   Invoice Number:   27-Jun-2020 - 26-Jun-2021\n", "Nearest matches  0.*****************   32   Invoice Number:   ********\n", "Nearest matches  0.*****************   89   Invoice Number:   Payment Terms:\n", "Nearest matches  0.*****************   98   Invoice Number:   Billing Period:\n", "Nearest matches  0.***************   85   Invoice Number:   Billing Account ID:\n", "Nearest matches  0.****************   87   Invoice Number:   Customer Tax ID:\n", "Nearest matches  0.*****************   79   Invoice Number:   Proration Factor\n", "Nearest matches  0.*****************   43   Invoice Number:   Net Price\n", "Nearest matches  0.***************   56   Invoice Number:   7,200.00 USD\n", "dst list  3.**************** 0 Invoice Number: ********* conf  99.*************\n", "*************************\n", "regex match  *********\n", "indices  0.033892710581702075 ['*********'] 99.*************\n", "dst list  3.**************** 6 Invoice Number: 27-Jun-2020 conf  99.**************\n", "*************************\n", "regex match  27-Jun-2020\n", "indices  0.036502273905648655 ['27-Jun-2020'] 99.**************\n", "dst list  4.*************** 13 Invoice Number: Net Terms 30 conf  99.**************\n", "*************************\n", "regex match  30\n", "indices  0.***************** ['30'] 99.**************\n", "dst list  5.*************** 20 Invoice Number: 27-Jul-2020 conf  99.**********6406\n", "*************************\n", "regex match  27-Jul-2020\n", "indices  0.***************** ['27-Jul-2020'] 99.**********6406\n", "dst list  6.654094641021838 19 Invoice Number: 27-Jun-2020 - 26-Jun-2021 conf  98.**************\n", "*************************\n", "regex match  27-Jun-2020\n", "indices  0.***************** ['27-Jun-2020'] 98.**************\n", "dst list  7.*************** 32 Invoice Number: ******** conf  99.**************\n", "*************************\n", "regex match  ********\n", "indices  0.***************** ['********'] 99.**************\n", "dst list  10.*************** 89 Invoice Number: Payment Terms: conf  99.*************\n", "*************************\n", "dst list  11.*************** 98 Invoice Number: Billing Period: conf  99.**************\n", "*************************\n", "dst list  12.************* 85 Invoice Number: Billing Account ID: conf  99.**************\n", "*************************\n", "dst list  13.************** 87 Invoice Number: Customer Tax ID: conf  99.**************\n", "*************************\n", "************* Invoice_No ********************\n", "match list  {0: (['*********'], 99.*************), 1: (['27-Jun-2020'], 89.**************), 2: (['30'], 79.**************), 3: (['27-Jul-2020'], 69.**********6406), 4: (['27-Jun-2020'], 58.**************), 5: (['********'], 49.**************)}\n", "match list keys  [0, 1, 2, 3, 4, 5]\n", "Invoice_No  :  ['*********'] confidence : 99.*************\n", "top ....  0\n", "bottom......  0\n", "******************** PO_no ******************************\n", "word list \n", "['po no', 'po number', 'po order', 'po ', 'purchase no', 'purchase number', 'purchase order', 'purchase ', 'P.O. no', 'P.O. number', 'P.O. order', 'P.O. ', ' no', ' number', ' order', ' ']\n", "--> p.o. number: po number 86\n", "--> p.o. number: p.o. number 96\n", "Best Match Found  ('P.O. Number:', 0.6144136786460876, 0.07048773020505905, 0.6970371007919312, 0.07048773020505905, (0.6557253897190094, 0.07048773020505905), 99.58689880371094)\n", "###  P.O. Number:  degrees : 0  match word : P.O. Number:  | distance : 0.0826234221458435\n", "###  Invoice Number:  degrees : 60  match word : P.O. Number:  | distance : 0.****************\n", "###  *********  degrees : 6  match word : P.O. Number:  | distance : 0.*****************\n", "###  Invoice Date:  degrees : 93  match word : P.O. Number:  | distance : 0.*****************\n", "###  27-Jun-2020  degrees : 12  match word : P.O. Number:  | distance : 0.056920341669256566\n", "###  Payment Terms:  degrees : 79  match word : P.O. Number:  | distance : 0.*****************\n", "###  Net Terms 30  degrees : 18  match word : P.O. Number:  | distance : 0.06***************\n", "###  A  degrees : 175  match word : P.O. Number:  | distance : 0.5944215113908474\n", "###  Due Date:  degrees : 100  match word : P.O. Number:  | distance : 0.*****************\n", "###  27-Jul-2020  degrees : 24  match word : P.O. Number:  | distance : 0.07***************\n", "###  Billing Period:  degrees : 90  match word : P.O. Number:  | distance : 0.*****************\n", "###  27-Jun-2020 - 26-Jun-2021  degrees : 22  match word : P.O. Number:  | distance : 0.****************\n", "###  Adobe  degrees : 170  match word : P.O. Number:  | distance : 0.****************\n", "###  Billing Account ID:  degrees : 81  match word : P.O. Number:  | distance : 0.*****************\n", "###  ********  degrees : 35  match word : P.O. Number:  | distance : 0.*****************\n", "###  Customer Tax ID:  degrees : 83  match word : P.O. Number:  | distance : 0.*****************\n", "###  BILL TO:  degrees : 165  match word : P.O. Number:  | distance : 0.****************\n", "###  WellDyneRx, Inc.  degrees : 164  match word : P.O. Number:  | distance : 0.****************\n", "###  500 Eagles Landing Drive  degrees : 161  match word : P.O. Number:  | distance : 0.****************\n", "###  Lakeland FL 33810  degrees : 161  match word : P.O. Number:  | distance : 0.****************\n", "###  US  degrees : 161  match word : P.O. Number:  | distance : 0.****************\n", "###  Description  degrees : 151  match word : P.O. Number:  | distance : 0.****************\n", "###  Unit Price  degrees : 128  match word : P.O. Number:  | distance : 0.*****************\n", "###  Qty  degrees : 108  match word : P.O. Number:  | distance : 0.2528916241944418\n", "###  Proration Factor  degrees : 78  match word : P.O. Number:  | distance : 0.*****************\n", "###  Net Price  degrees : 44  match word : P.O. Number:  | distance : 0.26342652719858045\n", "###  Adobe Sign - Enterprise Site License Annual  degrees : 149  match word : P.O. Number:  | distance : 0.6495415406342572\n", "###  4.800 USD  degrees : 127  match word : P.O. Number:  | distance : 0.3667375667886841\n", "###  1500  degrees : 111  match word : P.O. Number:  | distance : 0.2958492839855136\n", "###  100.00%  degrees : 89  match word : P.O. Number:  | distance : 0.25984806684353806\n", "###  7,200.00 USD  degrees : 56  match word : P.O. Number:  | distance : 0.26538087712196123\n", "###  Check (applied 31-Jul-2019)  degrees : 150  match word : P.O. Number:  | distance : 0.6543990636208107\n", "###  -7,200.00 USD  degrees : 57  match word : P.O. Number:  | distance : 0.2759069936095102\n", "###  RECEIVED 06.29.29 RF  degrees : 118  match word : P.O. Number:  | distance : 0.4934009966794033\n", "###  Invoice Total  degrees : 87  match word : P.O. Number:  | distance : 0.43770723471027817\n", "###  $7,200.00 USD  degrees : 64  match word : P.O. Number:  | distance : 0.4490323124763467\n", "###  Payment by Check  degrees : 135  match word : P.O. Number:  | distance : 0.7804624769827139\n", "###  Payment by Wire or ACH  degrees : 102  match word : P.O. Number:  | distance : 0.5410711223660077\n", "###  Billing Questions  degrees : 69  match word : P.O. Number:  | distance : 0.*****************\n", "###  Adobe Inc.  degrees : 135  match word : P.O. Number:  | distance : 0.****************\n", "###  Beneficiary: Adobe Inc.  degrees : 103  match word : P.O. Number:  | distance : 0.****************\n", "###  <EMAIL>  degrees : 69  match word : P.O. Number:  | distance : 0.****************\n", "###  29322 Network Place  degrees : 133  match word : P.O. Number:  | distance : 0.****************\n", "###  Bank: JPMorgan Chase Bank, N.A.  degrees : 98  match word : P.O. Number:  | distance : 0.****************\n", "###  Chicago, IL 60673-1293  degrees : 132  match word : P.O. Number:  | distance : 0.****************\n", "###  Bank ABA: *********  degrees : 102  match word : P.O. Number:  | distance : 0.****************\n", "###  USA  degrees : 134  match word : P.O. Number:  | distance : 0.****************\n", "###  SWIFT: CHASUS33  degrees : 102  match word : P.O. Number:  | distance : 0.****************\n", "###  Bank account: *********  degrees : 100  match word : P.O. Number:  | distance : 0.****************\n", "###  Remittance information should be sent to  degrees : 124  match word : P.O. Number:  | distance : 0.****************\n", "###  <EMAIL>  degrees : 126  match word : P.O. Number:  | distance : 0.****************\n", "*************dist list ****************\n", "{0.****************: ('bf2863ec-5af4-4373-ad65-2c79a2371841', 60), 0.*****************: ('60f5c054-a3aa-4124-b385-1a315b44a969', 6), 0.*****************: ('5cee9f4c-7e8f-4bd5-a60d-9f0c4c6bc95b', 93), 0.056920341669256566: ('3b0a7251-2ae8-45ad-ae75-88d0f108031b', 12), 0.*****************: ('ba30348d-31ef-47ed-ae53-2f0d4a9a75b4', 79), 0.06***************: ('41b09a32-d264-4fe8-9624-96a33901f760', 18), 0.07***************: ('cde801ef-1b9b-4079-836c-c2c8acfc0212', 24), 0.*****************: ('********-9f58-4c08-8988-680a28953d5b', 90), 0.****************: ('31c9a15b-0f4a-4353-bc08-0c122e63b2f6', 22), 0.*****************: ('572822a7-b21d-4f1c-bc58-26a5222eb107', 81), 0.*****************: ('758e33ba-5f66-4c53-a0a5-7d536cbd7448', 35), 0.*****************: ('b7fa429e-dd21-4135-a9f3-e7b21e4e9412', 83), 0.*****************: ('135ef65f-8ea4-4271-a22e-4bb34363d9c9', 78)}\n", "Nearest matches  0.*****************   6   P.O. Number:   *********\n", "Nearest matches  0.056920341669256566   12   P.O. Number:   27-Jun-2020\n", "Nearest matches  0.06***************   18   P.O. Number:   Net Terms 30\n", "Nearest matches  0.07***************   24   P.O. Number:   27-Jul-2020\n", "Nearest matches  0.****************   60   P.O. Number:   Invoice Number:\n", "Nearest matches  0.****************   22   P.O. Number:   27-Jun-2020 - 26-Jun-2021\n", "Nearest matches  0.*****************   93   P.O. Number:   Invoice Date:\n", "Nearest matches  0.*****************   79   P.O. Number:   Payment Terms:\n", "Nearest matches  0.*****************   35   P.O. Number:   ********\n", "Nearest matches  0.*****************   90   P.O. Number:   Billing Period:\n", "Nearest matches  0.*****************   81   P.O. Number:   Billing Account ID:\n", "Nearest matches  0.*****************   83   P.O. Number:   Customer Tax ID:\n", "Nearest matches  0.*****************   78   P.O. Number:   Proration Factor\n", "dst list  5.*************** 6 P.O. Number: ********* conf  99.*************\n", "*************************\n", "regex match  *********\n", "indices  0.***************** ['*********'] 99.*************\n", "dst list  5.*************** 12 P.O. Number: 27-Jun-2020 conf  99.**************\n", "*************************\n", "regex match  27-Jun-2020\n", "indices  0.056920341669256566 ['27-Jun-2020'] 99.**************\n", "dst list  6.*************** 18 P.O. Number: Net Terms 30 conf  99.**************\n", "*************************\n", "regex match  30\n", "indices  0.06*************** ['30'] 99.**************\n", "dst list  7.*************** 24 P.O. Number: 27-Jul-2020 conf  99.**********6406\n", "*************************\n", "regex match  27-Jul-2020\n", "indices  0.07*************** ['27-Jul-2020'] 99.**********6406\n", "dst list  8.37373823445343 60 P.O. Number: Invoice Number: conf  99.4202651977539\n", "*************************\n", "dst list  8.68994084455855 22 P.O. Number: 27-Jun-2020 - 26-Jun-2021 conf  98.**************\n", "*************************\n", "regex match  27-Jun-2020\n", "indices  0.**************** ['27-Jun-2020'] 98.**************\n", "dst list  8.722158516247292 93 P.O. Number: Invoice Date: conf  99.70856475830078\n", "*************************\n", "dst list  9.286425594141347 79 P.O. Number: Payment Terms: conf  99.*************\n", "*************************\n", "dst list  9.928099119298272 35 P.O. Number: ******** conf  99.**************\n", "*************************\n", "regex match  ********\n", "indices  0.***************** ['********'] 99.**************\n", "dst list  10.889349634133792 90 P.O. Number: Billing Period: conf  99.**************\n", "*************************\n", "************* PO_no ********************\n", "match list  {0: (['*********'], 99.*************), 1: (['27-Jun-2020'], 89.**************), 2: (['30'], 79.**************), 3: (['27-Jul-2020'], 69.**********6406), 5: (['27-Jun-2020'], 48.**************), 8: (['********'], 19.**************2)}\n", "match list keys  [0, 1, 2, 3, 5, 8]\n", "PO_no  :  ['*********'] confidence : 99.*************\n", "top ....  0\n", "bottom......  0\n", "******************** Total ******************************\n", "word list \n", "['total due', 'total ', 'total ', 'balance due', 'balance ', 'balance ', 'TOTAL USD due', 'TOTAL USD ', 'TOTAL USD ', ' due', ' ', ' ']\n", "Best Match Found  None\n", "top ....  0\n", "bottom......  0\n", "******************** Issue_Date ******************************\n", "word list \n", "['issue ', 'issue date', 'issue ', ' ', ' date', ' ']\n", "Best Match Found  None\n", "top ....  0\n", "bottom......  0\n", "******************** bank_name ******************************\n", "word list \n", "['bank name', 'bank name name']\n", "Best Match Found  None\n", "top ....  0\n", "bottom......  0\n", "******************** account_number ******************************\n", "word list \n", "['account number']\n", "Best Match Found  None\n", "top ....  0\n", "bottom......  0\n", "******************** terms ******************************\n", "word list \n", "['terms ', 'terms ']\n", "Best Match Found  None\n", "top ....  0\n", "bottom......  0\n", "******************** ship_to ******************************\n", "word list \n", "['ship to ', 'ship to to', 'ship ', 'ship to']\n", "Best Match Found  None\n", "top ....  0\n", "bottom......  0\n", "******************** bill_to ******************************\n", "word list \n", "['bill to ', 'bill to to', 'bill ', 'bill to', 'Billing Address ', 'Billing Address to']\n", "--> bill to: bill to 93\n", "Best Match Found  ('BILL TO:', 0.****************, 0.*****************, 0.*****************, 0.*****************, (0.*****************, 0.*****************), 99.************)\n", "###  P.O. Number:  degrees : -14  match word : BILL TO:  | distance : 0.*****************\n", "###  Invoice Number:  degrees : -12  match word : BILL TO:  | distance : 0.4880396188819742\n", "###  *********  degrees : -10  match word : BILL TO:  | distance : 0.6168978341624125\n", "###  Invoice Date:  degrees : -11  match word : BILL TO:  | distance : 0.4847967295092372\n", "###  27-Jun-2020  degrees : -9  match word : BILL TO:  | distance : 0.6139658839710249\n", "###  Payment Terms:  degrees : -9  match word : BILL TO:  | distance : 0.4819539696658328\n", "###  Net Terms 30  degrees : -7  match word : BILL TO:  | distance : 0.6117258957081116\n", "###  A  degrees : -77  match word : BILL TO:  | distance : 0.*****************\n", "###  Due Date:  degrees : -8  match word : BILL TO:  | distance : 0.****************\n", "###  27-Jul-2020  degrees : -6  match word : BILL TO:  | distance : 0.****************\n", "###  Billing Period:  degrees : -6  match word : BILL TO:  | distance : 0.****************\n", "###  27-Jun-2020 - 26-Jun-2021  degrees : -5  match word : BILL TO:  | distance : 0.****************\n", "###  Adobe  degrees : -65  match word : BILL TO:  | distance : 0.057012092041849864\n", "###  Billing Account ID:  degrees : -5  match word : BILL TO:  | distance : 0.*****************\n", "###  ********  degrees : -4  match word : BILL TO:  | distance : 0.****************\n", "###  Customer Tax ID:  degrees : -3  match word : BILL TO:  | distance : 0.****************\n", "###  BILL TO:  degrees : 0  match word : BILL TO:  | distance : 0.*****************\n", "###  WellDyneRx, Inc.  degrees : 27  match word : BILL TO:  | distance : 0.*****************\n", "###  500 Eagles Landing Drive  degrees : 26  match word : BILL TO:  | distance : 0.*****************\n", "###  Lakeland FL 33810  degrees : 49  match word : BILL TO:  | distance : 0.****************\n", "###  US  degrees : 109  match word : BILL TO:  | distance : 0.*****************\n", "###  Description  degrees : 30  match word : BILL TO:  | distance : 0.*****************\n", "###  Unit Price  degrees : 12  match word : BILL TO:  | distance : 0.***************\n", "###  Qty  degrees : 10  match word : BILL TO:  | distance : 0.43434177583882067\n", "###  Proration Factor  degrees : 7  match word : BILL TO:  | distance : 0.5079596347382642\n", "###  Net Price  degrees : 6  match word : BILL TO:  | distance : 0.7088720739790572\n", "###  Adobe Sign - Enterprise Site License Annual  degrees : 44  match word : BILL TO:  | distance : 0.*****************\n", "###  4.800 USD  degrees : 18  match word : BILL TO:  | distance : 0.3113184124557542\n", "###  1500  degrees : 14  match word : BILL TO:  | distance : 0.41653257461981374\n", "###  100.00%  degrees : 12  match word : BILL TO:  | distance : 0.5051844366611205\n", "###  7,200.00 USD  degrees : 9  match word : BILL TO:  | distance : 0.6497279603504114\n", "###  Check (applied 31-Jul-2019)  degrees : 62  match word : BILL TO:  | distance : 0.*****************\n", "###  -7,200.00 USD  degrees : 10  match word : BILL TO:  | distance : 0.6480636826575576\n", "###  RECEIVED 06.29.29 RF  degrees : 32  match word : BILL TO:  | distance : 0.31031548723665203\n", "###  Invoice Total  degrees : 28  match word : BILL TO:  | distance : 0.5748224637948341\n", "###  $7,200.00 USD  degrees : 21  match word : BILL TO:  | distance : 0.737665510385199\n", "###  Payment by Check  degrees : 84  match word : BILL TO:  | distance : 0.35833834903053613\n", "###  Payment by Wire or ACH  degrees : 39  match word : BILL TO:  | distance : 0.****************\n", "###  Billing Questions  degrees : 26  match word : BILL TO:  | distance : 0.****************\n", "###  Adobe Inc.  degrees : 89  match word : BILL TO:  | distance : 0.*****************\n", "###  Beneficiary: Adobe Inc.  degrees : 42  match word : BILL TO:  | distance : 0.****************\n", "###  <EMAIL>  degrees : 27  match word : BILL TO:  | distance : 0.****************\n", "###  29322 Network Place  degrees : 84  match word : BILL TO:  | distance : 0.****************\n", "###  Bank: JPMorgan Chase Bank, N.A.  degrees : 41  match word : BILL TO:  | distance : 0.****************\n", "###  Chicago, IL 60673-1293  degrees : 83  match word : BILL TO:  | distance : 0.****************\n", "###  Bank ABA: *********  degrees : 44  match word : BILL TO:  | distance : 0.****************\n", "###  USA  degrees : 91  match word : BILL TO:  | distance : 0.*****************\n", "###  SWIFT: CHASUS33  degrees : 46  match word : BILL TO:  | distance : 0.****************\n", "###  Bank account: *********  degrees : 45  match word : BILL TO:  | distance : 0.****************\n", "###  Remittance information should be sent to  degrees : 79  match word : BILL TO:  | distance : 0.****************\n", "###  <EMAIL>  degrees : 85  match word : BILL TO:  | distance : 0.****************\n", "*************dist list ****************\n", "{0.*****************: ('4abc933a-c1ed-4a47-8d54-e71638efbb82', 27), 0.*****************: ('0e170e33-76dd-4dfc-ab75-883d5eb4639b', 26), 0.****************: ('05b72737-bb72-4b31-b446-a3ad6433b011', 49), 0.*****************: ('575efe09-8a0d-4854-a347-c734f2778dee', 30), 0.*****************: ('2ef05ce3-6b5c-43fa-9216-970e96a5b60b', 44), 0.*****************: ('0e0d32eb-6130-4ab2-a97a-1aaff56a4499', 62)}\n", "Nearest matches  0.*****************   27   BILL TO:   WellDyneRx, Inc.\n", "Nearest matches  0.*****************   26   BILL TO:   500 Eagles Landing Drive\n", "Nearest matches  0.****************   49   BILL TO:   Lakeland FL 33810\n", "Nearest matches  0.*****************   30   BILL TO:   Description\n", "Nearest matches  0.*****************   44   BILL TO:   Adobe Sign - Enterprise Site License Annual\n", "Nearest matches  0.*****************   62   BILL TO:   Check (applied 31-Jul-2019)\n", "dst list  5.641482941513323 27 BILL TO: WellDyneRx, Inc. conf  98.46751403808594\n", "*************************\n", "regex match  WellDyneRx Inc.\n", "indices  {'id': 6, 'name': 'string', 'description': '', 'regex': '[a-zA-Z0-9].*'} ['WellDyneRx Inc.'] 98.46751403808594\n", "dst list  5.904021717311883 26 BILL TO: 500 Eagles Landing Drive conf  99.81892395019531\n", "*************************\n", "regex match  500 Eagles Landing Drive\n", "indices  {'id': 6, 'name': 'string', 'description': '', 'regex': '[a-zA-Z0-9].*'} ['500 Eagles Landing Drive'] 99.81892395019531\n", "dst list  6.48911938253047 49 BILL TO: Lakeland FL 33810 conf  99.64898681640625\n", "*************************\n", "regex match  Lakeland FL 33810\n", "indices  {'id': 6, 'name': 'string', 'description': '', 'regex': '[a-zA-Z0-9].*'} ['Lakeland FL 33810'] 99.64898681640625\n", "dst list  11.135711337507528 30 BILL TO: Description conf  98.80026245117188\n", "*************************\n", "regex match  Description\n", "indices  {'id': 6, 'name': 'string', 'description': '', 'regex': '[a-zA-Z0-9].*'} ['Description'] 98.80026245117188\n", "dst list  12.414807349326578 44 BILL TO: Adobe Sign - Enterprise Site License Annual conf  99.13524627685547\n", "*************************\n", "regex match  Adobe Sign - Enterprise Site License Annual\n", "indices  {'id': 6, 'name': 'string', 'description': '', 'regex': '[a-zA-Z0-9].*'} ['Adobe Sign - Enterprise Site License Annual'] 99.13524627685547\n", "dst list  13.609853263732475 62 BILL TO: Check (applied 31-Jul-2019) conf  99.3712158203125\n", "*************************\n", "regex match  Check (applied 31-Jul-2019)\n", "indices  {'id': 6, 'name': 'string', 'description': '', 'regex': '[a-zA-Z0-9].*'} ['Check (applied 31-Jul-2019)'] 99.3712158203125\n", "************* bill_to ********************\n", "match list  {0: (['WellDyneRx Inc.'], 98.46751403808594), 1: (['500 Eagles Landing Drive'], 89.81892395019531), 2: (['Lakeland FL 33810'], 79.64898681640625), 3: (['Description'], 68.80026245117188), 4: (['Adobe Sign - Enterprise Site License Annual'], 59.13524627685547), 5: (['Check (applied 31-Jul-2019)'], 49.3712158203125)}\n", "match list keys  [0, 1, 2, 3, 4, 5]\n", "bill_to  :  ['WellDyneRx Inc.'] confidence : 98.46751403808594\n", "top ....  0\n", "bottom......  0\n", "******************** balance_due ******************************\n", "word list \n", "['balance due']\n", "Best Match Found  None\n", "JSON File Written ...\n", "3\n"]}, {"name": "stdout", "output_type": "stream", "text": ["{'I': 0.89534324, 'NI': 0.10465671}\n", "fetching Boto Response .....\n", "\n", "\n", "== FOUND KEY : VALUE pairs ===\n", "\n", "[1, 2, 3, 4, 5, 6, 7, 8, 17, 18, 19, 20, 21]\n", "regex_id  [1, 2, 3, 4]\n", "regex_id  [6]\n", "regex_id  [6]\n", "top ....  0\n", "bottom......  0\n", "******************** Invoice_No ******************************\n", "word list \n", "['invoice no', 'invoice no.', 'invoice id', 'invoice #', 'invoice number', 'invoice: no', 'invoice: no.', 'invoice: id', 'invoice: #', 'invoice: number', 'Invoice ID no', 'Invoice ID no.', 'Invoice ID id', 'Invoice ID #', 'Invoice ID number', ' no', ' no.', ' id', ' #', ' number']\n", "--> invoice invoice # 88\n", "Best Match Found  ('Invoice', 0.7274447679519653, 0.03485369682312012, 0.9092110395431519, 0.03485369682312012, (0.8183279037475586, 0.03485369682312012), 99.75592803955078)\n", "###  TM  degrees : 178  match word : Invoice  | distance : 0.5175881950762932\n", "###  blackbaud  degrees : 179  match word : Invoice  | distance : 0.8540869724692065\n", "###  Invoice  degrees : 0  match word : Invoice  | distance : 0.18176627159118652\n", "###  power your passion  degrees : 173  match word : Invoice  | distance : 0.7310542815061822\n", "###  Invoice number: 91943311  degrees : 165  match word : Invoice  | distance : 0.41211785786814703\n", "###  Site ID: 70171  degrees : 158  match word : Invoice  | distance : 0.3410839431116471\n", "###  2000 Daniel Island Drive, Charleston SC 29492-7541  degrees : 172  match word : Invoice  | distance : 0.8504441275146886\n", "###  Quote number: Q-00767183  degrees : 153  match word : Invoice  | distance : 0.4108073653426619\n", "###  PO Number:  degrees : 154  match word : Invoice  | distance : 0.39431567002399553\n", "###  <PERSON>  degrees : 167  match word : Invoice  | distance : 0.8104598326943605\n", "###  Sold to:  degrees : 154  match word : Invoice  | distance : 0.4438324430571132\n", "###  <PERSON><PERSON><PERSON><PERSON> Williamson, Inc.  degrees : 133  match word : Invoice  | distance : 0.34957769996957283\n", "###  T<PERSON><PERSON><PERSON> Williamson, Inc.  degrees : 165  match word : Invoice  | distance : 0.8141819992472912\n", "###  6120 S Yale Ave  degrees : 133  match word : Invoice  | distance : 0.3569042646250945\n", "###  6120 S Yale Ave  degrees : 164  match word : Invoice  | distance : 0.8180515916250306\n", "###  Tulsa OK 74136-4234  degrees : 125  match word : Invoice  | distance : 0.3660091141400483\n", "###  Tulsa OK 74136-4234  degrees : 161  match word : Invoice  | distance : 0.8222947231101486\n", "###  Invoice total (USD): $31,874.96  degrees : 151  match word : Invoice  | distance : 0.9195085652861716\n", "###  Invoice date: 08/26/2020  degrees : 108  match word : Invoice  | distance : 0.4636276329701547\n", "###  Due date: 09/25/2020  degrees : 147  match word : Invoice  | distance : 0.8385165062853068\n", "###  Invoice number: 91943311  degrees : 110  match word : Invoice  | distance : 0.5066203433638441\n", "###  Payment terms: Net 30 days  degrees : 146  match word : Invoice  | distance : 0.9068134889709858\n", "###  Customer number: 1000206510  degrees : 109  match word : Invoice  | distance : 0.****************\n", "###  Subscription  degrees : 148  match word : Invoice  | distance : 0.9704087322121834\n", "###  CSRConnect Cause Cards  degrees : 144  match word : Invoice  | distance : 0.9853778178956671\n", "###  1,875.00  degrees : 82  match word : Invoice  | distance : 0.****************\n", "###  09/26/2020 - 09/25/2021  degrees : 143  match word : Invoice  | distance : 0.998005273948566\n", "###  CSRConnect Core  degrees : 142  match word : Invoice  | distance : 1.0246192033130568\n", "###  12,500.00  degrees : 83  match word : Invoice  | distance : 0.****************\n", "###  09/26/2020 - 09/25/2021  degrees : 139  match word : Invoice  | distance : 1.0382315932180874\n", "###  CSRConnect Donation Manager Global  degrees : 133  match word : Invoice  | distance : 1.067103380153323\n", "###  3,125.04  degrees : 84  match word : Invoice  | distance : 0.6231701474546067\n", "###  09/26/2020 - 09/25/2021  degrees : 136  match word : Invoice  | distance : 1.082278690052399\n", "###  CSRConnect Donation Mgr Matching Gifts  degrees : 129  match word : Invoice  | distance : 1.1134407910860853\n", "###  1,875.00  degrees : 85  match word : Invoice  | distance : 0.7000175524774247\n", "###  09/26/2020 - 09/25/2021  degrees : 132  match word : Invoice  | distance : 1.1303778374189026\n", "###  CSRConnect Donation Mgr Payroll Deduct  degrees : 126  match word : Invoice  | distance : 1.1644012349833999\n", "###  1,875.00  degrees : 85  match word : Invoice  | distance : 0.7771016269692219\n", "###  09/26/2020 - 09/25/2021  degrees : 130  match word : Invoice  | distance : 1.1815538650556223\n", "###  CSRConnect Volunteer Manager  degrees : 126  match word : Invoice  | distance : 1.2173015777653617\n", "###  1,874.92  degrees : 86  match word : Invoice  | distance : 0.8546712782689623\n", "###  Page 1 of 2  degrees : 87  match word : Invoice  | distance : 0.9282561504404835\n", "*************dist list ****************\n", "{}\n", "************* Invoice_No ********************\n", "match list  {}\n", "match list keys  []\n", "top ....  0\n", "bottom......  0\n", "******************** PO_no ******************************\n", "word list \n", "['po no', 'po number', 'po order', 'po ', 'purchase no', 'purchase number', 'purchase order', 'purchase ', 'P.O. no', 'P.O. number', 'P.O. order', 'P.O. ', ' no', ' number', ' order', ' ']\n", "--> po number: po number 95\n", "Best Match Found  ('PO Number:', 0.530454695224762, 0.1445281207561493, 0.6390674114227295, 0.1445281207561493, (0.5847610533237457, 0.1445281207561493), 99.74386596679688)\n", "###  TM  degrees : -151  match word : PO Number:  | distance : 0.2674382919867638\n", "###  blackbaud  degrees : -164  match word : PO Number:  | distance : 0.5926080181623172\n", "###  Invoice  degrees : -25  match word : PO Number:  | distance : 0.14085111434467767\n", "###  power your passion  degrees : -169  match word : PO Number:  | distance : 0.46181014278137383\n", "###  Invoice number: 91943311  degrees : -58  match word : PO Number:  | distance : 0.15000744855024498\n", "###  Site ID: 70171  degrees : -34  match word : PO Number:  | distance : 0.07405360835972327\n", "###  2000 Daniel Island Drive, Charleston SC 29492-7541  degrees : -175  match word : PO Number:  | distance : 0.5773099721655662\n", "###  Quote number: Q-00767183  degrees : -20  match word : PO Number:  | distance : 0.13191981187919338\n", "###  PO Number:  degrees : 0  match word : PO Number:  | distance : 0.10861271619796753\n", "###  <PERSON>  degrees : 175  match word : PO Number:  | distance : 0.5285808461720135\n", "###  Sold to:  degrees : 151  match word : PO Number:  | distance : 0.1537139339800862\n", "###  <PERSON><PERSON><PERSON><PERSON> Williamson, Inc.  degrees : 19  match word : PO Number:  | distance : 0.05923418549033134\n", "###  <PERSON><PERSON><PERSON><PERSON> Williamson, Inc.  degrees : 172  match word : PO Number:  | distance : 0.5303743216558564\n", "###  6120 S Yale Ave  degrees : 33  match word : PO Number:  | distance : 0.07077740118070004\n", "###  6120 S Yale Ave  degrees : 170  match word : PO Number:  | distance : 0.5325138858179743\n", "###  Tulsa OK 74136-4234  degrees : 33  match word : PO Number:  | distance : 0.08545531237166262\n", "###  Tulsa OK 74136-4234  degrees : 166  match word : PO Number:  | distance : 0.535276093490772\n", "###  Invoice total (USD): $31,874.96  degrees : 149  match word : PO Number:  | distance : 0.6280562866085302\n", "###  Invoice date: 08/26/2020  degrees : 60  match word : PO Number:  | distance : 0.22545232522174904\n", "###  Due date: 09/25/2020  degrees : 143  match word : PO Number:  | distance : 0.****************\n", "###  Invoice number: 91943311  degrees : 67  match word : PO Number:  | distance : 0.26111313036935624\n", "###  Payment terms: Net 30 days  degrees : 142  match word : PO Number:  | distance : 0.615738189111695\n", "###  Customer number: 1000206510  degrees : 69  match word : PO Number:  | distance : 0.2957645897491218\n", "###  Subscription  degrees : 145  match word : PO Number:  | distance : 0.6799984398984585\n", "###  CSRConnect Cause Cards  degrees : 140  match word : PO Number:  | distance : 0.6960455966303171\n", "###  1,875.00  degrees : 50  match word : PO Number:  | distance : 0.*****************\n", "###  09/26/2020 - 09/25/2021  degrees : 139  match word : PO Number:  | distance : 0.7097204553936094\n", "###  CSRConnect Core  degrees : 136  match word : PO Number:  | distance : 0.7389850558924099\n", "###  12,500.00  degrees : 55  match word : PO Number:  | distance : 0.****************\n", "###  09/26/2020 - 09/25/2021  degrees : 133  match word : PO Number:  | distance : 0.7540720971681875\n", "###  CSRConnect Donation Manager Global  degrees : 125  match word : PO Number:  | distance : 0.7863241770034874\n", "###  3,125.04  degrees : 60  match word : PO Number:  | distance : 0.****************\n", "###  09/26/2020 - 09/25/2021  degrees : 129  match word : PO Number:  | distance : 0.8031366426425272\n", "###  CSRConnect Donation Mgr Matching Gifts  degrees : 120  match word : PO Number:  | distance : 0.8381937157624905\n", "###  1,875.00  degrees : 63  match word : PO Number:  | distance : 0.619702659761948\n", "###  09/26/2020 - 09/25/2021  degrees : 125  match word : PO Number:  | distance : 0.8569840518155606\n", "###  CSRConnect Donation Mgr Payroll Deduct  degrees : 117  match word : PO Number:  | distance : 0.8950546437043946\n", "###  1,875.00  degrees : 66  match word : PO Number:  | distance : 0.6935069794437361\n", "###  09/26/2020 - 09/25/2021  degrees : 122  match word : PO Number:  | distance : 0.9141613520263793\n", "###  CSRConnect Volunteer Manager  degrees : 117  match word : PO Number:  | distance : 0.9540572113800947\n", "###  1,874.92  degrees : 68  match word : PO Number:  | distance : 0.7684188558406869\n", "###  Page 1 of 2  degrees : 71  match word : PO Number:  | distance : 0.831584867191794\n", "*************dist list ****************\n", "{0.05923418549033134: ('46773b65-4f6a-4dce-bcb3-2409c741e582', 19), 0.07077740118070004: ('f9927e68-a25f-4e7d-abda-e2de2bf80c1a', 33), 0.08545531237166262: ('d53653be-3326-4bec-bf87-61e2b16e20fc', 33), 0.22545232522174904: ('16c7db2f-1814-4cac-a5c2-0cf06916182d', 60)}\n", "Nearest matches  0.05923418549033134   19   PO Number:   T.D. Williamson, Inc.\n", "Nearest matches  0.07077740118070004   33   PO Number:   6120 S Yale Ave\n", "Nearest matches  0.08545531237166262   33   PO Number:   Tulsa OK 74136-4234\n", "Nearest matches  0.22545232522174904   60   PO Number:   Invoice date: 08/26/2020\n", "dst list  5.923418549033134 19 PO Number: T.D<PERSON> Williamson, Inc. conf  98.14119720458984\n", "*************************\n", "dst list  7.077740118070004 33 PO Number: 6120 S Yale Ave conf  99.38163757324219\n", "*************************\n", "regex match  6120\n", "indices  0.07077740118070004 ['6120'] 99.38163757324219\n", "dst list  8.545531237166262 33 PO Number: Tulsa OK 74136-4234 conf  99.81226348876953\n", "*************************\n", "regex match  74136-4234\n", "indices  0.08545531237166262 ['74136-4234'] 99.81226348876953\n", "dst list  22.545232522174903 60 PO Number: Invoice date: 08/26/2020 conf  99.74874877929688\n", "*************************\n", "regex match  08/26/2020\n", "indices  0.22545232522174904 ['08/26/2020'] 99.74874877929688\n", "************* PO_no ********************\n", "match list  {1: (['6120'], 89.38163757324219), 2: (['74136-4234'], 79.81226348876953), 3: (['08/26/2020'], 69.74874877929688)}\n", "match list keys  [1, 2, 3]\n", "PO_no  :  ['6120'] confidence : 89.38163757324219\n", "top ....  0\n", "bottom......  0\n", "******************** Total ******************************\n", "word list \n", "['total due', 'total ', 'total ', 'balance due', 'balance ', 'balance ', 'TOTAL USD due', 'TOTAL USD ', 'TOTAL USD ', ' due', ' ', ' ']\n", "Best Match Found  None\n", "top ....  0\n", "bottom......  0\n", "******************** Issue_Date ******************************\n", "word list \n", "['issue ', 'issue date', 'issue ', ' ', ' date', ' ']\n", "Best Match Found  None\n", "top ....  0\n", "bottom......  0\n", "******************** bank_name ******************************\n", "word list \n", "['bank name', 'bank name name']\n", "Best Match Found  None\n", "top ....  0\n", "bottom......  0\n", "******************** account_number ******************************\n", "word list \n", "['account number']\n", "Best Match Found  None\n", "top ....  0\n", "bottom......  0\n", "******************** terms ******************************\n", "word list \n", "['terms ', 'terms ']\n", "Best Match Found  None\n", "top ....  0\n", "bottom......  0\n", "******************** ship_to ******************************\n", "word list \n", "['ship to ', 'ship to to', 'ship ', 'ship to']\n", "Best Match Found  None\n", "top ....  0\n", "bottom......  0\n", "******************** bill_to ******************************\n", "word list \n", "['bill to ', 'bill to to', 'bill ', 'bill to', 'Billing Address ', 'Billing Address to']\n", "Best Match Found  None\n", "top ....  0\n", "bottom......  0\n", "******************** balance_due ******************************\n", "word list \n", "['balance due']\n", "Best Match Found  None\n", "JSON File Written ...\n", "4\n"]}, {"name": "stdout", "output_type": "stream", "text": ["{'I': 0.********, 'NI': 0.********}\n", "fetching Boto Response .....\n", "\n", "\n", "== FOUND KEY : VALUE pairs ===\n", "\n", "[1, 2, 3, 4, 5, 6, 7, 8, 17, 18, 19, 20, 21]\n", "regex_id  [1, 2, 3, 4]\n", "regex_id  [6]\n", "regex_id  [6]\n", "top ....  0\n", "bottom......  0\n", "******************** Invoice_No ******************************\n", "word list \n", "['invoice no', 'invoice no.', 'invoice id', 'invoice #', 'invoice number', 'invoice: no', 'invoice: no.', 'invoice: id', 'invoice: #', 'invoice: number', 'Invoice ID no', 'Invoice ID no.', 'Invoice ID id', 'Invoice ID #', 'Invoice ID number', ' no', ' no.', ' id', ' #', ' number']\n", "Best Match Found  None\n", "top ....  0\n", "bottom......  0\n", "******************** PO_no ******************************\n", "word list \n", "['po no', 'po number', 'po order', 'po ', 'purchase no', 'purchase number', 'purchase order', 'purchase ', 'P.O. no', 'P.O. number', 'P.O. order', 'P.O. ', ' no', ' number', ' order', ' ']\n", "Best Match Found  None\n", "top ....  0\n", "bottom......  0\n", "******************** Total ******************************\n", "word list \n", "['total due', 'total ', 'total ', 'balance due', 'balance ', 'balance ', 'TOTAL USD due', 'TOTAL USD ', 'TOTAL USD ', ' due', ' ', ' ']\n", "Best Match Found  None\n", "top ....  0\n", "bottom......  0\n", "******************** Issue_Date ******************************\n", "word list \n", "['issue ', 'issue date', 'issue ', ' ', ' date', ' ']\n", "Best Match Found  None\n", "top ....  0\n", "bottom......  0\n", "******************** bank_name ******************************\n", "word list \n", "['bank name', 'bank name name']\n", "Best Match Found  None\n", "top ....  0\n", "bottom......  0\n", "******************** account_number ******************************\n", "word list \n", "['account number']\n", "Best Match Found  None\n", "top ....  0\n", "bottom......  0\n", "******************** terms ******************************\n", "word list \n", "['terms ', 'terms ']\n", "Best Match Found  None\n", "top ....  0\n", "bottom......  0\n", "******************** ship_to ******************************\n", "word list \n", "['ship to ', 'ship to to', 'ship ', 'ship to']\n", "Best Match Found  None\n", "top ....  0\n", "bottom......  0\n", "******************** bill_to ******************************\n", "word list \n", "['bill to ', 'bill to to', 'bill ', 'bill to', 'Billing Address ', 'Billing Address to']\n", "Best Match Found  None\n", "top ....  0\n", "bottom......  0\n", "******************** balance_due ******************************\n", "word list \n", "['balance due']\n", "Best Match Found  None\n", "JSON File Written ...\n"]}], "source": ["fetch_response=True\n", "\n", "from Invoice.Forms_Extraction import *\n", "json_files=[]\n", "Invoice_obj_list={}\n", "for key in images_list:\n", "    ikey=key\n", "    print(key)\n", "    inv_info={}\n", "    IsInvoice=True\n", "    inv_page_path=images_list[key]\n", "    I_res_1=predictInvoice(TFmodel2,inv_page_path)\n", "    print(I_res_1)\n", "    inv_info[\"isinvoice\"]=I_res_1[\"I\"]\n", "    inv_info[\"path\"]=inv_page_path\n", "    if I_res_1[\"I\"]>=0.60:\n", "        json_path=inv_page_path.replace(\"png\",\"json\")\n", "        \n", "        with open(inv_page_path, \"rb\") as image_file:\n", "            base64_encoded_string = base64.b64encode(image_file.read())\n", "\n", "            data_str =reader(inv_page_path)  \n", "            feature_field=['TABLES','FORMS']\n", "            #################### PERFORMING OCR ##################\n", "\n", "            if fetch_response==True:\n", "                print(\"fetching Boto Response .....\")\n", "                response = client.analyze_document(Document={'Bytes': data_str}, FeatureTypes=feature_field)\n", "\n", "                with open(json_path, 'w') as fp:\n", "                    json.dump(response, fp)\n", "                json_files.append(json_path)\n", "            \n", "            inv_info[\"json_path\"]=json_path\n", "            #Extract key value pairs \n", "            raw_dict=get_raw_values(json_files[key])\n", "            \n", "            field_list={}\n", "            #converting k,v to nearest labels \n", "            for key, value in raw_dict.items():\n", "                check_res=field_match.get_field_label(key,w_list,match_threshold)\n", "                key=key.replace(\":\",\"\").replace(\",\",\"\")\n", "                \n", "                \n", "                if check_res[1]==None:\n", "                    data={key.strip():value[0].strip(),\"confidence_level\":value[1]}\n", "                    field_list[key.strip()]=value[0].strip() \n", "                    \n", "                else:\n", "                    data={check_res[1].strip():value[0].strip(),\"confidence_level\":value[1]}\n", "                    field_list[check_res[1].strip()]=value[0].strip()\n", "                        \n", "            #field_list.append(data)\n", "            \n", "            form_fields = []\n", "            table_dimensions=[0,0]\n", "\n", "            form_fields=getFormFieldsbyDistance(config_name,form_fields,json_path,table_dimensions)\n", "            #print(\"form fields :\",form_fields)\n", "            \n", "            \n", "            #t1=Invoice_obj_list[0]['form_fields']\n", "            #t2=Invoice_obj_list[0]['fields']\n", "\n", "            for i in form_fields:\n", "                key=list(i.keys())[0]\n", "                if not(key in field_list):\n", "                    field_list[key]=i[key]\n", "            \n", "            \n", "            \n", "            inv_info[\"fields\"]=field_list  \n", "            #inv_info[\"form_fields\"]=form_fields  \n", "                    \n", "                    \n", "                    \n", "                    \n", "            \n", "                    \n", "                   \n", "                    \n", "            \n", "            \"\"\" \n", "            op_image=markheader_main(json_path,inv_page_path,inv_page_path.replace(\".png\",\"-markup.png\"),config_name )\n", "            \n", "            if op_image!=None:\n", "                df=getlineitems(op_image,json_path,config_name)\n", "                c1=list(df.head())\n", "                head_str=' '.join(c1)\n", "                inv_info[\"header\"]=head_str\n", "                \n", "            \"\"\"    \n", "                \n", "\n", "            print(\"JSON File Written ...\")\n", "    else:\n", "        IsInvoice=False\n", "        \n", "    Invoice_obj_list[ikey]=inv_info\n", "            \n", "            \n", "                    \n", "        \n", "    "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#to be used only once \n", "json_files=['/home/<USER>/invoice/inv-collection/image_splitter2/SOU_Carahsoft_IN859177-merged.pdf_0.json',\n", " '/home/<USER>/invoice/inv-collection/image_splitter2/SOU_Carahsoft_IN859177-merged.pdf_1.json',\n", " '/home/<USER>/invoice/inv-collection/image_splitter2/SOU_Carahsoft_IN859177-merged.pdf_2.json'] \n", "#json_files=[]\n", "#json_files.append(\"/home/<USER>/invoice/inv-collection/image_splitter/SOU_Carahsoft_IN859177.pdf_1.json\")\n", "#json_files.append(\"/home/<USER>/invoice/inv-collection/image_splitter/SOU_Carahsoft_IN859177.pdf_0.json\")"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["['/home/<USER>/invoice/inv-collection/image_splitter3/SOU_Carahsoft_IN859177-merged-merged.pdf_0.json',\n", " '/home/<USER>/invoice/inv-collection/image_splitter3/SOU_Carahsoft_IN859177-merged-merged.pdf_1.json',\n", " '/home/<USER>/invoice/inv-collection/image_splitter3/SOU_Carahsoft_IN859177-merged-merged.pdf_2.json',\n", " '/home/<USER>/invoice/inv-collection/image_splitter3/SOU_Carahsoft_IN859177-merged-merged.pdf_3.json',\n", " '/home/<USER>/invoice/inv-collection/image_splitter3/SOU_Carahsoft_IN859177-merged-merged.pdf_4.json']"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["json_files"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t1=Invoice_obj_list[0]['form_fields']\n", "t2=Invoice_obj_list[0]['fields']\n", "\n", "for i in t1:\n", "    key=list(i.keys())[0]\n", "    if not(key in t2):\n", "        t2[key]=i[key]\n", "        \n", "        "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Invoice_obj_list[0]['fields']"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0\n", "1\n", "match is  100\n", "overall match score  100.0\n", "2\n", "match is  47\n", "overall match score  47.0\n", "3\n", "match is  24\n", "overall match score  24.0\n", "4\n", "match is  0\n", "overall match score  0.0\n"]}], "source": ["#keywords to compare\n", "fields=[\"Invoice_No\"]\n", "\n", "#Invoice Summary \n", "#Invoice_obj_list\n", "from fuzzywuzzy import fuzz\n", "\n", "cluster_list=[]\n", "cluster=[]\n", "for key,value in Invoice_obj_list.items():\n", "    print(key)\n", "    if key>0:\n", "        I1=Invoice_obj_list[key-1]\n", "        I2=Invoice_obj_list[key]\n", "        if value[\"isinvoice\"]<0.60:#i.e its not an invoice \n", "            cluster.append(key)\n", "        else : #i.e document is invoice:\n", "            match_score=0\n", "            absent_field=0\n", "            for i in fields:\n", "                match=0\n", "                try:\n", "                    if  (i in I1['fields']) and (i in I2['fields'] ):\n", "                        match=fuzz.ratio(I1['fields'][i],I2['fields'][i])\n", "                    elif not (i in I2['fields'] ):\n", "                        #i.e field is not present in secound Invoice\n", "                        absent_field=absent_field+1\n", "                    \n", "                except Exception as e:\n", "                    print(\"error \",e)\n", "                    pass\n", "                    \n", "                print(\"match is \",match)\n", "                match_score=match_score+match\n", "            \n", "            print(\"overall match score \",(match_score/len(fields)) )\n", "            if (match_score/len(fields))>80:\n", "                    #invoices are same\n", "                    cluster.append(key)\n", "            elif absent_field==len(fields):\n", "                cluster.append(key)\n", "            else:\n", "                cluster_list.append(cluster)\n", "                cluster=[]\n", "                cluster.append(key)\n", "    else:\n", "        cluster.append(key)\n", "            \n", "if len(cluster)>0:\n", "    cluster_list.append(cluster)\n", "                        \n", "            \n", "            \n", "            \n", "            \n"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"text/plain": ["[[0, 1], [2], [3, 4]]"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["cluster_list"]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [{"data": {"text/plain": ["'[[{\"id\": 0, \"base64\": \"/home/<USER>/invoice/inv-collection/image_splitter2/SOU_Carahsoft_IN859177-merged.pdf_0.png\"}, {\"id\": 1, \"base64\": \"/home/<USER>/invoice/inv-collection/image_splitter2/SOU_Carahsoft_IN859177-merged.pdf_1.png\"}], [{\"id\": 0, \"base64\": \"/home/<USER>/invoice/inv-collection/image_splitter2/SOU_Carahsoft_IN859177-merged.pdf_2.png\"}]]'"]}, "execution_count": 58, "metadata": {}, "output_type": "execute_result"}], "source": ["#convert cluster into \n", "import base64\n", "import json\n", "\n", "\n", "#with open(imagename, \"rb\") as image_file:\n", "#    encoded_string = base64.b64encode(image_file.read())\n", "\n", "cl=[]\n", "for i in cluster_list:\n", "    l=[]\n", "    for index,val in enumerate(i):\n", "        #print(index,images_list[val])\n", "        d={\"invoiceid\":index,\"invoicedata\":images_list[val]}\n", "        l.append(d)\n", "    cl.append(l)\n", "result={\"invocies\":cl}    \n", "json.dumps(cl)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Invoice_obj_list[2]['fields']['Invoice_No']=213\n", "Invoice_obj_list[2]\n", "#Invoice_obj_list[2]['header']=\"one , two\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from Invoice.Forms_Extraction import *\n", "temp_dict=get_raw_values(json_files[0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from ml_li_items.mlextractLI.markheaders import *\n", "from ml_li_items.mlextractLI.pytorchmodelload import * "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["config_name=\"global_config\" \n", "inv_page_path=images_list[1]\n", "json_path=json_files[1]\n", "op_image=markheader_main(json_path,inv_page_path,inv_page_path.replace(\".png\",\"-markup.png\"),config_name )\n", "op_image"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df=getlineitems(op_image,json_path,config_name)\n", "df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c1=list(df.head())\n", "' '.join(c1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c2=list(df.head())\n", "' '.join(c2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from fuzzywuzzy import fuzz\n", "fuzz.ratio(' '.join(c1),' '.join(c2))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Django Shell-Plus", "language": "python", "name": "django_extensions"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.4"}}, "nbformat": 4, "nbformat_minor": 2}