from invoice_tracker.models import * 

def invoice_tracker_save(config_name,
         api,
         headers,
         method,
         scanned_pages,
         total_pages,
         client_host,
         filename,
         username,
         agent
         ):
    
    #save instance id 
    c = InvoiceCounter(api = api, headers = headers, method = method,scanned_pages=scanned_pages,client_host=client_host,filename=filename,config_name=config_name,total_pages=total_pages,response='None',username=username,agent=agent)
    c.save()
    
    return True 