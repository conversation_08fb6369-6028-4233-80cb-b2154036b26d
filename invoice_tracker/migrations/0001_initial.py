# Generated by Django 2.2.5 on 2023-04-21 10:16

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='InvoiceCounter',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False)),
                ('api', models.CharField(help_text='API URL', max_length=1024)),
                ('config_name', models.Char<PERSON>ield(max_length=300)),
                ('headers', models.TextField()),
                ('method', models.CharField(db_index=True, max_length=300)),
                ('filename', models.CharField(max_length=500)),
                ('scanned_pages', models.IntegerField()),
                ('total_pages', models.IntegerField()),
                ('client_host', models.TextField()),
                ('response', models.TextField(default=None)),
                ('added_on', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'API Log',
                'verbose_name_plural': 'API Logs',
                'db_table': 'InvoiceCounter',
            },
        ),
    ]
