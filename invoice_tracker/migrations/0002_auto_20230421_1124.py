# Generated by Django 2.2.5 on 2023-04-21 11:24

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('invoice_tracker', '0001_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='invoicecounter',
            options={'verbose_name': 'Single Invoice Counter', 'verbose_name_plural': 'Single Invoice Counter'},
        ),
        migrations.AddField(
            model_name='invoicecounter',
            name='agent',
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name='invoicecounter',
            name='username',
            field=models.TextField(blank=True),
        ),
        migrations.AlterField(
            model_name='invoicecounter',
            name='client_host',
            field=models.TextField(blank=True),
        ),
        migrations.AlterField(
            model_name='invoicecounter',
            name='response',
            field=models.TextField(blank=True, default=None),
        ),
    ]
