from django.db import models

# Create your models here.

from django.db import models
from Invoice.models import Configuration
from jsonfield import <PERSON><PERSON><PERSON><PERSON>
# Create your models here.
class InvoiceCounter(models.Model):
   id = models.BigAutoField(primary_key=True)
   api = models.CharField(max_length=1024, help_text='API URL')
   config_name=models.CharField(max_length=300)
   headers = models.TextField()
   method = models.CharField(max_length=300, db_index=True)
   filename= models.CharField(max_length=500)
   scanned_pages=models.IntegerField()
   total_pages=models.IntegerField()
   client_host = models.TextField(blank=True)
   agent= models.TextField(blank=True)
   username= models.TextField(blank=True)
   response = models.TextField(default=None,blank=True)
   added_on = models.DateTimeField(auto_now_add=True)
   
   def __str__(self):
      return self.api

   class Meta:
      db_table = 'InvoiceCounter'
      verbose_name = 'Single Invoice Counter'
      verbose_name_plural = 'Single Invoice Counter'