from django.shortcuts import render
from rest_framework.views import APIView
from django.http import HttpResponse
import glob
import os
import time
from AMS.extract_settings import directories
from os import path
import json
from InvoiceSplit.utils import *

# Create your views here.
class Clean(APIView):
    def get(self, request):
        #cl = CustomLogger("-")
        total_files_deleted = 0
        trying_to_delete = ""
        request_id = log_to_db(request, 'Clean', 'clean', None)
        message = "ok"
        try:
            for directory in directories:
                for item in glob.glob(directory+"*"):
                    seconds = time.time() - (1 * 60 * 60)
                    if seconds >= os.stat(item).st_ctime:
                        if path.exists(item):
                            trying_to_delete = directory+item
                            os.remove(item)
                            total_files_deleted = total_files_deleted + 1
            res = {"files_deleted":total_files_deleted,"message": message}   
        except:
            message = "failed to delete "+trying_to_delete
            res = {"files_deleted":total_files_deleted,"message": message}   

        #cl.print("version is :"+str(version),request.user)
        update_status(request_id, 200, error_msg=message)

        return HttpResponse(json.dumps(res,indent=4, sort_keys=True, default=str))