
#Instantiate Database Logger
import logging
from pathlib import Path
import torch
from transformers import T5Tokenizer, T5ForConditionalGeneration, T5Config
import spacy 
db_logger = logging.getLogger('db')



header_items=["item name","sno","particular","description","rate","quantity","qty","total","amount","man day","hsn","price","uom","unit price","total","man days","grade","man month","sr.no","item","extended amount","net amount","part no","usage country","qty ordered"]
tax_items=["gst","cgst","igst","ugst","cess","sales","RECEIVED"]
total_items=["total","total amount","totalamount","subtotal"]
item_name=["description","service","item","particular"]

header_items_type={"sno":["num"],
              "particular":["string"],
              "description":["string"],
              "rate":["num"],
              "product":["string"],
              "quantity":["num"],
              "qty":["num"],
              "total":["num"],
              "amount":["num"],
              "man day":["num"],
              "hsn":["num"],
              "price":["num"],
              "uom":["num"],
              "unit price":["num"],
               "man days":["num"],
              "grade":[""],
              "man month":[""],
              "item":[""] }

alpha_neumeric_regex = "([$a-zA-Z0-9/-]+[0-9]+)|([0-9+]+[$0-9a-zA-Z_/-]+)$"

string =[{"TEXT": {"REGEX": "([a-zA-Z]+)"}}]
num = [{'LIKE_NUM': True}]
 
#Instantiate Database Logger 

#Importing tensorflow libraries for prediction 
from tensorflow.keras.models import load_model
from tensorflow.keras.preprocessing import image
import numpy as np
from Invoice.ML.TFPredictions import *

import tensorflow as tf
from object_detection.utils import ops as utils_ops
from object_detection.utils import label_map_util
from Invoice.Vendor_Field_Filters import *
from AMS.settings import BASE_DIR
from tensorflow.keras.models import load_model
import tensorflow_hub as hub

op_path_=None
TF_path_=None
 


#Spacy Model That should be Shares 
#spacy model load path 


#nlp=spacy.load("en_core_web_md")




#Extract Raw Values from Form 
extract_raw=True

#Invoice-NonInvoice classification Model
#TFmodel2 = load_model('../model.h5')

run_mode='local'
TF_path_=None
address_model_path=None

if run_mode=='local':
    spacy_model_path="spacy_model/spacy_model/Jul_20"
    #op_path_="inference_graph_13_sept"
    TF_path_="model.h5"
    address_model_path="address_spacy_model/updated_model2"
    op_path_="inference_graph_13_sept"
    directories = ["Invoice/pdf/","extract_check/"]
    inv_no_model_dir = "models/"
    #TF_path_="model.h5"
else:
    spacy_model_path="../spacy_model/spacy_model/Jul_20"
    #op_path_="../inference_graph_13_sept"
    #TF_path_="../model.h5"
    address_model_path="../address_spacy_model/updated_model2"
    op_path_="../inference_graph_13_sept"
    TF_path_="../model.h5"
    directories = ["../Invoice/pdf/","../extract_check/"]
    inv_no_model_dir = "../models/"

nlp=spacy.load(spacy_model_path)
nlp2=spacy.load(address_model_path)


print("NER 2 models laoded.....  ")


TFmodel2 = load_model(TF_path_, custom_objects={'KerasLayer':hub.KerasLayer})
print("TENSORFLOW INVOICE DETECTION MODEL LOADED......")


output_directory = op_path_  #'../inference_graph_13_sept'

print('base diretory '+BASE_DIR)
base_path = BASE_DIR+'/Invoice/pdf/'
# invoice_path=base_path+'invoice.png'
# image_path=invoice_path
# json_path=base_path+'invoice.json'
csv_path = base_path+'csv'
base_extract = BASE_DIR+'/extract/'

#Invoice-NonInvoice classification Model
#TFmodel2 = load_model('../model.h5')
#TFmodel2 = load_model(TF_path_)
#print("TENSORFLOW MODEL LOADED......")
print("object detection API ",TFmodel2)

tf.keras.backend.clear_session()
TF_OB_model = tf.saved_model.load(f'{output_directory}/saved_model')
print("TENSORFLOW Object Detection API Model Loaded ......")





#Image Required in Response
image_in_response=False







#Model Name - later we can specify model per vendor  
model_path=""
#Beam Threshhold
threshold = 0.4

#Load vendor List if Present
vendor_list=""
#Load Company List if Present
company_list=""







#types of Fields
#Alpha - Alphneumric 
#Date  - Only Date 
#Float - Strictly Float 
#Money - Money Field
#String - pure charcters with no numbers
#Int  - Integer with no Float Fields Involved 


#Search Staregy 
# search can be peformed either of the ways 
#0- fuzz -> this keyword tries to search with fuzzy logic , for fuzzy logic search you should specify head and tail
#1  regex -> this keyword searches data with regular expression , must specify array of regular expressions that can be used . 
#search_stragegy=[""]

#Invoice Extraction Fields 

######################Invoice No ###############################
invoice_no={}
invoice_no["name"]="Invoice_No"
invoice_no["type"]="Alpha"
invoice_no["strategy"]="fuzz"
invoice_no["head"]=["billing invoice","billing","invoice"]
invoice_no["tail"]=["no","no.","id","#","number"]

#For any specific type of regular expression
#invoice_no["regex"]=""
#NER Field Name in ML Model 
#invoice_no["entity_name"]="INVOICE_NO"

######################Invoice No ###############################

######################PO No ###############################
po_no={}
po_no["name"]="PO_no"
po_no["type"]="Alpha"
po_no["strategy"]="fuzz"
po_no["head"]=["po","purchase"]
po_no["tail"]=["no","number","order"]

#For any specific type of regular expression
#invoice_no["regex"]=""
#NER Field Name in ML Model 
#invoice_no["entity_name"]="INVOICE_NO"

######################PO No ###############################

######################Total###############################
total={}
total["name"]="Total"
total["type"]="Numstring"
total["head"]=["total","balance"]
total["strategy"]="fuzz"
total["tail"]=["","due"]
total["regex"]=["(total|Total|TOTAL)\s*(w*)[0-9][0-9.]*[0-9]",
"^(total|Total|TOTAL)\s*(w*)\d",
"^(total|Total|TOTAL)\s*(w*)\d+\.\d{2}",
"(total|Total|TOTAL)\s*[\w].*\s*[:,\s]\s*\d+\.\d{2}"]

#total["type"]="Alpha"
#total["head"]=["po","purchase"]
#total["tail"]=["no","number","order"]

#For any specific type of regular expression
#invoice_no["regex"]=""
#NER Field Name in ML Model 
#invoice_no["entity_name"]="INVOICE_NO"

######################Total ###############################

###################### Issue Date###############################
issue_date={}
issue_date["name"]="Issue_Date"
issue_date["type"]="Date"
issue_date["strategy"]="fuzz"
issue_date["head"]=["issue","invoice","date"]
issue_date["tail"]=["date",""]

#For any specific type of regular expression
#invoice_no["regex"]=""
#NER Field Name in ML Model 
#invoice_no["entity_name"]="INVOICE_NO"

######################Issue Date ###############################

###################### Due Date###############################
due_date={}
due_date["name"]="Due_Date"
due_date["type"]="Date"
due_date["head"]=["due"]
due_date["tail"]=["date"]
due_date["strategy"]="fuzz"

#For any specific type of regular expression
#invoice_no["regex"]=""
#NER Field Name in ML Model 
#invoice_no["entity_name"]="INVOICE_NO"

######################Due Date ###############################

###################### Tax Rate ###############################
tax_rate={}
tax_rate["name"]="tax_rate"
tax_rate["type"]="Numstring"
tax_rate["head"]=["tax","tax rate"]
tax_rate["tail"]=["rate"]
tax_rate["strategy"]="fuzz"

#For any specific type of regular expression
#invoice_no["regex"]=""
#NER Field Name in ML Model 
#invoice_no["entity_name"]="INVOICE_NO"

######################TAX RATE ###############################

###################### Tax  ###############################
tax={}
tax["name"]="tax"
tax["type"]="Numstring"
tax["head"]=["tax"]
tax["tail"]=[""]
tax["strategy"]="fuzz"


#For any specific type of regular expression
#invoice_no["regex"]=""
#NER Field Name in ML Model 
#invoice_no["entity_name"]="INVOICE_NO"

###################### TAX ###############################



###################### Payment Terms  ###############################
payment_term={}
payment_term["name"]="payment_term"
payment_term["type"]="Alpha"
payment_term["strategy"]="fuzz"
payment_term["head"]=["payment","payment term"]
payment_term["tail"]=["term"]
payment_term["rule"]={  "one": [{'LIKE_NUM': True, 'ENT_TYPE': 'DATE'},{'ENT_TYPE': 'DATE'}]  }

#For any specific type of regular expression
#invoice_no["regex"]=""
#NER Field Name in ML Model 
#invoice_no["entity_name"]="INVOICE_NO"

###################### Payment Terms ###############################

###################### Account Number ###############################
account_number={}
account_number["name"]="account_number"
account_number["type"]="Numstring"
account_number["head"]=["account"]
account_number["tail"]=["number"]
account_number["strategy"]="fuzz"


#For any specific type of regular expression
#invoice_no["regex"]=""
#NER Field Name in ML Model 
#invoice_no["entity_name"]="INVOICE_NO"

###################### Account Number ###############################

###################### Bank Name Temp  ###############################
bank_name={}

bank_name["name"]="bank_name"
bank_name["type"]="Rule"
bank_name["strategy"]="fuzz"
bank_name["head"]=["bank","bank name"]
bank_name["tail"]=["name"]
bank_name["rule"]={  "one": [{'LOWER': 'bank'},{'LOWER': 'of'},{'OP': '?'}],
                     "two": [{'IS_ASCII': True},{'LOWER': 'bank'}]    }



###################### Bank Name Temp ###############################

###################### Total - Flat Field  ###############################

total_flat={}

total_flat["name"]="total"
total_flat["type"]="Numstring"

total_flat["rule"]={  "one": [{'LOWER': 'total'},{'LOWER': 'amount','OP':'?'},{'OP': '*'},{'LIKE_NUM': True}]  }



###################### Total - Flat Field ###############################



#############################################KeyWords For Extraction ###########################################
#Add any additional keywords that will be used while extraction



#############################################KeyWords For Extraction ###########################################


#Fields to be Extracted 
#extract_fields=[invoice_no,po_no,issue_date,due_date,tax_rate,tax,payment_term,account_number,bank_name]
#Reserved Keywords
reserved_keywords=["bill to","ship to"]

#field that will be searched with flat lines 
#flat_fields=[total_flat]


########################################### DATA TYPE FOR HEADER ITEMS #############################################



############################### Vendor Settings #########################################

#check which of the vendors are near 
vendor_identifier=["service provider","branch office",""]
vendor_rules={"one":[{'LOWER': 'for'},{'OP': '?'},{'ENT_TYPE': 'ORG','OP':'*'}]  }

############################### Vendor Settings #########################################

print(f'models directory: {inv_no_model_dir}')

t5_tokenizer = T5Tokenizer.from_pretrained('google/flan-t5-base')
inv_no_detector = T5ForConditionalGeneration(config=T5Config.from_pretrained('google/flan-t5-base'))
inv_no_detector.load_state_dict(torch.load(inv_no_model_dir+ 'inv_no_detector.pth'))
inv_no_detector.eval()
__text = "ARA LOGISTICS CO., LTD INVOICE 220401T-ASC Room505 Market Hall, BIFM Building, #35 Wonyang-ro, Seo-gu, Busan, Korea 49277 www.aralogistic.com DATE BILL TO PLEASE PAY DUE DATE AMERICAN SEAFOOD COMPANY, LLC 30/04/2022 25/05/2022 W23,150,600 TITLE MCC & CONTAINER HANDLING FOR APR TRIPLICATE ACTIVITY Qty AMOUNT WS2201A03(DISCHARGING) RE STUFFING 14/40FT Kw 5,880,000 ON/OFF 14/40FT Kw 840,000 SURVEY 2 DAYS Kw 900,000 CLEANING 14/40FT Kw 420,000 TEMPORARY STORAGE ON/OFF 22/40FT Kw 1,320,000 WS2202 (LOADING) ON/OFF 24/20FT,40FT Kw 1,440,000 HANDLING CHARGE 24/20FT,40FT Kw 2,400,000 WHARFAGE & PORT SECURITY 16/40FT Kw 144,480 8/20FT Kw 36,120 PR2202A04-1ST (DISCHARGING) RE STUFFING 1/40FT Kw 420,000 ON/OFF 1/40FT Kw 60,000 SURVEY 1 DAY Kw 450,000 CLEANING 1/40FT Kw 30,000 TEMPORARY STORAGE FOR ON/OFF 4/40FT Kw 240,000 PR2202A04-2ND (DISCHARGING) ON/OFF 17/40FT Kw 1,020,000 CLEANING 17/40FT Kw 510,000 TEMPORARY STORAGE FOR ON/OFF 9/40FT Kw 540,000 MONTHLY STORAGE FEE IN APR. Kw 6,500,000 REMARK PORT : BUSAN, KOREA VESSEL : WS2201A03 , WS2202 / PR2202A04 TOTAL DUE Kw 23,150,600 Andy Andy Hwang / Tramper Team THANK YOU. Please pay by wire Payment. Account Name : ARA Logistics Co.,Ltd. Bank Name : KB KOOKMIN BANK -Busan International Finance Center Branch SWIFT Code : CZNBKRSEXXX USD A/C : 891268-00-000648 KW A/C : 891237-00-000309 "
with torch.no_grad():
    __input_ids = t5_tokenizer(__text + '</s>', return_tensors='pt', max_length=512, truncation=True, padding='max_length').input_ids
    __outputs = inv_no_detector.generate(__input_ids)
    __expected = t5_tokenizer.decode(__outputs[0], skip_special_tokens=True)
    assert '220401T-ASC' == __expected
print('Invoice number detector loaded...')