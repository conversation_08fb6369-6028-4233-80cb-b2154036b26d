"""AMS URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/2.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path
from django.urls import path, include
from Invoice.views import InvoiceRest
from ml_li_items.views import NewInvoiceAPI
from InvoiceSplit.urls import *
from Clean.urls import *

from Duplicate_Configuration.urls import *

from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView
urlpatterns = [
    path('admin/', admin.site.urls),
    path('', include('Invoice.urls')),
    path('', include('ml_li_items.urls')),
    path('', include('InvoiceSplit.urls')),
    path('', include('check_invoice.urls')),
    path('', include('Duplicate_Configuration.urls')),
    path('', include('Clean.urls')),
    path('api/token/', TokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('api/refresh/', TokenRefreshView.as_view(), name='token_refresh')

]+ static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
