

#Threshhold For INVOCIE
threshold = 0.4

#Regular Expressions for Alpha Neumric 
alpha_neumeric_regex = "([$a-zA-Z0-9/-]+[0-9]+)|([0-9+]+[$0-9a-zA-Z_/-]+)$"





###################### TOTAL ###############################
check_total={}
check_total["name"]="total"
check_total["type"]="string"
check_total["pattern"]= [ [{'LOWER':'total'},{'OP':'*'},{'LIKE_NUM': True,'OP':'?'}],
                           [{'LOWER':'subtotal'},{'OP':'*'},{'LIKE_NUM': True,'OP':'?'}]    ]
check_total["weight"]="3"
###################### TOTAL ###############################

###################### INVOICE ###############################
check_invoice={}
check_invoice["name"]="invoice"
check_invoice["type"]="string"
check_invoice["pattern"]= [ [{'LOWER':'tax','OP':'?'}, {'LOWER':'invoice'}],
                            [ {'LOWER':'bill'}] ]
check_invoice["weight"]="3"
###################### INVOICE ###############################

###################### Amount In Words ###############################
check_inwords={}
check_inwords["name"]="amount_in_words"
check_inwords["type"]="string"
check_inwords["pattern"]= [ [{'LOWER':'total','OP':'?'},{'OP':'*'},{'LOWER':'amount'},{'OP':'*'},{'LOWER':'in','OP':'?'},{'OP':'*'},{"TEXT": {"REGEX": "(word|Word|WORD*)"}}] ]
check_inwords["weight"]="3"
###################### INVOICE ###############################


###################### Invoice Number ###############################
check_invoice_no={}
check_invoice_no["name"]="invoice_no"
check_invoice_no["type"]="string"
check_invoice_no["pattern"]= [ [{'LOWER':'invoice'},{'OP':'*'},{"TEXT": {"REGEX": alpha_neumeric_regex}}],
                                [{"TEXT": {"REGEX": "(bill|Bill|BILL*)"}},{'OP':'?'},{'OP':'?'},{'OP':'?'},{'OP':'?'},{'OP':'?'},{"TEXT": {"REGEX": alpha_neumeric_regex}}],
                                [{'LOWER':'invoice'}, {'OP':'*'},{"TEXT": {"REGEX": "(no|NO|No|num|Num|NUM*)"} } ]   ]
check_invoice_no["weight"]="4"
###################### Invoice Number ###############################

"""
###################### Bill Number ###############################
check_bill_no={}
check_bill_no["name"]="bill_no"
check_bill_no["type"]="string"
check_bill_no["pattern"]= [ [{"TEXT": {"REGEX": "(bill|Bill|BILL*)"}},{'OP':'?'},{'OP':'?'},{'OP':'?'},{'OP':'?'},{'OP':'?'},{"TEXT": {"REGEX": alpha_neumeric_regex}}] ]
check_bill_no["weight"]="4"
###################### Bill Number ###############################
"""

###################### Bill TO ###############################
check_bill_to={}
check_bill_to["name"]="bill_to"
check_bill_to["type"]="string"
check_bill_to["pattern"]= [ [{'LOWER':'bill' } ,{'LOWER':'to'}  ],
                            [{'LOWER':'billing' } ,{'LOWER':'address'}  ]    ]
check_bill_to["weight"]="4"
###################### Bill TO ###############################


###################### Shipping TO ###############################
check_ship_to={}
check_ship_to["name"]="ship_to"
check_ship_to["type"]="string"
check_ship_to["pattern"]= [ [{'LOWER':'ship' } ,{'LOWER':'to'}  ],
                            [{'LOWER':'shipping' } ,{'LOWER':'address'}  ]    ]
check_ship_to["weight"]="4"
###################### Bill TO ###############################


###################### Invoice DATE ###############################
check_invoice_date={}
check_invoice_date["name"]="invoice_date"
check_invoice_date["type"]="string"
check_invoice_date["pattern"]=[ [{'LOWER':'invoice','OP':'?' },{"TEXT": {"REGEX": "(bill|Bill|BILL*)"},'OP':'?'} ,{'LOWER':'date','OP':'?'}  ] ]
check_invoice_date["weight"]="2"
###################### INVOICE DATE ###############################







#Fields to be Extracted 
check_fields=[check_total,check_invoice_no,check_bill_to,check_invoice_date ,check_invoice, check_inwords ]
