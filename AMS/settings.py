"""
Django settings for AMS project.

Generated by 'django-admin startproject' using Django 2.2.

For more information on this file, see
https://docs.djangoproject.com/en/2.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/2.2/ref/settings/
"""


import os
from datetime import timedelta
import pymysql 
pymysql.install_as_MySQLdb()
# Build paths inside the project like this: os.path.join(BASE_DIR, ...)
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/2.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = '$64wb0)9=she97lz7zq2lnh57#yxqwqm(+qqkvdclum3ijt2cz'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True


DATA_UPLOAD_MAX_MEMORY_SIZE = 15728640

ALLOWED_HOSTS = ['*']



# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'Invoice',
    'check_invoice',
    'InvoiceSplit',
    'rest_framework',
    'rest_framework.authtoken',
    'db_logger',
    'django_extensions',
    'modelclone',
    'feedback',
    'simple_history',
    'Duplicate_Configuration',
    'invoice_tracker',
    'metric_tracker',
    "django.contrib.humanize"  
]
#SILENCED_SYSTEM_CHECKS = ['mysql.E001']

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    #'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'simple_history.middleware.HistoryRequestMiddleware'
   
]


ROOT_URLCONF = 'AMS.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.i18n',
                'django.template.context_processors.media',
                'django.template.context_processors.static',
                'django.template.context_processors.tz',
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'AMS.wsgi.application'


# Database
# https://docs.djangoproject.com/en/2.2/ref/settings/#databases

"""
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': os.path.join(BASE_DIR, 'db.sqlite3'),
    }
}
"""
"""
########## Local database settings
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'invflow_config',
        'USER': 'admin',
        'PASSWORD': 'Aavenir1',
        'HOST': '127.0.0.1',
        'PORT': '3307',
    }
}

####database settings for DEV Server
"""
#"""
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'invflow_config',
        'USER': 'admin',
        'PASSWORD': 'Aavenir1',
        'HOST':'invoiceflow-dev-2703.cdj1hls4yp1w.us-east-2.rds.amazonaws.com',
        #'invoiceflow-dev.cvqzpm1grph6.us-east-2.rds.amazonaws.com'
        #'invoiceflow-dev.cdj1hls4yp1w.us-east-2.rds.amazonaws.com',
         
        'PORT': '3306',
    },
    'counterDB': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'usagemetrics',
        'USER': 'admin',
        'PASSWORD': 'Aavenir1',
        'HOST': 'usagemetrics-dev.cdj1hls4yp1w.us-east-2.rds.amazonaws.com',
        'PORT': '3306',
    }
}
#""" 

#######prod replica 
#""" 
DATABASE_ROUTERS = ['InvoiceSplit.dbRouter.App1DBRouter', 'metric_tracker.dbRouter.App2DBRouter']
"""
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'invflow_config',
        'USER': 'admin',
        'PASSWORD': 'Aavenir1',
        'HOST': 'invoiceflow-prod.cdj1hls4yp1w.us-east-2.rds.amazonaws.com',
        'PORT': '3306',
    },
    'counterDB': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'usagemetrics',
        'USER': 'admin',
        'PASSWORD': 'Aavenir1',
        'HOST': 'usagemetrics-prod.cdj1hls4yp1w.us-east-2.rds.amazonaws.com',
        'PORT': '3306',
    }
}
"""
###### prod replica 

# Password validation
# https://docs.djangoproject.com/en/2.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
    'rest_framework_simplejwt.authentication.JWTAuthentication',
    ],
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',
    ]
}
# Internationalization
# https://docs.djangoproject.com/en/2.2/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_L10N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/2.2/howto/static-files/

STATIC_URL = '/static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'static')
'''
STATICFILES_DIRS = [
    os.path.join(BASE_DIR, "static"),
]
'''
STATIC_ROOT = os.path.join(BASE_DIR, 'static')
STATIC_URL = '/static/'
SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(minutes=30),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=1),
    'ROTATE_REFRESH_TOKENS': False,
    'BLACKLIST_AFTER_ROTATION': True,

    'ALGORITHM': 'HS256',
    'SIGNING_KEY': SECRET_KEY,
    'VERIFYING_KEY': None,
    'AUDIENCE': None,
    'ISSUER': None,

    'AUTH_HEADER_TYPES': ('Bearer',),
    'USER_ID_FIELD': 'id',
    'USER_ID_CLAIM': 'user_id',

    'AUTH_TOKEN_CLASSES': ('rest_framework_simplejwt.tokens.AccessToken',),
    'TOKEN_TYPE_CLAIM': 'token_type',

    'JTI_CLAIM': 'jti',

    'SLIDING_TOKEN_REFRESH_EXP_CLAIM': 'refresh_exp',
    'SLIDING_TOKEN_LIFETIME': timedelta(minutes=30),
    'SLIDING_TOKEN_REFRESH_LIFETIME': timedelta(days=5),
}

#DATA_UPLOAD_MAX_MEMORY_SIZE = 5242880

LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '%(levelname)s %(asctime)s %(module)s %(process)d %(thread)d %(message)s'
        },
        'simple': {
            'format': '%(levelname)s %(asctime)s %(message)s'
        },
    },
    'handlers': {
        'db_log': {
            'level': 'DEBUG',
            'class': 'db_logger.db_log_handler.DatabaseLogHandler'
        },
    },
    'loggers': {
        'db': {
            'handlers': ['db_log'],
            'level': 'DEBUG'
        }
    }
}

#Silences system checks 
SILENCED_SYSTEM_CHECKS = ['mysql.E001']