from fuzzywuzzy import fuzz
import cv2 
from Invoice.DbConfig import *


def get_liheader_fields(config_name):
    header_dict_list=get_header_fields(config_name)

    header_items_list=[]
    for i in header_dict_list:

        if len(i["field_names"])>0:
            header_items_list.extend(i["field_names"])
    return header_items_list
        
def h_invoice_arr(text_cordinates):
    
    #find all horizontal lines :
    line_dict={}
    for item in text_cordinates:
        line_no=(item[1])
        if (line_no in line_dict)  :
            line_dict[line_no].append(item)
        else:
            line_dict[line_no] = [item]

    invoice_arr=[]
    invoice_arr_dict={}
    rows=0
    for item in line_dict.keys():
        cols=0
        temp=[]
        for i in line_dict[item]:
            temp.append(i[0])
            cols=cols+1
        rows=rows+1
        invoice_arr.append((temp,line_dict[item]))
        invoice_arr_dict[line_dict[item][0][5]]=temp
        
    return invoice_arr,invoice_arr_dict

def match_line_items(invoice_arr,config_name):
    
    count=0
    header_match_counts={}
    header_items_list=get_liheader_fields(config_name)

    for row in invoice_arr:
        match_count=0
        for col in row[0]:
            for item in header_items_list:
                ratio=fuzz.ratio(str(item).lower().strip(),str(col).lower().strip())
                #ratio2=distance.get_jaro_distance(str(item).lower().strip(),str(v).lower().strip(), winkler=True, scaling=0.1)
                if ratio>87:
                    match_count=match_count+1
                    break
        if match_count>=2:
            header_match_counts[count]=match_count 
        count=count+1
    
    if len(header_match_counts)>0:
        maximum = max(header_match_counts.items(), key=lambda k: k[1])
        return invoice_arr,maximum[0]
    else:
        return None,0

def writeImage(img_info,img_name,op_image,points):
    
    image = cv2.imread(img_name)
    color=(0,0,255)

    # Line thickness of 2 px 
    thickness = 5
    
    # Using cv2.rectangle() method 
    # Draw a rectangle with blue line borders of thickness of 2 px 
    for p in points:
        image = cv2.rectangle(image, p[0], p[1], color, thickness) 

    cv2.imwrite(op_image,image)
    return op_image

def markheader_main(data,image,op_image,config_name):
    
    img_path=image
    text_cordinates=[] 
        
    for blocks in data['Blocks']:

        a=blocks["Geometry"]["Polygon"][0]['X']
        b=blocks["Geometry"]["Polygon"][0]['Y']

        c=blocks["Geometry"]["Polygon"][1]['X']
        d=blocks["Geometry"]["Polygon"][1]['Y']

        if (blocks["BlockType"]=='LINE' )  :
            text_cordinates.append((blocks["Text"], round(b*100), ((a+c)/2)*100,a*100, (c-a)*100,b,blocks ))
      
    #finding the horizontal lines 
    invoice_arr,invoice_arr_dict=h_invoice_arr(text_cordinates)
    res_inv_arr,count=match_line_items(invoice_arr,config_name)
    
    if res_inv_arr==None:
        return None 
    
    points=[]
    image_o = cv2.imread(image)
    height, width = image_o.shape[:2]
    
    for item in res_inv_arr[count][1]:
        #print(item[6]['Geometry'])
        start=(int(float(item[6]['Geometry']['Polygon'][0]['X'])*width),int(float(item[6]['Geometry']['Polygon'][0]['Y'])*height) )
        end=(int(float(item[6]['Geometry']['Polygon'][2]['X'])*width),int(float(item[6]['Geometry']['Polygon'][2]['Y'])*height) )

        points.append([start,end])
    
    return_image=writeImage(image_o.shape,img_path,op_image,points)
    return return_image