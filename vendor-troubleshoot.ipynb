{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["inv_page_path=\"/home/<USER>/invoice/invoice-flow/inv-flow-dvc/db-repo/aavenir.invoiceflow.ai/Invoice/pdf/4f8d1f03-ba79-44d4-9d3e-b88f0b466dca_0.png\"\n", "json_path=\"/home/<USER>/invoice/invoice-flow/inv-flow-dvc/db-repo/aavenir.invoiceflow.ai/Invoice/pdf/4f8d1f03-ba79-44d4-9d3e-b88f0b466dca_0invoice.json\"\n", "image=\"/home/<USER>/invoice/invoice-flow/inv-flow-dvc/db-repo/aavenir.invoiceflow.ai/Invoice/pdf/4f8d1f03-ba79-44d4-9d3e-b88f0b466dca_0.png\"\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import os \n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\""]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": [" form_fields = []"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "== FOUND KEY : VALUE pairs ===\n", "\n", " temp_dict found  {'HST: ': ['30.44 ', 97.5], 'Subtotal: ': ['234.19 ', 97.0], 'Total Due: ': ['264.63 ', 95.0], 'Customer ': ['Number:********* ', 94.5], 'Invoice ': ['Date:03/23/2022 ', 93.0], 'Service: ': ['Weekly - WE ', 89.5], 'B.N.#: ': ['10076 7490 RT0001 ', 85.5], 'Bill To: ': ['T.D. WILLIAMSON <PERSON> 8300 LAWSON ROAD UNIT #101 MILTON, ON L9T 0A4 ', 79.5], 'Account ': ['', 66.5], 'Route:12 ': ['PETER MACISAAOMAR 2 5 2022 ', 57.0], 'Deliver To: ': ['T.D. WILLIAMSON 8300 LAWSON RD #1 MILTON, ON L9T 0A4 ', 45.0], 'Email: ': ['<EMAIL> ', 39.5], 'Please Remit Payment to : ': ['Canadian Linen and Uniform Service Corp., Box 51073, RPO Tyndall, Winnipeg, MB, R2X 3C6 ', 32.0], 'Fax: ': ['(************* ', 30.5], 'Ph: ': ['(416)849-5100 ', 26.0], 'Page 1 of 2 ': ['', 11.5]}\n"]}], "source": ["from Invoice.Forms_Extraction import *\n", "AI_Fields={}\n", "temp_dict=get_raw_values(json_path)\n", "print(\" temp_dict found \",temp_dict)\n", "AI_Fields.update(temp_dict)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["import django\n", "django.setup()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["NER 2 models laoded.....  \n", "TENSORFLOW INVOICE DETECTION MODEL LOADED......\n", "base diretory /home/<USER>/invoice/invoice-flow/inv-flow-dvc/db-repo/aavenir.invoiceflow.ai\n", "object detection API  <tensorflow.python.keras.engine.functional.Functional object at 0x7f8b3a232fd0>\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "TENSORFLOW Object Detection API Model Loaded ......\n"]}], "source": ["from Invoice.LineSearch import *\n", "from AMS.extract_settings import *\n", "from Invoice.ExtractUtils import *\n", "from Invoice.FieldMatch import * "]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1, 2, 4, 5, 6, 8, 17, 18, 19, 20, 21, 31]\n", "regex_id  [6]\n", "regex_id  [6]\n", "regex_id  [7]\n", "regex_id  [8]\n", "match threshold  95\n"]}], "source": ["config_name=\"global_config\"\n", "field_match=FieldMatch()\n", "db_extrtaction_list=get_extraction_list(config_name,None)\n", "w_list=field_match.cerate_word_list(db_extrtaction_list)\n", "\n", "match_threshold=get_ai_fieldmatch_threshold(config_name)\n", "print(\"match threshold \",match_threshold)\n", "\n", "raw_dict=AI_Fields\n", "for key, value in raw_dict.items():\n", "        #print(key,' -- ',value)\n", "        #checking duplicate for extracted_field\n", "        #print(\"Match Field  checking \",key)\n", "\n", "        check_res=field_match.get_field_label(key,w_list,match_threshold) \n", "\n", "        key=key.replace(\":\",\"\").replace(\",\",\"\")\n", "        search_key=None\n", "        if check_res[1]==None:\n", "            data={key.strip():value[0].strip(),\"confidence_level\":value[1]}\n", "            search_key=key.strip()\n", "        else:\n", "            data={check_res[1].strip():value[0].strip(),\"confidence_level\":value[1]}\n", "            search_key=check_res[1].strip()\n", "\n", "        #form_fields.append(data)\n", "\n", "        #check for any duplicate present in the list \n", "\n", "        for d in form_fields:\n", "            l = [item.lower().strip() for item in list(d.keys())]\n", "            if search_key.lower().strip() in l:\n", "                print(\"Duplicate found \",d , \" :for \",check_res[1],\" , Match_score :\")\n", "                form_fields.remove(d)\n", "\n", "\n", "                #form_fields.append(d)\n", "                \"\"\"\n", "                temp_val=d[check_res[1]]\n", "                d[check_res[1]+\"_duplicate\"]=temp_val\n", "                t=d\n", "                del d[check_res[1]]\n", "                form_fields.append(t)\n", "                \"\"\"\n", "\n", "\n", "        form_fields.append(data)\n", "        #temp_data[key]=value"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["from Invoice.Vendor_Extraction import *\n", "from Invoice.DbConfig import *"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["from AMS.extract_settings import *"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["length of vendorfields  1\n", "before sorting  [('Please Remit Payment to', 100)]\n", "after sorting  [('Please Remit Payment to', 100)]\n", "matched key  ('Please Remit Payment to', 100)\n", "*******************entity***************************\n", "['T.D. Williamson Canada ULC', 'pallet express', 'Canadian Linen & Uniform Service Corp.']\n", "Text  Canadian Linen and Uniform Service Corp., Box 51073, RPO Tyndall, Winnipeg, MB, R2X 3C6\n", "Key Value Vendor name  None\n", "companylist ......  ['mufg Investor Services', 'td williamson', 'welldyne', 'armanagement', 'bit', 'EDMONTON AB T6N 1N4', 'e-hps', 'K8B', 'MUFG CAPITAL ANALYTICS', 'na', '2249 S. 2700 W.']\n", "vendor list ...  ['mufg Investor Services', 'td williamson', 'welldyne', 'armanagement', 'bit', 'EDMONTON AB T6N 1N4', 'e-hps', 'K8B', 'MUFG CAPITAL ANALYTICS', 'na', '2249 S. 2700 W.']\n"]}, {"ename": "AttributeError", "evalue": "module 'tensorflow.keras.preprocessing.image' has no attribute 'read'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "\u001b[0;32m~/anaconda3/lib/python3.7/site-packages/PIL/Image.py\u001b[0m in \u001b[0;36mopen\u001b[0;34m(fp, mode)\u001b[0m\n\u001b[1;32m   2846\u001b[0m     \u001b[0;32mtry\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 2847\u001b[0;31m         \u001b[0mfp\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mseek\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;36m0\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   2848\u001b[0m     \u001b[0;32mexcept\u001b[0m \u001b[0;34m(\u001b[0m\u001b[0mAttributeError\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mio\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mUnsupportedOperation\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mAttributeError\u001b[0m: module 'tensorflow.keras.preprocessing.image' has no attribute 'seek'", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[0;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-11-327e29705f05>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[1;32m      3\u001b[0m \u001b[0;32mif\u001b[0m \u001b[0mkv_vendorname\u001b[0m\u001b[0;34m==\u001b[0m\u001b[0;32mNone\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      4\u001b[0m     \u001b[0mcompanylist\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mget_companynames\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mconfig_name\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m----> 5\u001b[0;31m     \u001b[0mven_res\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mgetVendors\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mTF_OB_model\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mimage\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mjson_path\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mcompanylist\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mconfig_name\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m      6\u001b[0m     \u001b[0;31m#vendor_match=getVendorbycsvMatch(header_list)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      7\u001b[0m     \u001b[0mdata\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;34m{\u001b[0m\u001b[0;34m\"vendor_name\"\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0mven_res\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;36m0\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\"confidence_level\"\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0mven_res\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;36m1\u001b[0m\u001b[0;34m]\u001b[0m \u001b[0;34m}\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/home/<USER>/invoice/invoice-flow/inv-flow-dvc/db-repo/aavenir.invoiceflow.ai/Invoice/Vendor_Extraction.py\u001b[0m in \u001b[0;36mgetVendors\u001b[0;34m(model, image, json_path, companylist, config_name)\u001b[0m\n\u001b[1;32m    306\u001b[0m     \u001b[0mprint\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m\"vendor list ... \"\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mvendor_list\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    307\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 308\u001b[0;31m     \u001b[0mim\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mImage\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mopen\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mimage\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    309\u001b[0m     \u001b[0mim_width\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mim_height\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mim\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0msize\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    310\u001b[0m     \u001b[0mprint\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m\"image dim \"\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mim_height\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mim_width\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/anaconda3/lib/python3.7/site-packages/PIL/Image.py\u001b[0m in \u001b[0;36mopen\u001b[0;34m(fp, mode)\u001b[0m\n\u001b[1;32m   2847\u001b[0m         \u001b[0mfp\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mseek\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;36m0\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   2848\u001b[0m     \u001b[0;32mexcept\u001b[0m \u001b[0;34m(\u001b[0m\u001b[0mAttributeError\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mio\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mUnsupportedOperation\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 2849\u001b[0;31m         \u001b[0mfp\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mio\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mBytesIO\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mfp\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mread\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   2850\u001b[0m         \u001b[0mexclusive_fp\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;32mTrue\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   2851\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mAttributeError\u001b[0m: module 'tensorflow.keras.preprocessing.image' has no attribute 'read'"]}], "source": ["kv_vendorname,kv_score=filter_vendor_fields(form_fields,config_name,nlp) \n", "print(\"Key Value Vendor name \",kv_vendorname)\n", "if kv_vendorname==None:\n", "    companylist=get_companynames(config_name)\n", "    ven_res=getVendors(TF_OB_model,image,json_path,companylist,config_name)\n", "    #vendor_match=getVendorbycsvMatch(header_list)\n", "    data={\"vendor_name\":ven_res[0],\"confidence_level\":ven_res[1] }\n", "    form_fields.append(data)\n", "else:\n", "    data={\"vendor_name\":kv_vendorname,\"confidence_level\":kv_score }\n", "    form_fields.append(data)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["config_name"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'companylist' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-10-79f2ccd0faa9>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[0;32m----> 1\u001b[0;31m \u001b[0mven_res\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mgetVendors\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mTF_OB_model\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mimage\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mjson_path\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mcompanylist\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mconfig_name\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;31mNameError\u001b[0m: name 'companylist' is not defined"]}], "source": ["ven_res=getVendors(TF_OB_model,image,json_path,companylist,config_name)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["gen_fields=get_General_entity(config_name)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'field': <ExtractField: global_extract_fields>,\n", "  'fieldtype': 'ORG',\n", "  'str': 'T<PERSON><PERSON><PERSON> Canada ULC',\n", "  'entity': 'T<PERSON><PERSON><PERSON> Williamson Canada ULC'},\n", " {'field': <ExtractField: global_extract_fields>,\n", "  'fieldtype': 'ORG',\n", "  'str': 'pallet express',\n", "  'entity': 'pallet express'},\n", " {'field': <ExtractField: global_extract_fields>,\n", "  'fieldtype': 'ORG',\n", "  'str': 'Canadian Linen and Uniform Service Corp.',\n", "  'entity': 'Canadian Linen and Uniform Service Corp.'}]"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["gen_fields"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["['LINEN & UNIFORM SERVICE']"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["spacy_getorg(\"LINEN & UNIFORM SERVICE\",config_name,gen_fields)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["text='LINEN & UNIFORM SERVICE'"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'getVendorbyMatch' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-9-3ff700abab68>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[0mconfig_name\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;34m\"global_config\"\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      2\u001b[0m \u001b[0mcompanylist\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mget_companynames\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mconfig_name\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m----> 3\u001b[0;31m \u001b[0mmatch_item\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mgetVendorbyMatch\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m\"LINEN & UNIFORM SERVICE\"\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mcompanylist\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mconfig_name\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mgen_fields\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;31mNameError\u001b[0m: name 'getVendorbyMatch' is not defined"]}], "source": ["config_name=\"global_config\"\n", "companylist=get_companynames(config_name)\n", "match_item=getVendorbyMatch([\"LINEN & UNIFORM SERVICE\"],companylist,config_name,gen_fields)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["''"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["company_list"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["res=[]\n", "entity=[]\n", "\n", "\n", "#gen_fields=get_General_entity(config_name)\n", "#print(\"gen fields \",gen_fields)\n", "\n", "pharase_matcher = PhraseMatcher(nlp.vocab)\n", "\n", "for item in gen_fields:\n", "    #print(\"item \",item)\n", "    if item['fieldtype']==\"ORG\":\n", "        #entity_str.append( (item['str'],item['entity'] ) )\n", "        entity.append(item['entity'])\n", "#print(\"entity \",entity)\n", "if len(entity)>0:\n", "    patterns = [nlp(e) for e in entity]\n", "    pharase_matcher.add(\"ENTITY_PATTERN\", patterns)\n", "\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'text' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-13-2693bc12c969>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[0;32m----> 1\u001b[0;31m \u001b[0mdoc\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mnlp\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mtext\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m      2\u001b[0m \u001b[0mmatches2\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mpharase_matcher\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mdoc\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      3\u001b[0m \u001b[0mprint\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m\"macthes 2 \"\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mmatches2\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mdoc\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0ments\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mres\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      4\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      5\u001b[0m \u001b[0;32mfor\u001b[0m \u001b[0me\u001b[0m \u001b[0;32min\u001b[0m \u001b[0mdoc\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0ments\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mNameError\u001b[0m: name 'text' is not defined"]}], "source": ["doc=nlp(text)\n", "matches2 = pharase_matcher(doc)\n", "print(\"macthes 2 \",matches2,doc.ents,res)        \n", "\n", "for e in doc.ents:\n", "    if e.label_==\"ORG\":\n", "        res.append(e.text)\n", "\n", "if len(res)==0:\n", "    for match_id, start, end in matches2:\n", "        span = doc[start:end]\n", "        match_res=span.text\n", "        print(\"match\",match_res)\n", "        res.append(match_res)"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["LINEN & UNIFORM SERVICE\n"]}], "source": ["for e in doc.ents:\n", "    if e.label_==\"ORG\":\n", "        print(e.text)"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"data": {"text/plain": ["(LINEN & UNIFORM SERVICE,)"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["doc.ents"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["#fetch all qualified company names in invoice with exclusion list \n", "\n", "text=[]\n", "with open(json_path) as json_file:\n", "    data=json.load(json_file)\n", "    for blocks in data['Blocks']:\n", "         if blocks[\"BlockType\"]=='LINE':\n", "                text.append(blocks[\"Text\"])\n", "        "]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["entity=[]\n", "    \n", "for item in gen_fields:\n", "    if item['fieldtype']==\"ORG\":\n", "        #entity_str.append( (item['str'],item['entity'] ) )\n", "        entity.append(item['entity'])\n", "\n", "pharase_matcher = PhraseMatcher(nlp.vocab)\n", "if len(entity)>0:\n", "    patterns = [nlp(e.strip().lower()) for e in entity]\n", "    pharase_matcher.add(\"ENTITY_PATTERN\", patterns)\n"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["LINEN & UNIFORM SERVICE\n", "Canadian Linen and Uniform Service Corp.\n", "Canadian Linen and Uniform Service Corp.,\n"]}], "source": ["res=[]\n", "for i in text :\n", "    doc = nlp(i ) \n", "    doc2=nlp(i.strip().lower())\n", "    #matches = matcher(doc)\n", "    matches2 = pharase_matcher(doc2)\n", "\n", "  \n", "    for ent in doc.ents:\n", "\n", "        if ent.label_==\"ORG\":\n", "            print(ent.text)\n", "            res.append(ent.text)\n", "    \n", "    \n", "    for match_id, start, end in matches2:\n", "        span = doc[start:end]\n", "        match_res=span.text\n", "        print(match_res)\n", "        res.append(match_res)\n", "    "]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"data": {"text/plain": ["()"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["doc=nlp(\"Canadian Linen and Uniform Service Corp. \")\n", "doc.ents"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"text/plain": ["['LINEN & UNIFORM SERVICE',\n", " 'Canadian Linen and Uniform Service Corp.',\n", " 'Canadian Linen and Uniform Service Corp.,']"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["res"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/plain": ["['T<PERSON><PERSON><PERSON> Canada ULC',\n", " 'pallet express',\n", " 'Canadian Linen & Uniform Service Corp.']"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Django Shell-Plus", "language": "python", "name": "django_extensions"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.4"}}, "nbformat": 4, "nbformat_minor": 2}