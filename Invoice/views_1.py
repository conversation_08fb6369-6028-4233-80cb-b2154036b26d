# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from django.shortcuts import render
from django.views.generic import View
from django.views.decorators.csrf import csrf_exempt
from django.http import JsonResponse
import json
from django.http import HttpResponse
import base64
import os
# Subprocess For Reading PDF
import subprocess
from fuzzywuzzy import fuzz
from fuzzywuzzy import process
import re

from AMS.settings import BASE_DIR
from fuzzywuzzy import fuzz
from fuzzywuzzy import process
from pyjarowinkler import distance

import numpy as np
import pandas as pd

from PIL import Image
import math
import os.path
from os import path
# import fitz


import webbrowser
import os
import json
import boto3
import io
from io import BytesIO
import sys


import threading

from collections import Counter
# Import Settings for
from AMS.extract_settings import *
from Invoice.ExtractUtils import *
from Invoice.Extract import *
from Invoice.LineSearch import *

import spacy
import uuid
from rest_framework.views import APIView
from Invoice.FieldSearch import FieldSearch
import traceback
from Invoice.ImageManipulation import *

#importing libraries for pdf to invoice
from pdf2image import convert_from_path, convert_from_bytes
from pdf2image.exceptions import (
    PDFInfoNotInstalledError,
    PDFPageCountError,
    PDFSyntaxError
)
from Invoice.Forms_Extraction import *
from Invoice.Vendor_Extraction import *
from Invoice.DbConfig import *
from Invoice.FieldMatch import * 



#nlp = spacy.load('en_core_web_md')
#print('spacy model loaded.....')

print('base diretory '+BASE_DIR)
base_path = BASE_DIR+'/Invoice/pdf/'
# invoice_path=base_path+'invoice.png'
# image_path=invoice_path
# json_path=base_path+'invoice.json'
csv_path = base_path+'csv'
base_extract = BASE_DIR+'/extract/'


# fetch all Company Names

def getCompanyLists(header_left_list, header_right_list, footer_list):
    orgs = []

    for item in header_left_list:
        doc = nlp(item)

        for ent in doc.ents:

            if str(ent.label_) == 'ORG':
                orgs.append(ent.text)


    for item in header_right_list:
        doc = nlp(item)

        for ent in doc.ents:
            # print(ent.text,ent.label_)

            if str(ent.label_) == 'ORG':
                orgs.append(ent.text)

    for item in footer_list:

        doc = nlp(item)
        for ent in doc.ents:
            if str(ent.label_) == 'ORG':
                orgs.append(ent.text)
    return orgs

# Fetch Vendor Names


def getvendorName(vendor_names, orgs):
    org_comparison_result = []
    for company in orgs:

        for names in vendor_names:
            jratio = distance.get_jaro_distance(str(names).lower().replace(
                '.', ''), str(company).lower().replace('.', ''))
            sort_ratio = fuzz.token_sort_ratio(str(names).lower().replace(
                '.', ''), str(company).lower().replace('.', ''))
            set_ratio = fuzz.token_set_ratio(str(names).lower().replace(
                '.', ''), str(company).lower().replace('.', ''))

            if jratio > 80 or sort_ratio > 80 or set_ratio > 80:
                org_comparison_result.append((names, jratio))
                # print(names," -- ",company,jratio)

    max_score = 0
    final_vendor_name = ""
    for score in org_comparison_result:
        if max_score < score[1]:
            max_score = score[1]
            final_vendor_name = score[0]

    return final_vendor_name

# Get Company Names
#
# # Get Company Names:


def remov_duplicates(input):
    input = input.split(" ")
    for i in range(0, len(input)):
        input[i] = "".join(input[i])
    UniqW = Counter(input)
    s = " ".join(UniqW.keys())
    return s


def getCompanyName(company_list):
    company_names = set()

    for company in company_list:

        if (company.lower()).find("adani") >= 0:
            company_names.add(remov_duplicates(company))

    print("Company names ", company_names)
    return company_names


class InvoiceRest(APIView):

    invoice_total_counter = []

    def extractToFolder(self, filename, extract_folder):
        if not os.path.exists(extract_folder):
            os.mkdir(extract_folder)
        print("To Be Extracted", filename)
        print("extract folder", extract_folder)
        doc = fitz.open(filename)
        print("Number of Pages ", len(doc))
        for i in range(len(doc)):

            for img in doc.getPageImageList(i):
                xref = img[0]
                pix = fitz.Pixmap(doc, xref)
                if pix.n < 5:       # this is GRAY or RGB
                    pix.writePNG(extract_folder + "p%s-%s.png" % (i, xref))
                    print(extract_folder + "p%s-%s.png" % (i, xref))
                else:               # CMYK: convert to RGB first
                    pix1 = fitz.Pixmap(fitz.csRGB, pix)
                    pix1.writePNG(extract_folder+"p%s-%s.png" % (i, xref))
                    print(extract_folder + "p%s-%s.png" % (i, xref))
                    pix1 = None
                pix = None
        print("Data Extracted.....")

    def reader(self, file):
        with open(file, "rb") as image_file:
            img_test = image_file.read()
            bytes_test = bytearray(img_test)
        return bytes_test

    def get(self, request):
        res = {"response": "ok"}
        return HttpResponse(res)

    # @csrf_exempt
    def post(self, request):
        result = {}
        config_name=None
        # Construction of JSON Structures
        try:
            extracted_fields = {}
            form_fields = []

            json_body = json.loads(request.body.decode("utf-8"))
            base64__ = json_body["data"]
            mode = json_body["mode"]
            invoice_type = json_body["type"]
            print(base64__)
            print("********")

            #Finding the configuration 
            if "config_name" in json_body:
                config_name=json_body["config_name"]
            else:
                config_name="global_config"    
            

            #checking if PDF FOLDER exists or not 
            #If PDF Folder does not exists , create it 

            if not os.path.isdir(BASE_DIR+'/Invoice/pdf'):
                os.mkdir(BASE_DIR+'/Invoice/pdf')
                print("pdf folder created...")
            
            unique_filename=None
            if "extraction_id" in json_body.keys():
                unique_filename=json_body["extraction_id"]
            
            else:
                unique_filename = str(uuid.uuid4())

            
            json_path = base_path+unique_filename+'invoice.json'
            mapping_file=BASE_DIR+'/Invoice/pdf/'+unique_filename+'mapping.json'
            csv_folder=BASE_DIR+'/Invoice/pdf/'+unique_filename+"csv"

            # invoice_id=json_body["invoice_key"]
            # name=json_body["name"]
            # base64.decodestring(base64__)
            # print(base64__)
            base_extract = BASE_DIR+'/extract/'

            
            if invoice_type == "pdf":
                invoice_path = base_path+unique_filename+".pdf"
            else:
                invoice_path = base_path+unique_filename+".png"
               


            #print("invoice_path", invoice_path)
            db_logger.info("Invoice Path "+invoice_path,{"user": str(request.user) ,"entity":unique_filename} )

            with open(os.path.expanduser(invoice_path), 'wb') as fout:
                fout.write(base64.b64decode(base64__))
            #print("File Written "+invoice_path)
            fout.close()

            db_logger.info("File Extracted Sucessfully",{"user": str(request.user) ,"entity":unique_filename} )


            #convert the pdf into image 
            if invoice_type=="pdf":
                images = convert_from_path(invoice_path, size=(2000, None))
                #for im in images:
                #im.save(base_path+unique_filename+".png")
                if len(images)>0:
                    images[0].save(base_path+unique_filename+".png")
                
                #print("exiting program....")
                #raise ValueError('A very specific bad thing happened.')
                  



                
                invoice_path = base_path+unique_filename+".png"
            
            #Skew Correction 
            #skew logic is bugged in some cases 
            #skewImage(invoice_path)

            # invoice_file_=""
            # handling the case when its pdf

            # print("Invoice file ",invoice_file_)
            # invoice_path=base_extract+"/"+invoice_file_

            with open(invoice_path, "rb") as image_file:
                base64_encoded_string = base64.b64encode(image_file.read())

            # result["base64"]=base64_encoded_string
            # Perform OCR To invoice
            # Amazon Textract client
            textract = boto3.client('textract','us-east-1')
            data_str = self.reader(invoice_path)

            # get the results
            client = boto3.client(
                    service_name='textract',
                    region_name='us-east-1',
                    endpoint_url='https://textract.us-east-1.amazonaws.com',aws_access_key_id="********************",
                        aws_secret_access_key="6629LxPd+So34S4EJPxsxs0KOvCwt/AwrjZkAHwN"
            )

            feature_field=""
            if extract_raw:
                feature_field=['TABLES','FORMS']
            else:
                feature_field=['TABLES']


            if "extraction_id" not in json_body.keys() :
                print("fetching Boto Response .....")
                response = client.analyze_document(
                    Document={'Bytes': data_str}, FeatureTypes=feature_field
                    )
                with open(json_path, 'w') as fp:
                    json.dump(response, fp)

                print("JSON File Written ...")

            db_logger.info("OCR Successful",{"user": str(request.user) ,"entity":unique_filename} )

            # CREATING Objects
            table_util_obj = TableUtils()
            extract_util_obj = ExtractUtils()
            extract_obj = Extract()

            image_path = invoice_path
          
            csv_path = base_path+unique_filename+'csv'

            # Loading Configuration File

            # Finding Dimensions of Image
            im = Image.open(image_path)
            width, height = im.size

            # calling TbExtract

            csv_path = table_util_obj.parse_main(
                invoice_path, json_path, height,mapping_file)
            # Extracting Header Items
            csv_file = extract_util_obj.findLineItemTable_V2(csv_path)
            print("csv file name .....",csv_file)
            
            
            if csv_file==None:
                db_logger.info("No LineItems Detected",{"user": str(request.user) ,"entity":unique_filename} )
                #raise ValueError('No Line Items Detected')

            db_logger.info("Line Item csv file "+csv_file,{"user": str(request.user) ,"entity":unique_filename} )

                

            # Read the mapping file and get Dimensions of Table
            #check if csv file is not none 

            mapping_data = ""
            csv_file_path=BASE_DIR +'/Invoice/pdf/'+unique_filename+'invoice-csv/'+csv_file
            with open(mapping_file) as f:
                 mapping_data = json.load(f)
            table_dimensions = mapping_data[csv_file_path]
            
            table_confidence=table_dimensions[2]
            print("table confidence ",table_confidence)

            #line_items_list,top,bottom=extract_util_obj.findLineItems_V2(base_path+'invoice.json')
            print("csv file_path ",csv_file_path)
            #try:
            df,top,bottom=extract_util_obj.findLineItems_V3( csv_file_path, json_path)
            if df.empty:
                df=extract_util_obj.findLineItemTable_raw(BASE_DIR +'/Invoice/pdf/'+unique_filename+'invoice-csv/')
            
            #except Exception:
                #print("exception",e)
            #    traceback.print_exc()
            #    raise ValueError('Match More Line Items in Settings')

            #Cleaning Data Frame 
            nan_value = float("NaN")   
            
            if df is not None:
                if not df.empty:
                    df.replace("", nan_value, inplace=True)
                    df=df.dropna(how='all',axis=1) 
                    df = df.apply(lambda x: x.str.strip() if x.dtype == "object" else x)
                    try:
                        df.columns = df.columns.str.strip()

                    except:
                        print("error while removing spaces from colmns ")
                    
                    

            

            

            print("data frame *************")
            print(df)
            print("data frame**************")
            
            #check of csv file None type finishes here 





            # Finding Table Dimensions
            extract_util_obj.getHeight(invoice_path)
            # extract_util_obj.getTableDimensions(json_path)
             # Finding Table Dimensions
            extract_util_obj.getHeight(invoice_path)
            # extract_util_obj.getTableDimensions(json_path)
            header_list = extract_util_obj.createText(
                json_path, extract_util_obj.height, extract_util_obj.width, table_dimensions[0], table_dimensions[1])
            footer_list = extract_util_obj.createTextFooter(
                json_path, extract_util_obj.height, extract_util_obj.width, table_dimensions[0], table_dimensions[1])


            #for debugging of line items  
            #return HttpResponse(json.dumps({"id":unique_filename},indent=4, sort_keys=True, default=str)) 
            
            fieldsearch=FieldSearch()
             
            #Fetch fields to be extracted from database 
            

            db_extrtaction_list=get_extraction_list(config_name,'NearestDistance')

            for extract_field in db_extrtaction_list:


                f_name = None
                f_type = None
                f_head = None
                f_tail = None
                f_regex = None
                f_entityname = None
                f_rule = None

                if "name" in extract_field.keys():
                    f_name = extract_field["name"]

                if "type" in extract_field.keys():
                    f_type = extract_field["type"]

                if "head" in extract_field.keys():
                    f_head = extract_field["head"]
                    if len(f_head.strip())>0:
                        f_head=f_head.split(",")

                if "tail" in extract_field.keys():
                    f_tail = extract_field["tail"]
                    if len(f_tail.strip())>0:
                        f_tail=f_tail.split(",")

                if "regex" in extract_field.keys():
                    f_regex = extract_field["regex"]

                if "entity_name" in extract_field.keys():
                    f_entityname = extract_field["regex"]

                if "rule" in extract_field.keys():
                    f_rule = extract_field["rule"]

                fuzz_result = None
                regex_result = None
                date_result = None
                match_result = None

                field_details={}
                field_details["name"]=f_name
                field_details["type"]=f_type
                field_details["head"]=f_head
                field_details["tail"]=f_tail
                field_details["regex"]=f_regex
                field_details["entity_name"]=f_entityname
                field_details["rule"]=f_rule

                field_value,field_conf= fieldsearch.search_field(json_path,field_details,table_dimensions[0],table_dimensions[1] )

                if not field_value==None:
                    data={f_name:field_value,"confidence_level":field_conf }
                    form_fields.append(data)





            #Reading Configurations from JSON File 
            """
            for extract_field in extract_fields:
                f_name = None
                f_type = None
                f_head = None
                f_tail = None
                f_regex = None
                f_entityname = None
                f_rule = None
                fuzz_result = None
                regex_result = None
                date_result = None
                match_result = None

                if "name" in extract_field.keys():
                    f_name = extract_field["name"]

                if "type" in extract_field.keys():
                    f_type = extract_field["type"]

                if "head" in extract_field.keys():
                    f_head = extract_field["head"]

                if "tail" in extract_field.keys():
                    f_tail = extract_field["tail"]

                if "regex" in extract_field.keys():
                    f_regex = extract_field["regex"]

                if "entity_name" in extract_field.keys():
                    f_entityname = extract_field["regex"]

                if "rule" in extract_field.keys():
                    f_rule = extract_field["rule"]

                # Searching by Fuzzy Search
                field_details={}
                field_details["name"]=f_name
                field_details["type"]=f_type
                field_details["head"]=f_head
                field_details["tail"]=f_tail
                field_details["regex"]=f_regex
                field_details["entity_name"]=f_entityname
                field_details["rule"]=f_rule

                field_value,field_conf= fieldsearch.search_field(json_path,field_details,table_dimensions[0],table_dimensions[1] )

                if not field_value==None:
                    data={f_name:field_value,"confidence_level":field_conf }
                    form_fields.append(data)
            
            """


            linesearch=LineSearch() 
            lines=linesearch.createLines(json_path)
            flat_fields=get_extraction_list(config_name,'Horizontal')

            for extract_field in flat_fields:
                f_name=None
                f_type=None
                f_regex=None
                f_rule=None

                if "name" in extract_field.keys():
                    f_name = extract_field["name"]

                if "type" in extract_field.keys():
                    f_type = extract_field["type"]

                
                if "regex" in extract_field.keys():
                    f_regex = extract_field["regex"]

               

                if "rule" in extract_field.keys():
                    f_rule = extract_field["rule"]
                


                # Searching by Fuzzy Search
                field_details={}
                field_details["name"]=f_name
                field_details["type"]=f_type
                
                field_details["regex"]=f_regex
             
                field_details["rule"]=f_rule





                res=linesearch.search_field(lines,field_details) 

                if not res==None:
                    data={f_name:res,"confidence_level":95 }
                    form_fields.append(data)
         
                

                



               
            

            

            
            

            

            
            confidence_dict=extract_util_obj.get_confidence_matrix(json_path)
            print("data frame ",df)
            line_items_dict={}
            
            if df is not None:
                if not df.empty:
                    line_items_dict=extract_util_obj.df_to_dict(df,confidence_dict)
            
             
            extracted_fields["line_items"]=line_items_dict
            # Extracting company names by patterns
            company_pattern = extract_obj.extract_company_pattern(header_list)
            companies=[]
            for i in company_pattern:
                companies.append(str(i[0]) +" "+ str(i[1]) )
            
            # extracting vendor from  Invocie 
            dst_list=get_distance_list(json_path)
            companies=addCompanyFromSpacy(companies,nlp,header_list+footer_list)
            prob_company=company_and_distance_list_intersect(companies,dst_list)
            company_header= getVendorByPosition(prob_company)

            #Fetching Vendor by Identifiers 
            match_identifier=getVendorIdentifiers(dst_list,vendor_identifier)
            company_identifier,comp_value_list=getVendorsNearIdentifiers(prob_company,match_identifier)

            # Fetching the Vendors In Footer :
            rules_matcher_vendor=getRulesMatcher(vendor_rules,lines,nlp)
            
            final_vendor_match=None

            if len(rules_matcher_vendor)>0:
                final_vendor_match= rules_matcher_vendor[0]
            elif len(comp_value_list)>0:
                final_vendor_match=comp_value_list[0]
            elif company_header is not None :
                if len(company_header)>0:
                    final_vendor_match=company_header[0]


            print("final vendor match ",final_vendor_match)






            
            #vendor_name=extract_util_obj.getVendorNameByPosition(header_list,companies)

            # extracted_fields["company"]=companies
            #print("vendor names",vendor_name)
            

            ############## temporary vendor comment ###############################
            """
            if final_vendor_match is not None:
                data={"vendor_name":final_vendor_match,"confidence_level":100}
                form_fields.append(data)
            """
            #temporary vendor logic 

            vendor_match=getVendorbycsvMatch(header_list)
            data={"vendor_name":vendor_match[0],"confidence_level":vendor_match[1]}
            form_fields.append(data)


            ############## temporary vendor comment ###############################

            
            #if extract_raw:
            raw_dict=get_raw_values(json_path)
            temp_data={} 

            field_match=FieldMatch()
            w_list=field_match.cerate_word_list(flat_fields+db_extrtaction_list)
            
            for key, value in raw_dict.items():
                    #print(key,' -- ',value)
                    #checking duplicate for extracted_field
                    #print("Match Field  checking ",key)
                    check_res=field_match.get_field_label(key,w_list) 
                    
                    key=key.replace(":","").replace(",","")

                    if check_res[1]==None:
                        data={key.strip():value[0],"confidence_level":value[1]}
                    else:
                        data={check_res[1].strip():value[0],"confidence_level":value[1]}
                    
                    #check for any duplicate present in the list 
                   
                    for d in form_fields:
                        if check_res[1] in list(d.keys()):
                            print("Duplicate found ",d , " :for ",check_res[1]," , Match_score :")
                            form_fields.remove(d)
                            

                            #form_fields.append(d)
                            temp_val=d[check_res[1]]
                            d[check_res[1]+"_duplicate"]=temp_val
                            t=d
                            del d[check_res[1]]
                            form_fields.append(t)
                    





                    
                    form_fields.append(data)
                    #temp_data[key]=value
                
                
                


                



            

            #Embedding Image Field 
            base64_data={"base64":base64__}

            #If Setting in Response 
            if image_in_response:
                extracted_fields["base64"]=base64__
            
            extracted_fields["extraction_id"]=unique_filename
            

            
            extracted_fields["uid"]=unique_filename
            extracted_fields["form_fields"]=form_fields
            extracted_data={}
            response_data={"extracted_data":extracted_fields}

            # print("extracted fields ",extract_fields)
            
            # Remove csv files extracted 
            


            if not mode=="test" : #"extraction_id" not in json_body.keys() :
                import shutil
                #removing folder
                shutil.rmtree(csv_path)

                if path.exists(json_path):
                    os.remove(json_path)

                if path.exists(mapping_file):
                    os.remove(mapping_file)

                if path.exists(invoice_path):
                    os.remove(invoice_path)
                
                print("all files cleaned ......")
            
           
             
            
            


           
            
            return HttpResponse(json.dumps(response_data,indent=4, sort_keys=True, default=str)) 
        
        except ValueError as e:
            import traceback
            print(traceback.format_exc())
            error={}
            error["error"]=str(e)
            print("msg",e)
            db_logger.exception("Line Item Exception",{"user": str(request.user) ,"entity":unique_filename})
            return HttpResponse(json.dumps(error,indent=4, sort_keys=True, default=str))
        
        except Exception as e:
            import traceback
            print(traceback.format_exc())
            error={}
            error["error"]="Invalid JSON/Corruption JSON values"
            print("Error ",str(e))
            return HttpResponse(json.dumps(str(e),indent=4, sort_keys=True, default=str))
        
         
