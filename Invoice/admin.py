from django.contrib import admin
from modelclone import ClonableModelAdmin
from simple_history.admin import SimpleHistoryAdmin
# Register your models here.
from django.contrib.auth.models import User

from .models import *
from django.apps import apps


class KValueAdmin(ClonableModelAdmin):
    search_fields = ('display_name', )

    def render_change_form(self, request, context, *args, **kwargs):
        kwargs['add'] = True
        return super(KValueAdmin, self).render_change_form(request, context, *args, **kwargs)


#cloneable lineitem exclusion
class LineItemHeadersAdmin(ClonableModelAdmin,admin.ModelAdmin):
    search_fields = ('display_name', )
   
    def render_change_form(self, request, context, *args, **kwargs):
        kwargs['add'] = True
        return super(LineItemHeadersAdmin, self).render_change_form(request, context, *args, **kwargs)

#cloneable lineitem exclusion
class LineItemExclusionAdmin(ClonableModelAdmin):
    #search_fields = ('display_name', )

    def render_change_form(self, request, context, *args, **kwargs):
        kwargs['add'] = True
        return super(LineItemExclusionAdmin, self).render_change_form(request, context, *args, **kwargs)


class ExtractFieldMappingAdmin(admin.ModelAdmin):
    #list_select_related = ['field__name']
    search_fields = ('field__name', )

    pass

class ExtractFieldMappingAdmin(admin.ModelAdmin):
    #list_select_related = ['field__name']
    search_fields = ('field__name', )

    pass

class SearchableLineItemHeadersAdmin(admin.ModelAdmin):
    #list_select_related = ['field__name']
    search_fields = ('display_name', )

    pass




    
   

admin.site.register(kvalue,KValueAdmin)
admin.site.register(Configuration)
admin.site.register(ExtractField)

admin.site.register(ExtractFieldMapping,ExtractFieldMappingAdmin)
admin.site.register(MlModel)
admin.site.register(VendorFields)
admin.site.register(Regex)
admin.site.register(RegexGroupName)
admin.site.register(RegexGroupValue)
admin.site.register(LineItemHeaders,LineItemHeadersAdmin)
admin.site.register(AddressEntity)
admin.site.register(GeneralEntity)
admin.site.register(LineItemExclusion,LineItemExclusionAdmin)
admin.site.register(Query)

# Register your models here.
Historicalkvalue = apps.get_model("Invoice", "Historicalkvalue")

@admin.register(Historicalkvalue)
class HistoricalkvalueAdmin(admin.ModelAdmin):
    list_display = ["history_date","display_name", "f_name","history_user_name","history_type"]
    
    def history_user_name(self, obj):
        return User.objects.get(pk=obj.history_user_id)
    def has_delete_permission(self, request, obj=None):
        return False
    def has_add_permission(self, request, obj=None):
        return False
    def has_change_permission(self, request, obj=None):
        return False