from django.db import models
#from picklefield.fields import PickledO<PERSON>Field
#import pickle as cPickle
from django import forms
from jsonfield import <PERSON><PERSON><PERSON><PERSON>
#from regex_field.fields import RegexField
##### Authnetication Imports Start
from django.views.generic.list import ListView

from django.db.models.signals import post_save
from django.dispatch import receiver
from rest_framework.authtoken.models import Token
from django.conf import settings
from simple_history.models import HistoricalRecords

##### Authentication Imports END


# Create your models here.
#types of Fields
#Alpha - Alphneumric 
#Date  - Only Date 
#Float - Strictly Float 
#Money - Money Field
#String - pure charcters with no numbers
#Int  - Integer with no Float Fields Involved 


FIELD_TYPES = (
    ('Alpha','Alphaneumeric'),
    ('Date', 'Date'),
    ('Float','Float'),
    #('Money','Money'),
    #('String','String'),
    #('Int','Int'),
    ('Numstring','Numstring'),
    ('Rule','Rule'),
    ('Rule for Value','Rule for Value'),
    ('Regex','Regex'),
    ('Address','Address')
)

ADDR_ENTITY_TYPES = (
    ('PLACE','PLACE'),
    ('CITY-COUNTRY', 'CITY-COUNTRY'),
    ('EXCLUDE','EXCLUDE')
   
)

GEN_ENTITY_TYPES = (
    ('ORG','ORG'),
  
   
)

ALGO_TYPES = (
    ('NearestDistance','NearestDistance'),
    ('Horizontal', 'Horizontal'),
   
)

@receiver(post_save, sender=settings.AUTH_USER_MODEL)
def create_auth_token(sender, instance=None, created=False, **kwargs):
    if created:
        Token.objects.create(user=instance)

class Regex(models.Model):
    name = models.CharField(max_length=100,db_index=True,unique=True)
    description = models.TextField(max_length=500,null=True,blank=True)
    regex = models.CharField(max_length=200)
    regex_index = models.IntegerField(null=True, blank=True)
    #group_id = models.CharField(max_length=200,null=True,blank=True)
    #priority = models.CharField(max_length=200,null=True,blank=True)
    #terminate_once_matched = models.BooleanField(default=False)
    
    def __str__(self):
        return self.name

class RegexGroupName(models.Model):
    name      = models.CharField(max_length=50,db_index=True)
    description = models.TextField(max_length=500,null=True,blank=True)
    def __str__(self):

        return self.name

class RegexGroupValue(models.Model):
    field = models.ForeignKey(RegexGroupName, db_index=True,on_delete=models.CASCADE)
    value = models.ForeignKey(Regex, db_index=True ,on_delete=models.CASCADE)
    def __str__(self):

        return (self.field.name+" - "+self.value.name)




        


class kvalue(models.Model): 
    #container = models.ForeignKey(Dicty, db_index=True,on_delete=models.CASCADE)
    f_name = models.CharField(max_length=240, db_index=True)
    display_name = models.CharField(max_length=240, blank=True)

    f_type = models.CharField(max_length=240,choices=FIELD_TYPES)
    f_head = models.TextField(max_length=2000, blank=True)
    f_tail = models.TextField(max_length=2000, blank=True)
    f_strategy =models.CharField(max_length=240, blank=True)
    #f_rule =models.TextField(max_length=2000, blank=True)
    f_rule = JSONField(default={},blank=True, null=True)
    #f_model_path =models.CharField(max_length=240, db_index=True)
    #f_regex= RegexField(max_length=200,blank=True, null=True)
    #f_regex = models.TextField(max_length=2000, null=True,blank=True)
    f_regex_field = models.ForeignKey(RegexGroupName,on_delete=models.CASCADE,null=True,blank=True)
    #f_regex = JSONField(default={},blank=True, null=True)
    #source = PythonCodeField(blank=True, null=True)
    f_algo_type = models.CharField(max_length=240,choices=ALGO_TYPES)
    masking=models.BooleanField(default=True)
    match_threshhold=models.IntegerField(default=85)
    
    hascondition=models.BooleanField(default=False)
    conditionfieldnames = JSONField(default={},blank=True, null=True)
    

    history = HistoricalRecords()
    
    def clean(self):
        print("head",self.f_head)
        arr=self.f_head.split(',')
        print("array ",arr)

    def __str__(self):

        return self.display_name







class ExtractField(models.Model):
    name      = models.CharField(max_length=50,db_index=True)
    description = models.TextField(max_length=500,null=True,blank=True)
    def __str__(self):

        return self.name

class ExtractFieldMapping(models.Model):
    field = models.ForeignKey(ExtractField, db_index=True,on_delete=models.CASCADE)
    value = models.ForeignKey(kvalue, on_delete=models.CASCADE)
    active = models.BooleanField()
    def __str__(self):

        return (self.field.name+" - "+self.value.f_name +"     "+str(self.active) )

#Class for adding entity types 
#These Entity types will be injected at runtime 
#and will be used for training to the datasets 

class AddressEntity(models.Model):
    
    name = models.CharField(max_length=200)
    extractfield = models.ForeignKey(ExtractField, db_index=True,on_delete=models.CASCADE)
    fieldtype = models.CharField(max_length=240,choices=ADDR_ENTITY_TYPES)
    
    longstring = models.TextField(max_length=500)
    entity      = models.CharField(max_length=200,null=True,blank=True)
    
    
    def __str__(self):

        return (self.extractfield.name+" - "+self.longstring  )


class GeneralEntity(models.Model):
    
    name = models.CharField(max_length=200)
    extractfield = models.ForeignKey(ExtractField, db_index=True,on_delete=models.CASCADE)
    fieldtype = models.CharField(max_length=240,choices=GEN_ENTITY_TYPES)
    
    fullstring = models.TextField(max_length=500)
    entity      = models.CharField(max_length=200,null=True,blank=True)
    
    
    def __str__(self):

        return (self.extractfield.name+" - "+self.fullstring  )

#List of Tax Items that should be removed from line items 


class VendorFields(models.Model):
    
    name = models.CharField(max_length=200)
    
    extractfield = models.ForeignKey(ExtractField, db_index=True,on_delete=models.CASCADE)
    #fieldtype = models.CharField(max_length=240,choices=GEN_ENTITY_TYPES)
    
    fieldnames = models.TextField(max_length=5000)
    
    
    def __str__(self):

        return (self.extractfield.name+" - "+self.name  )

class Query(models.Model):
    
    displayname = models.CharField(max_length=200)
    fieldname = models.CharField(max_length=200)
    query = models.TextField(max_length=500,null=True,blank=True)
    description = models.TextField(max_length=500,null=True,blank=True)
    extractfield = models.ForeignKey(ExtractField, db_index=True,on_delete=models.CASCADE)
    #fieldtype = models.CharField(max_length=240,choices=GEN_ENTITY_TYPES)
    
    vendorfieldnames = JSONField(default={})
    missingfieldnames = models.TextField(max_length=5000)
    
    
    def __str__(self):

        return (self.extractfield.name+" - "+self.displayname  )

class TaxItem(models.Model):
    
    name= models.CharField(max_length=200)
    extractfield = models.ForeignKey(ExtractField, db_index=True,on_delete=models.CASCADE)
    
    
    taxlist = models.TextField(max_length=5000)
    description = models.TextField(max_length=500)
   
    
    
    def __str__(self):

        return (self.extractfield.name+" - "+self.name )

class Configuration(models.Model):
    name = models.CharField(max_length=100,db_index=True,unique=True)
    description = models.TextField(max_length=500,null=True,blank=True)
    company_names = models.TextField(max_length=3000,null=True,blank=True)
    extract_field = models.ForeignKey(ExtractField,on_delete=models.CASCADE)
    snow_url= models.TextField(max_length=200,null=True,blank=True)
    ai_fieldmatch_threshold=models.IntegerField(default=95)
    max_pagescan=models.IntegerField(default=10)
    min_col_in_line_items = models.IntegerField(default=3)
    def __str__(self):
        return self.name


#Header Names Configuration



class LineItemHeaders(models.Model):
    display_name = models.CharField(max_length=100,db_index=True)
    name = models.CharField(max_length=100,db_index=True)
    description = models.TextField(max_length=500,null=True,blank=True)
    field_names = models.TextField(max_length=2000,null=True,blank=False)
    active = models.BooleanField()
    field = models.ForeignKey(ExtractField, default=None,db_index=True,on_delete=models.CASCADE)
    def __str__(self):
        return self.display_name

class LineItemExclusion(models.Model):
    name = models.CharField(max_length=100,db_index=True,unique=True)
    field = models.ForeignKey(ExtractField, default=None,db_index=True,on_delete=models.CASCADE)
    type = models.CharField(max_length=100)
    field_list = models.TextField(max_length=5000)
    active = models.BooleanField()
    
    def __str__(self):
        return self.name+" , "+str(self.active)

class MlModel(models.Model):
    name = models.CharField(max_length=100,db_index=True,unique=True)
    path = models.TextField(max_length=500)
    active = models.BooleanField()
    
    def __str__(self):
        return self.name


class kvalueListView(ListView): # or PollHistorySerializer(ModelSerializer):
    class Meta:
        model = kvalue.history.model