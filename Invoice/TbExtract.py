import webbrowser, os
import json
import boto3
import io
from io import BytesIO
import sys
from pprint import pprint




def get_rows_columns_map(table_result, blocks_map):
    rows = {}
    for relationship in table_result['Relationships']:
        if relationship['Type'] == 'CHILD':
            for child_id in relationship['Ids']:
                cell = blocks_map[child_id]
                if cell['BlockType'] == 'CELL':
                    row_index = cell['RowIndex']
                    col_index = cell['ColumnIndex']
                    if row_index not in rows:
                        # create new row
                        rows[row_index] = {}
                        
                    # get the text value
                    rows[row_index][col_index] = get_text(cell, blocks_map)
    return rows


def get_text(result, blocks_map):
    text = ''
    if 'Relationships' in result:
        for relationship in result['Relationships']:
            if relationship['Type'] == 'CHILD':
                for child_id in relationship['Ids']:
                    word = blocks_map[child_id]
                    if word['BlockType'] == 'WORD':
                        text += word['Text'] + ' '
                        
                    """     
                    if word['BlockType'] == 'SELECTION_ELEMENT':
                        if word['SelectionStatus'] =='SELECTED':
                            text +=  'X '
                    """
    return text.replace(',','')

def get_table_csv_results(filename,jsonname):

    
    with open(jsonname) as json_file:
         data=json.load(json_file)
            
    print("File Read "+jsonname)
    
    response=data
    
   
   

    csv_list=[]
    # Get the text blocks
    blocks=response['Blocks']
    #pprint(blocks)

    blocks_map = {}
    table_blocks = []
    for block in blocks:
        blocks_map[block['Id']] = block
        if block['BlockType'] == "TABLE":
            table_blocks.append(block)

    if len(table_blocks) <= 0:
        return "<b> NO Table FOUND </b>"

    
    for index, table in enumerate(table_blocks):
        csv = ''
        csv += generate_table_csv(table, blocks_map, index +1)
        csv_list.append(csv)
        #csv += '\n\n'

    return csv_list
    


def generate_table_csv(table_result, blocks_map, table_index):
    rows = get_rows_columns_map(table_result, blocks_map)

    table_id = 'Table_' + str(table_index)
    
    # get cells.
    # csv = 'Table: {0}\n\n'.format(table_id)
    csv=''
    for row_index, cols in rows.items():
        
        for col_index, text in cols.items():
            csv += '{}'.format(text) + ","
        csv += '\n'
        
    csv += '\n\n\n'
    return csv






def parse_main(filename,jsonname):
    table_csv = get_table_csv_results(filename,jsonname)
    
    cv_path=csv_path.replace('.csv','-csv')
    import os
    if not os.path.isdir(cv_path):
        os.mkdir(cv_path)
    
    
    counter=1
    for table in table_csv:
        filename_=cv_path+"/"+str(counter)+'.csv'
        
        with open(filename_, "wt") as fout:
            fout.write(table)
        counter=counter+1
            
        print('CSV OUTPUT FILE: ', filename_)

    
    
    fout.close()
