import sys
import re
import json
from scipy.spatial import distance as sp_distance
import math
from fuzzywuzzy import fuzz
import spacy
from spacy.matcher import Matcher
import pandas as pd 
from fuzzywuzzy import fuzz
from Invoice.ML_Vendor_Extraction import *
import json
import pandas as pd  
#import lexnlp.extract.en.entities.nltk_re
from PIL import Image
#import Image
#import lexnlp.extract.en.urls
from urllib.parse import urlparse
import traceback
import tldextract

#extract object initiated 
#extract = tldextract.TLDExtratct()



def getTextFromBox(json_path,box,im_width,im_height):

    f = open(json_path,) 
    data = json.load(f)
    
    x1= box[0] -50
    x2= box[1] + 100
    y1= box[2] 
    y2= box[3]
    print("get text from box ",y1,x1,y2,x2)
    text=[]
    for d in data["Blocks"]:
        if d["BlockType"]=="LINE":

            BoundingBox=d["Geometry"]["BoundingBox"]
            add=d["Geometry"]["Polygon"]

             
            y1_= (add[0]["Y"]*im_height)
            y2_= (add[3]["Y"]*im_height)
            x1_= (add[0]["X"]*im_width)
            x2_= (add[2]["X"]*im_width)
            
            #print(d["Text"],"--",y1_,x1_,y2_,x2_)
            
            
            if y1_>y1 and y2_<y2 and x1_>x1 and x2_<x2:
                print("ex text ",d["Text"])
                if len(d["Text"])>=3:
                    text.append(d["Text"])
    print("text from box ",text)
    return text 



def getVendorByTop(json_path,im_height,vendorlist):

    f = open(json_path,) 
    data = json.load(f)

    lines=[]
    for d in data["Blocks"]:
        if d["BlockType"]=="LINE":
            top=d["Geometry"]["BoundingBox"]["Top"]
            lines.append( (d["Text"],top) )
            if top>(im_height*30):
                break
    
    f=[]
    for l in lines:
        #r=list(lexnlp.extract.en.entities.nltk_re.get_companies(l[0]))
        r=spacy_getorg(l[0])
        if getfuzz_score_list(vendorlist,l[0]) is not None:
            continue
                        

        if len(r)>0:
            f.append(l)
            #print(l)
    if len(f)>0:        
        f.sort(key = lambda x: x[1])
        return f[0]
    else:
        return None

def spacy_getorg(text):
    res=[]
    doc=nlp(text)

    for e in doc.ents:
        if e.label_=="ORG":
            res.append(e.text)
    
    return res




def spacy_geturl(text):

    urls=[]
    doc=nlp(text)
    for e in doc:
        if e.like_url:
            urls.append(e)
    
    return urls

  
def getVendorbyMatch(boxtext):

    f=[]
    for l in boxtext:
        #r=list(lexnlp.extract.en.entities.nltk_re.get_companies(l))
        r=spacy_getorg(l)
        print("vendor ",r)
        if len(r)>0:
            f.append(r[0])
            print("ven ext spacy ",r)
    if len(f)>0:
        
        #f.sort(key = lambda x: x[1])
        return f[0]
    else:
        return None

def get_fuzz_score(str1, str2):

    from fuzzywuzzy import fuzz
    partial_ratio = fuzz.partial_ratio(str1.lower(), str2.lower())
    return partial_ratio

def getfuzz_score_list(arr,str1):
    
    final_match=None
    for v in arr :
        ratio = fuzz.partial_ratio(v.lower(), str1.lower())
        if ratio>85:
            final_match=v
    
    return final_match




def getVendorByMail(json_path,vendorlist):
    
    email_list=[]
    url_list=[]

    with open(json_path) as json_file:
        data=json.load(json_file)
    
    for d in data["Blocks"]:
        if d["BlockType"]=="LINE":
            #print(d["Text"])
            text= d["Text"]
            emails = re.findall(r"[a-z0-9\.\-+_]+@[a-z0-9\.\-+_]+\.[a-z]+", text)
            #url=list(lexnlp.extract.en.urls.get_urls(text))
            url=spacy_geturl(text)
            #print(url )
            if len(url)>0:
                #parsed_uri = urlparse(str(url[0]))

                #if parsed_uri.netloc!="":
                #    domain=parsed_uri.netloc.split(".")[0]
                #else:
                #    domain=parsed_uri.path.split(".")[0]
                res=tldextract.extract(str(url[0]) )
                domain=str(res.domain)
                #domain=parsed_uri.netloc.split(".")[0]
                if domain is not None:
                    if getfuzz_score_list(vendorlist,domain) is None:
                        url_list.append(domain)
            if len(emails)>0:
                print("emails found ",emails,getfuzz_score_list(vendorlist,emails[0]))

                if getfuzz_score_list(vendorlist,emails[0]) is None:
                    email_list.append(emails[0].split("@")[1].split(".")[0])
    
    print("url list ",url_list)
    print("email list ",email_list)
    url_list=[x for x in url_list if x]
    email_list=[x for x in email_list if x]

    if len(url_list)>0:
        
        return url_list[0]
    
    if len(email_list) >0:
        return email_list[0]

    return None

        

       

def getVendorByLogoMatch(json_path,boxtext,vendorlist):

    #Retrive all the lines in the scanned doc 
    text_data=[]
    with open(json_path) as json_file:

        data=json.load(json_file)

        for blocks in data['Blocks']:
            if (blocks["BlockType"]=='LINE'):
                text_data.append(blocks["Text"])

    ############Extract Company Names ########
    company_list=[]
    #company_list=spacy_getorg(text_data)
    
    for i in text_data:
        #r=list(lexnlp.extract.en.entities.nltk_re.get_companies(i))
        r=spacy_getorg(i)

        if len(r)>0 :
            if getfuzz_score_list(vendorlist,r[0]) is None:
                company_list.append(r[0])
    
    result=[]
    for i in company_list:
        for j in boxtext:
            score=get_fuzz_score(i, j.split(" ")[0])
            print("matching ",i,j,score)
            if score > 80:
                ven=i
                """
                #if len(i[1])>70:
                    ven=j
                else:
                    ven=i[1]
                """
                result.append([ven,score])
                #print(i[1],j,score)
    return result


def getVendors(model,image,json_path,companylist):
   
    #vendor_list=["td williamson","welldyne"]
    print("companylist ...... ",companylist)
    vendor_list=[]
    if companylist is not None:
        vendor_list=companylist
    
    print("vendor list ... ",vendor_list) 

    im = Image.open(image)
    im_width, im_height = im.size
    print("image dim ",im_height,im_width)
    #call ML Model to get the Results 
    p_res=None
    boxtext=[]
    try: 
        p_res=predict(model,image,im_width,im_height)
        
    except Exception:
        
        print("Error While ML Vendor Extraction")  
        print(traceback.format_exc())  
    if p_res is not None:
    
        box=p_res[0]
        confidence=p_res[1]
    
        #extract all the text from bounding box 
        boxtext = getTextFromBox(json_path,box,im_width,im_height)
    
        print("boxtext")
        print(boxtext)
        #check if any text matches the company format 
        match_item=None
        if len(boxtext)>0:
            match_item=getVendorbyMatch(boxtext)
    
        if match_item is not None:
            return match_item,confidence
    
        #Search for Match in whole of the Invoice except the Company Name
    
        result=getVendorByLogoMatch(json_path,boxtext,vendor_list)
        result.sort(key = lambda x: x[1],reverse=True)
    
        #sort results by secound column 
        print("vendor results are ")
        print(result)
        if len(result)>0:
            return result[0][0],result[0][1]
    
    #Search vendor by email id or url :
    resbymail=getVendorByMail(json_path,vendor_list)
    print("vendor results by email ",resbymail)
    if resbymail is not None:
        return resbymail,100
    
    if len(boxtext)>0:
        return boxtext[0],confidence
    else:
        match_item=getVendorByTop(json_path,im_height,vendor_list)
        if match_item is not None:
            return match_item[0],100
    return None,0     
    
    

    
    
    
    
    




















       

    









        

        


