
from Invoice.DbConfig import *
import spacy
from spacy.matcher import Matcher
from Invoice.ExtractUtils import *
import math 
from scipy.spatial import distance as sp_distance
from fuzzywuzzy import fuzz 
from fuzzywuzzy import process 
import datefinder
from spacy.matcher import Phrase<PERSON><PERSON><PERSON>,Matcher
from Invoice.DbConfig import *
from spacy.tokens import Span
#

def getfuzz_score_list(arr,str1):
    
    final_match=None
    for v in arr :
        ratio = fuzz.partial_ratio(v.lower(), str1.lower())
        if ratio>85:
            final_match=v
    
    return final_match



def filter_vendor_fields(form_fields,config_name):
    
    #get company names 
    config_company_names=get_companynames(config_name)


    vendorfields=get_VendorFields(config_name)
    print("length of vendorfields ",len(vendorfields))
    if len(vendorfields)==0:
        return None,0
    kvpair={}
    ratio_matches=[]
    for item in form_fields:
        
        key=list(item.keys())[0]

        kvpair[key]=item[key]

        match_ratio=0
        for vitem in vendorfields:
            try:
                match_ratio = fuzz.ratio(vitem.lower().strip(), key.lower().strip())
                print("match ratio ",vitem.lower().strip()," : ",key.lower().strip()," : ",match_ratio)
                
            except:
                continue
        
            if match_ratio>95:
                ratio_matches.append((key,match_ratio))
    
    #find the highiest match 
    print("before sorting ",ratio_matches)
    ratio_matches.sort(key=lambda x:x[1],reverse=True)
    print("after sorting ",ratio_matches) 
    matched_key=None
    if len(ratio_matches)>0:
        matched_key=ratio_matches[0]
    
    print("matched key ",matched_key)
    if matched_key==None:
        return None,0

    #Extract Org from Values :
     ################# Add patterns for entity in config  
    gen_fields=get_General_entity(config_name)

    
    entity_str=[]
    entity=[]
    
    for item in gen_fields:
        if item['entity']=="ORG":
            entity_str.append( (item['str'],item['entity'] ) )
            entity.append(item['entity'])

    print("*******************entity***************************")
    print(entity)
    pharase_matcher = PhraseMatcher(nlp.vocab)
    if len(entity)>0:
        patterns = [nlp(e) for e in entity]
        pharase_matcher.add("ENTITY_PATTERN", patterns)
    
    text=kvpair[matched_key[0]]

    if getfuzz_score_list(config_company_names,text ) is not None:
        return None,0

    doc = nlp( text ) 
    #matches = matcher(doc)
    matches2 = pharase_matcher(doc)

    res=[]
    for ent in doc.ents:

        if ent.label_=="ORG":
            res.append(ent.text)
    
    for match_id, start, end in matches2:
        span = doc[start:end]
        match_res=span.text
        res.append(match_res)
    
    #checking phrase matcher 
    print("Res ")
    print(res) 

    if len(res)>0:
        result = max(res, key = len)
        return result,matched_key[1]
    else:
        return None,0 






    


        



