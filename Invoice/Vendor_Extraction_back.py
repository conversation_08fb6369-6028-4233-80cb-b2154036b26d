import sys
import re
import json
from scipy.spatial import distance as sp_distance
import math
from fuzzywuzzy import fuzz
import spacy
from spacy.matcher import Matcher
import pandas as pd 
from fuzzywuzzy import fuzz



comp_list=[
"A & A Cleaning, Inc.",
"Alteryx, Inc.",
"amazon business",
"avizva",
"cdw direct",
"comcast business",
"Falcon-Software Company Inc",
"FLORIDA HANDLING SYSTEMS",
"Gartner",
"Grainer",
"Innovation",
"Dell Inc" ,
"IPrint Technologies",
"NUcycle Energy of Tampa LLC",
"pitney bowes",
"Polk Air",
"Presidio Networked Solutions",
"SHI International Corp"
"Raymond",
"sprint",
"stericycle",
"sylvester",
"terminix",
"Unimax Systems Corporation",
"verizon",
"wellsky",
"adobe",
"DELL MARKETING L.P",
"Copy Products inc"
"American cooler",
"automated business products",
"central food equipment",
"Century Signs, Inc.",
"comcast business",
"conga",
"Dickson",
"Duffy and Lee Company",
"ELITE OFFICE LOGISTICS",
"xcel energy",
"FaxCore, Inc."
"Frontier communications",
"Aspect Software Inc",
"JJ maintenance company",
"lakeland electric",
"k-c electric co.",
"MicroFocus LLC",
"Milliman",
"orasi software inc",
"pluralsight",
"rapid7 llc",
"relay health",
"United Power & Battery Corporation",
"Wired Tight Audio Visual, Inc.",
"welltok",
"xevant"
]



def get_distance_list(json_path):

    dist_list=[] 
    with open(json_path) as json_file:
        data=json.load(json_file)

        for blocks in data["Blocks"]:
            
            if blocks["BlockType"]=='LINE':
                item=blocks["Geometry"]["Polygon"]
                dist = math.hypot(item[0]['X']-item[3]['X'], item[0]['Y']-item[3]['Y'])
                y=item[0]['Y']
                x=item[0]['X']
                dist_list.append( (blocks["Text"],round(dist,2),round(x,2),round(y,2) ) )
    
    return dist_list


def addCompanyFromSpacy(companies,nlp,items_list):

    for item in items_list:
        doc = nlp(item)
        for ent in doc.ents:
            if ent.label_=="ORG":
                companies.append(item)

    return companies


def company_and_distance_list_intersect(companies,dist_list):
    prob_company=[]
   
    for i in dist_list:
        for j in companies:
            ratio=fuzz.ratio(i[0],j)
            if ratio >85:
                #print(i[0],' - ',i[1]+(1-i[2]),' -- ',j,' -- ',ratio)
                prob_company.append(i)
    return prob_company

def getVendorByPosition(prob_companies):
    
    max_score=0
    max_item=None
    for item in prob_companies:
        item_score=item[1]+(1-item[3])
        if item_score>max_score:
            max_score=item_score
            max_item=item

    return max_item


#Finding the keywords in the list for vendor identification 

def getVendorIdentifiers(dist_list,vendor_identifier):
    match_identifier={}

    for identifier in vendor_identifier:
        final_id=None
        max_score=0
        
        for i in dist_list:
            item=i[0]
            ratio=fuzz.ratio(item.lower().strip(),identifier.lower().strip() )
            if ratio>max_score and ratio>85:
                max_score=ratio
                final_id=i
                
        if final_id is not None:
            match_identifier[identifier]=(final_id,max_score)
    
    return match_identifier

def getVendorsNearIdentifiers(prob_company,match_identifier):

    distance_matrix={}
    values_list=[]
    #Matching the service provider and 
    for key,value in match_identifier.items():
        #print(key,'--',value)
        x1=value[0][2]
        y1=value[0][3]
        lowest_dist=0
        distance_item=None
        for c in prob_company:
            x2=c[2]
            y2=c[3]
            
            #Distance between them 
            dist = math.hypot(x2-x1, y2-y1)
            #dist2=1-distnc
            #print(dist ,' : ',value[0][0],' : ',c[0])
            if (1-dist)>lowest_dist:
                lowest_dist=(1-dist)
                distance_item=c
        
        #print( "final ",distance_item)
        distance_matrix[key]=distance_item
        values_list.append(distance_item[0])
    
    return distance_matrix,values_list

def getRulesMatcher(rules,lines,nlp):

    matcher = Matcher(nlp.vocab, validate=True)
    #rule1=[{'LOWER': 'for'},{'OP': '?'},{'ENT_TYPE': 'ORG','OP':'*'}]       
    
    for key,value in rules.items():
        matcher.add(key, None, value)

    
    match_list=[]
    for line in lines:

        doc = nlp(line)
        matches = matcher(doc)

        if matches:
            doc = nlp(line)
            for ent in doc.ents:
                if ent.label_=="ORG":
                    match_list.append(str(ent))
    return match_list


def getVendorbycsvMatch(header_list):

    #read the csv file
    #clist= pd.read_csv("./clist.csv")
    #clist=list(clist["company"])
    clist=comp_list
    max_match_score=0
    max_match_item=None
    for item in header_list:

        for i in clist:
            ratio=fuzz.ratio(i.lower().strip(), item.lower().strip())
            if ratio > max_match_score:
                max_match_score=ratio
                max_match_item=i
    

    return (max_match_item,max_match_score)













       

    









        

        


