import numpy as np
from PIL import Image
#import Image
import os

import gc

def predictInvoice(model, imagepath):
    im = Image.open(imagepath).resize((480, 480))
    
    arr = np.expand_dims(np.array(im), axis=0)
    prediction = model(arr, training=False).numpy()

    result = {"NI": float(prediction[0][1]), "I": float(prediction[0][0])}

    im.close()
    del im, arr, prediction
    gc.collect()

    return result

