# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from django.shortcuts import render
from django.views.generic import View
from django.views.decorators.csrf import csrf_exempt
from django.http import JsonResponse
import json
from django.http import HttpResponse
import base64
import os
# Subprocess For Reading PDF
import subprocess
from fuzzywuzzy import fuzz
from fuzzywuzzy import process
import re

from AMS.settings import BASE_DIR
from fuzzywuzzy import fuzz
from fuzzywuzzy import process
from pyjarowinkler import distance

import numpy as np
import pandas as pd

from PIL import Image
import math
import os.path
from os import path
# import fitz


import webbrowser
import os
import json
import boto3
import io
from io import BytesIO
import sys


import threading

from collections import Counter
# Import Settings for
from AMS.extract_settings import *
from Invoice.ExtractUtils import *
from Invoice.Extract import *
from Invoice.LineSearch import *

import spacy
import uuid
from rest_framework.views import APIView

import traceback
from Invoice.ImageManipulation import *
from Invoice.FormFieldExtractionWrapper import *

#importing libraries for pdf to invoice
from pdf2image import convert_from_path, convert_from_bytes
from pdf2image.exceptions import (
    PDFInfoNotInstalledError,
    PDFPageCountError,
    PDFSyntaxError
)
from Invoice.Forms_Extraction import *
from Invoice.Vendor_Extraction import *
from Invoice.DbConfig import *
from Invoice.FieldMatch import * 

#Importing tensorflow libraries for prediction 
from tensorflow.keras.models import load_model
from tensorflow.keras.preprocessing import image
import numpy as np
from Invoice.ML.TFPredictions import *

import tensorflow as tf
from object_detection.utils import ops as utils_ops
from object_detection.utils import label_map_util

op_path_=None
TF_path_=None

if run_mode=='local':

    op_path_="inference_graph_13_sept"
    TF_path_="model.h5"
else:
    op_path_="../inference_graph_13_sept"
    TF_path_="../model.h5"



output_directory = op_path_  #'../inference_graph_13_sept'

#nlp = spacy.load('en_core_web_md')
#print('spacy model loaded.....')

print('base diretory '+BASE_DIR)
base_path = BASE_DIR+'/Invoice/pdf/'
# invoice_path=base_path+'invoice.png'
# image_path=invoice_path
# json_path=base_path+'invoice.json'
csv_path = base_path+'csv'
base_extract = BASE_DIR+'/extract/'

#Invoice-NonInvoice classification Model
#TFmodel2 = load_model('../model.h5')
TFmodel2 = load_model(TF_path_)
print("TENSORFLOW MODEL LOADED......")

tf.keras.backend.clear_session()
TF_OB_model = tf.saved_model.load(f'{output_directory}/saved_model')
print("TENSORFLOW Object Detection API Model Loaded ......")

# fetch all Company Names

def getCompanyLists(header_left_list, header_right_list, footer_list):
    orgs = []

    for item in header_left_list:
        doc = nlp(item)

        for ent in doc.ents:

            if str(ent.label_) == 'ORG':
                orgs.append(ent.text)


    for item in header_right_list:
        doc = nlp(item)

        for ent in doc.ents:
            # print(ent.text,ent.label_)

            if str(ent.label_) == 'ORG':
                orgs.append(ent.text)

    for item in footer_list:

        doc = nlp(item)
        for ent in doc.ents:
            if str(ent.label_) == 'ORG':
                orgs.append(ent.text)
    return orgs

# Fetch Vendor Names


def getvendorName(vendor_names, orgs):
    org_comparison_result = []
    for company in orgs:

        for names in vendor_names:
            jratio = distance.get_jaro_distance(str(names).lower().replace(
                '.', ''), str(company).lower().replace('.', ''))
            sort_ratio = fuzz.token_sort_ratio(str(names).lower().replace(
                '.', ''), str(company).lower().replace('.', ''))
            set_ratio = fuzz.token_set_ratio(str(names).lower().replace(
                '.', ''), str(company).lower().replace('.', ''))

            if jratio > 80 or sort_ratio > 80 or set_ratio > 80:
                org_comparison_result.append((names, jratio))
                # print(names," -- ",company,jratio)

    max_score = 0
    final_vendor_name = ""
    for score in org_comparison_result:
        if max_score < score[1]:
            max_score = score[1]
            final_vendor_name = score[0]

    return final_vendor_name

# Get Company Names
#
# # Get Company Names:


def remov_duplicates(input):
    input = input.split(" ")
    for i in range(0, len(input)):
        input[i] = "".join(input[i])
    UniqW = Counter(input)
    s = " ".join(UniqW.keys())
    return s




class InvoiceRest(APIView):

    invoice_total_counter = []

    def extractToFolder(self, filename, extract_folder):
        if not os.path.exists(extract_folder):
            os.mkdir(extract_folder)
        print("To Be Extracted", filename)
        print("extract folder", extract_folder)
        doc = fitz.open(filename)
        print("Number of Pages ", len(doc))
        for i in range(len(doc)):

            for img in doc.getPageImageList(i):
                xref = img[0]
                pix = fitz.Pixmap(doc, xref)
                if pix.n < 5:       # this is GRAY or RGB
                    pix.writePNG(extract_folder + "p%s-%s.png" % (i, xref))
                    print(extract_folder + "p%s-%s.png" % (i, xref))
                else:               # CMYK: convert to RGB first
                    pix1 = fitz.Pixmap(fitz.csRGB, pix)
                    pix1.writePNG(extract_folder+"p%s-%s.png" % (i, xref))
                    print(extract_folder + "p%s-%s.png" % (i, xref))
                    pix1 = None
                pix = None
        print("Data Extracted.....")

    def reader(self, file):
        with open(file, "rb") as image_file:
            img_test = image_file.read()
            bytes_test = bytearray(img_test)
        return bytes_test

    def get(self, request):
        res = {"response": "ok"}
        return HttpResponse(res)

    # @csrf_exempt
    def post(self, request):
        result = {}
        config_name=None
        # Construction of JSON Structures
        try:
            extracted_fields = {}
            form_fields = []

            json_body = json.loads(request.body.decode("utf-8"))
            base64__ = json_body["data"]
            mode = json_body["mode"]
            invoice_type = json_body["type"]
            print(base64__)
            print("********")

            #Finding the configuration 
            if "config_name" in json_body:
                config_name=json_body["config_name"]
            else:
                config_name="global_config"    
            

            #checking if PDF FOLDER exists or not 
            #If PDF Folder does not exists , create it 

            if not os.path.isdir(BASE_DIR+'/Invoice/pdf'):
                os.mkdir(BASE_DIR+'/Invoice/pdf')
                print("pdf folder created...")
            
            unique_filename=None
            if "extraction_id" in json_body.keys():
                unique_filename=json_body["extraction_id"]
            
            else:
                unique_filename = str(uuid.uuid4())

            #db_logger.info("Invoice Path "+invoice_path,{"user": str(request.user) ,"entity":unique_filename} )
            

            #PATH where main invoice is stored 
            if invoice_type == "pdf":
                invoice_path = base_path+unique_filename+".pdf"
            else:
                invoice_path = base_path+unique_filename+".png"
               



            #Saving the Invoice PDF/PNG
            with open(os.path.expanduser(invoice_path), 'wb') as fout:
                fout.write(base64.b64decode(base64__))
            #print("File Written "+invoice_path)
            fout.close()

            images_list={}
            json_list={}
            folder_list=[]
            mapping_list=[]

            images_count=0
            #convert the pdf into image 
            if invoice_type=="pdf":
                images = convert_from_path(invoice_path, size=(2000, None))
                #for im in images:
                #im.save(base_path+unique_filename+".png")
                
                for i in images:
                    i.save(base_path+unique_filename+"_"+str(images_count)+".png")
                    images_list[images_count]=base_path+unique_filename+"_"+str(images_count)+".png"
                    images_count=images_count+1
            else:
                i = Image.open(invoice_path)
                i.save(base_path+unique_filename+"_"+str(images_count)+".png")
                images_list[images_count]=base_path+unique_filename+"_"+str(images_count)+".png"
                images_count=images_count+1
                
                #print("exiting program....")
                #raise ValueError('A very specific bad thing happened.')

            
            image_keys=list(images_list.keys())
            image_keys.sort()

            #Creating AMAZON Textract Instance 
            # Amazon Textract client
            textract = boto3.client('textract','us-east-1')
               

            # Instance of boto3 client 
            client = boto3.client(
                service_name='textract',
                region_name='us-east-1',
                endpoint_url='https://textract.us-east-1.amazonaws.com',aws_access_key_id="********************",
                        aws_secret_access_key="6629LxPd+So34S4EJPxsxs0KOvCwt/AwrjZkAHwN"
            ) 
            line_items_dict=[]
            lines=""
            flat_fields=None
            header_list=[]
            footer_list=[]

            invoice_checklist={}
            AI_Fields={}
            Form_Present=False
            #Iterating thorough all the images found  
            
            for key in image_keys:

                """
                if key>2:
                    break
                """
                inv_page_path=images_list[key]
                print("scanning page no ",key)

                
               

                
                
                if key>=0:
                    #check if it is invoice or not
                    
                    inv_page_path_1=None
                    I_res_1=None

                    if key+1 in images_list:
                        inv_page_path_1=images_list[key+1]

                        if inv_page_path_1 is not None:
                            I_res_1=predictInvoice(TFmodel2,inv_page_path_1)
                        
                        print("Invoice -Invoice Ml Prediction ")
                        print(key+1,I_res_1) 

                         #check if NON-Invoice confidence is greater than equal to 70 
                        if I_res_1["NI"]>=0.80:
                            invoice_checklist[key+1]="NI"
                            invoice_checklist["last_invoice"]=key
                        else:
                            invoice_checklist[key+1]="I"
                            #print("Breaking ... out as page no : ",key," is Non Invoice" )
                            #break

                    


                        #I_res=predictInvoice(TFmodel2,inv_page_path)
                    
                    



                    
                   
                         

                json_path = base_path+unique_filename+'_'+str(key)+'invoice.json'
                mapping_file=BASE_DIR+'/Invoice/pdf/'+unique_filename+'_'+str(key)+'mapping.json'
                csv_folder=BASE_DIR+'/Invoice/pdf/'+unique_filename+'_'+str(key)+"csv"
                base_extract = BASE_DIR+'/extract/'
                db_logger.info("File Extracted Sucessfully",{"user": str(request.user) ,"entity":unique_filename} )
                
                #adding json lists :
                json_list[key]=json_path
                folder_list.append(json_path.replace('.json','-csv'))
                mapping_list.append(mapping_file)
              
                
                
                
                
                
                
                #Skew Correction 
                #skew logic is bugged in some cases 
                #skewImage(invoice_path)

                # invoice_file_=""
                # handling the case when its pdf

                # print("Invoice file ",invoice_file_)
                # invoice_path=base_extract+"/"+invoice_file_
 

                #Reading the Image 
                
                with open(inv_page_path, "rb") as image_file:
                    base64_encoded_string = base64.b64encode(image_file.read())

                data_str = self.reader(inv_page_path)    
                
                # result["base64"]=base64_encoded_string
                # Perform OCR To invoice
                

                feature_field=""
                #Forms should only be fetched for First Invoice

                if key==0 or key==(len(images_list)-1) :
                   
                    feature_field=['TABLES','FORMS']
                    Form_Present=True
                else:
                    if ("last_invoice" in invoice_checklist) and  (invoice_checklist["last_invoice"]==key):
                        feature_field=['TABLES','FORMS']
                        Form_Present=True
                    else:
                        feature_field=['TABLES']
                        Form_Present=False
                
                print("Features Used ",feature_field)

                #################### PERFORMING OCR ##################
                if "extraction_id" not in json_body.keys() :
                    print("fetching Boto Response .....")
                    response = client.analyze_document(
                        Document={'Bytes': data_str}, FeatureTypes=feature_field)
                    with open(json_path, 'w') as fp:
                        json.dump(response, fp)

                    print("JSON File Written ...")

                    db_logger.info("OCR Successful",{"user": str(request.user) ,"entity":unique_filename} )

                #################### PERFORMING OCR ################## 




                #Extract AI Fields and merge into original fields 
                if Form_Present:
                    print("getting values for ",json_path)
                    temp_dict=get_raw_values(json_path)
                    print(key," temp_dict found ",temp_dict)
                    AI_Fields.update(temp_dict)


                # CREATING Objects
                table_util_obj = TableUtils()
                extract_util_obj = ExtractUtils()
                extract_obj = Extract()
                 

                #image_path = invoice_path #DupV
          
                #csv_path = base_path+unique_filename+'csv' #DupV

                # Loading Configuration File

                # Finding Dimensions of Image
                im = Image.open(inv_page_path)
                width, height = im.size

                # calling TbExtract - Table Extraction from JSON

                csv_path = table_util_obj.parse_main(inv_page_path, json_path, height,mapping_file)
                
                # Extracting Header Items
                csv_file=None
                if csv_path is not None :
                    csv_file = extract_util_obj.findLineItemTable_V2(csv_path)
                    print("csv file name .....",csv_file) 

                if csv_file==None:
                    db_logger.info("No LineItems Detected",{"user": str(request.user) ,"entity":unique_filename} )
                    #raise ValueError('No Line Items Detected')
                
                #############TEMP COMMENETED SHOULD BE UNCOMMENTED ######################################
                #db_logger.info("Line Item csv file "+ str(csv_file),{"user": str(request.user) ,"entity":unique_filename} )
                  
                # Read the mapping file and get Dimensions of Table
                #check if csv file is not none 
                table_dimensions=[0,0]
                df=None
                if csv_file is not None :
                    mapping_data = ""
                    csv_file_path=BASE_DIR +'/Invoice/pdf/'+unique_filename+'_'+str(key)+'invoice-csv/'+csv_file
                    with open(mapping_file) as f:
                        mapping_data = json.load(f)
                    table_dimensions = mapping_data[csv_file_path]
                
                    table_confidence=table_dimensions[2]
                    print("table confidence ",table_confidence)


                    #line_items_list,top,bottom=extract_util_obj.findLineItems_V2(base_path+'invoice.json')
                    print("csv file_path ",csv_file_path)
                    #try:
                    df,top,bottom=extract_util_obj.findLineItems_V3( csv_file_path, json_path)
                    if df.empty:
                        df=extract_util_obj.findLineItemTable_raw(BASE_DIR +'/Invoice/pdf/'+unique_filename+'_'+str(key)+'invoice-csv/')
                
                
                    #Cleaning Data Frame 
                    nan_value = float("NaN")  
                
                
                    if df is not None:
                        if not df.empty:
                            df.replace("", nan_value, inplace=True)
                            df=df.dropna(how='all',axis=1) 


                    #Break out of the Loop 
                    # If No Line Items are found in Page number
                    # Greater than 1

                    if key>0 and df is None:
                        print("No Line Items Found on page Number ",key)
                        print("\n Hence Breaking OUT....")  
                        break      

                    print("data frame *************")
                    print(df)
                    print("data frame**************")
                
                #check of csv file None type finishes here 

                 # Finding Table Dimensions
                extract_util_obj.getHeight(inv_page_path)
                # extract_util_obj.getTableDimensions(json_path)
                # Finding Table Dimensions
                #extract_util_obj.getHeight(inv_page_path)
                # extract_util_obj.getTableDimensions(json_path)
                header_list = extract_util_obj.createText(
                json_path, extract_util_obj.height, extract_util_obj.width, table_dimensions[0], table_dimensions[1])
                footer_list = extract_util_obj.createTextFooter(
                json_path, extract_util_obj.height, extract_util_obj.width, table_dimensions[0], table_dimensions[1])

                ########### EXTRACTING FIELDS START ###########################

                #call formfields by Distance method 
                #Form Field will be only fetched for First Invocie 
                if Form_Present:

                    form_fields=getFormFieldsbyDistance(config_name,form_fields,json_path,table_dimensions)
                 
                    #call form fields search by linear method  
                    form_fields,lines,flat_fields=getFormFieldsbyLinear(config_name,form_fields,json_path,table_dimensions)
                
                ########### EXTRACTING FIELDS ENDS ###########################

            
                confidence_dict=extract_util_obj.get_confidence_matrix(json_path)
                print("data frame ",df)
                
                temp_list=[]
                if df is not None:
                    if not df.empty:
                        temp_list=extract_util_obj.df_to_dict(df,confidence_dict)
                        line_items_dict.append(temp_list) 

                if (key>0):
                    if invoice_checklist[key]=="NI":
                        print(key , " is NI")
                        break

            
            #combile all the line items into one list 
            #for maintaining the structure 
            
            combined_line_items=[]
            for i in line_items_dict:
                combined_line_items=combined_line_items+i

            
            #extracted_fields["line_items"]=line_items_dict
            extracted_fields["line_items"]=combined_line_items
           
            ##############  Vendor Extraction Start ###############################
            
            ven_res=getVendors(TF_OB_model,images_list[0],json_list[0])
            #temporary vendor logic 

            #vendor_match=getVendorbycsvMatch(header_list)
            data={"vendor_name":ven_res[0],"confidence_level":ven_res[1] }
            form_fields.append(data)


            ############## Vendor Extraction End ###############################

            
            #if extract_raw:
            #raw_dict=get_raw_values(json_list[0])
            raw_dict=AI_Fields
            temp_data={} 
            
            print("AI Fields ", AI_Fields)

            field_match=FieldMatch()
            db_extrtaction_list=get_extraction_list(config_name,None)
            w_list=field_match.cerate_word_list(db_extrtaction_list)
           
            for key, value in raw_dict.items():
                    #print(key,' -- ',value)
                    #checking duplicate for extracted_field
                    #print("Match Field  checking ",key)
                    check_res=field_match.get_field_label(key,w_list) 
                    
                    key=key.replace(":","").replace(",","")

                    if check_res[1]==None:
                        data={key.strip():value[0].strip(),"confidence_level":value[1]}
                    else:
                        data={check_res[1].strip():value[0].strip(),"confidence_level":value[1]}
                    
                    form_fields.append(data)
                    """
                    #check for any duplicate present in the list 
                   
                    for d in form_fields:
                        if check_res[1] in list(d.keys()):
                            print("Duplicate found ",d , " :for ",check_res[1]," , Match_score :")
                            form_fields.remove(d)
                            

                            #form_fields.append(d)
                            temp_val=d[check_res[1]]
                            d[check_res[1]+"_duplicate"]=temp_val
                            t=d
                            del d[check_res[1]]
                            form_fields.append(t)
                    





                    
                    form_fields.append(data)
                    #temp_data[key]=value
                    """
            
                


                



            

            #Embedding Image Field 
            base64_data={"base64":base64__}

            #If Setting in Response 
            if image_in_response:
                extracted_fields["base64"]=base64__
            
            extracted_fields["extraction_id"]=unique_filename
            

            
            extracted_fields["uid"]=unique_filename
            extracted_fields["form_fields"]=form_fields
            extracted_data={}
            response_data={"extracted_data":extracted_fields}

            # print("extracted fields ",extract_fields)
            
            # Remove csv files extracted 
            


            if not mode=="test" : #"extraction_id" not in json_body.keys() :
                import shutil
                #removing folder
                

                #Remove folder list  
                for item in folder_list:
                    shutil.rmtree(item)
                
                #removing all json files 
                for item in json_list:
                    os.remove(json_list[item] )
                
                #remove all the mapping files 
                for item in mapping_list:
                    os.remove(item)
                
                for item in images_list:
                    os.remove(images_list[item] )


               
                

                if path.exists(invoice_path):
                    os.remove(invoice_path)
                
                print("all files cleaned ......")
            
           
             
            
            


           
            
            return HttpResponse(json.dumps(response_data,indent=4, sort_keys=True, default=str)) 
        
        except ValueError as e:
            import traceback
            print(traceback.format_exc())
            error={}
            error["error"]=str(e)
            print("msg",e)
            db_logger.exception("Line Item Exception",{"user": str(request.user) ,"entity":unique_filename})
            return HttpResponse(json.dumps(error,indent=4, sort_keys=True, default=str))
        
        except Exception as e:
            import traceback
            print(traceback.format_exc())
            error={}
            error["error"]="Invalid JSON/Corruption JSON values"
            print("Error ",str(e))
            return HttpResponse(json.dumps(str(e),indent=4, sort_keys=True, default=str))
        
         
