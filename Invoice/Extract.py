# Import Lex Libraries
"""
from lexnlp.extract.en.conditions import *
from lexnlp.extract.en.acts import *
from lexnlp.extract.en.amounts import *
from lexnlp.extract.en.citations import *
from lexnlp.extract.en.conditions import *
from lexnlp.extract.en.copyright import *

from lexnlp.extract.en.constraints import *
from lexnlp.extract.en.courts import *
from lexnlp.extract.en.dates import *
from lexnlp.extract.en.definitions import *
from lexnlp.extract.en.distances import *
from lexnlp.extract.en.durations import *
#from lexnlp.extract.en.ge import *
from lexnlp.extract.en.money import *
from lexnlp.extract.en.percents import *
from lexnlp.extract.en.regulations import *
from lexnlp.extract.en.trademarks import *
from lexnlp.extract.en.entities.nltk_re import *
# Import all annotations
from lexnlp.extract.common.annotations.condition_annotation import *
from lexnlp.extract.en.addresses.addresses import *
"""
from pyjarowinkler import distance as d


from pyjarowinkler import distance
from fuzzywuzzy import fuzz
from fuzzywuzzy import process
from collections import defaultdict
import re
from AMS.extract_settings import *
from price_parser import Price

import spacy
from spacy.matcher import Matcher
import re 



alpha_neumeric_regex = "([$a-zA-Z0-9/-]+[0-9]+)|([0-9+]+[$0-9a-zA-Z_/-]+)$"
date_name_parser = "\w+\s+(date)"
float_regex = "\d+\.\d+"
num_string = "[0-9][0-9.]*[0-9]"
string="[a-zA-Z0-9].*"


def getSearchableRow(dataframe, searchlist):

    return_list = []
    for index, row in dataframe.iterrows():

        for k, v in row.iteritems():

            for item in searchlist:

                jratio = d.get_jaro_distance(str(v).lower().replace(
                    '.', ''), str(item).lower().replace('.', ''))

                lratio = fuzz.partial_ratio(str(v).lower().replace(
                    '.', ''), str(item).lower().replace('.', ''))

                if (jratio*100) > 85 or lratio > 85:
                    return_list.append(index)

                    #
                    # print(row)

    return return_list


class Extract:

    # threshhold for beam search
    threshold = 0.4

    def fuzzMatch(self, dict_, search_list,nlp):

        word_list = []

        for h in dict_["head"]:
            for t in dict_["tail"]:
                word_list.append(h+" "+t)

        print("Words list ......")
        print(word_list)

        match_result = []
        res=None
        for word in word_list:
            for s_item in search_list:
                if not (len(s_item.split(" ")) > 1):
                    continue

                # Fuzzy Sort Token ratio
                # match_ratio=fuzz.token_set_ratio(word.lower().strip(),s_item.lower().strip())
                # match_f_sort=fuzz.partial_ratio(word.lower().strip(),s_item.lower().strip())
                match_ratio = fuzz.ratio(
                    word.lower().strip(), s_item.lower().strip())

                # JaroW Distance
                match_f = distance.get_jaro_distance(
                    word.lower().strip(), s_item.lower().strip())
                match_f = match_f*100
                if match_ratio > match_f:
                    match_f = match_ratio
                #print(word.lower(),s_item.lower(),match_f)

                if(dict_["type"] == "Alpha" or dict_["type"] == "Date"):
                    res = re.search(alpha_neumeric_regex, s_item)
                elif(dict_["type"] == "numstring"):
                    res = re.search(num_string, s_item)
                elif(dict_["type"] == "rule"):
                    res = re.search(string, s_item)
                
                #print("result ",match_f,res)
                # res=re.search(alpha_neumeric_regex,s_item)
                if res == None:
                    continue

                if match_f > 70:
                    match_result.append((word, s_item, match_f))

        # Find Highiest Match and Return Data
        print("match Results ************************")
        print(match_result)
        count = 0
        max_el = []
        max_score = 0
        for item in match_result:
            if count == 0:

                max_score = item[2]
                max_el = item

            if item[2] > max_score:
                max_score = item[2]
                max_el = item
            count = count+1

        # Extract value using Regular Expression:
        #print("match results ",match_result)

        print("max element ", max_el)
        res=None
        match_res=None
        if len(max_el) > 0:
            item = max_el[1]
            item = item.replace('$', '').replace(',', '')
            return_list = list(max_el)
            print("dictiory ", dict_)
            
            print("dict type",dict_["type"]," ")

            if "rule" in dict_.keys():
                matcher = Matcher(nlp.vocab)
                pattern_dict=dict_["rule"]

                for key in pattern_dict.keys():
                    matcher.add(key, None, pattern_dict[key])

                
                doc = nlp(item)
                matches = matcher(doc)
                print("matches",matches,"rule",dict_["rule"] )
                for match_id, start, end in matches:
                    span = doc[start:end]
                    match_res=span.text
            else:

                if dict_["type"] == "Alpha":
                    print("item", item)
                    res = re.search(alpha_neumeric_regex, item)
                    print("regex result ", item, res)
                    if res:
                        return_list.append(res.group())
                elif dict_["type"] == "Price":
                    price = Price.fromstring(item)
                    return_list.append(price.amount_float)
                elif dict_["type"] == "numstring":
                    res = re.search(num_string, item)
           

                      
            print("total match  ", item, res)
           

            if res:
                return_list.append(res.group())
            elif match_res:
                return_list.append(match_res)

           


            return return_list

        else:
            return None

        return return_list

    def date_match(self, dict_, dates_list):
        try:

            word_list = []
            for h in dict_["head"]:
                for t in dict_["tail"]:
                    word_list.append(h+" "+t)

            print("Words list ......")
            print(word_list)

            match_result = []

            for word in word_list:
                for s_item in dates_list:
                    item = s_item[0]

                    # Fuzzy Sort Token ratio
                    # match_f=fuzz.token_set_ratio(word.lower().strip(),s_item.lower().strip())
                    # match_f_sort=fuzz.token_sort_ratio(word.lower().strip(),s_item.lower().strip())

                    # JaroW Distance
                    match_f = distance.get_jaro_distance(
                        word.lower().strip(), item.lower().strip())
                    match_f = match_f*100
                    # print(word.lower(),s_item.lower(),match_f)
                    if match_f > 70:
                        match_result.append((word, s_item, match_f))

            # Find Highiest Match and Return Data
            # print(match_result)
            count = 0
            max_el = []
            max_score = 0
            for item in match_result:
                if count == 0:

                    max_score = item[2]
                    max_el = item

                if item[2] > max_score:
                    max_score = item[2]
                    max_el = item
                count = count+1

            # Extract value using Regular Expression:
            #print("match results ",match_result)

            print("element extracted", max_el)
            if len(max_el) > 0:
                item = (max_el[1][1]["Extracted Entity Date"], max_el[2])
                return item
            else:
                return None

        except Exception as ex:
            import traceback
            print(traceback.format_exc())

            return None

    # For Matching NLP Results
    def nlp_match(self, dict_, search_list, nlp):
        entity_name = dict_["entity_name"]
        result = []

        for item in search_list:
            with nlp.disable_pipes('ner'):
                doc = nlp(item)

            entity_scores = defaultdict(float)
            beams = nlp.entity.beam_parse(
                [doc], beam_width=16, beam_density=0.0001)

            for beam in beams:
                for score, ents in nlp.entity.moves.get_beam_parses(beam):
                    for start, end, label in ents:
                        entity_scores[(start, end, label)] += score

            for key in entity_scores:
                start, end, label = key
                score = entity_scores[key]
                if (score > self.threshold and label == entity_name):
                    result.append((doc[start:end], label, score))
                    #print ('Label: {}, Text: {}, Score: {}'.format(label, doc[start:end], score))

        return result

    # Searching Items with Regular Expression
    def regex_search(self, regex_list, search_list, type,nlp):
        return_list = []
        print("regex_list", regex_list)
        for regex in regex_list:

            for item in search_list:
                #print("regex ",regex)
                res = re.search(regex, item.lower())

                res2 = None
                res_match=None
                if res:
                    item = res.group()
                    item = item.replace(",", "")
                    print("result", item.lower().replace(',', ''),  regex)

                    if type == "Alpha":
                        res2 = re.search(alpha_neumeric_regex, item)
                    elif type == "Float":
                        res2 = re.search(float_regex, item.replace('$', ''))
                    elif type == "numstring":
                        res2 = re.search(num_string, item.replace('$', ''))
                    elif type == "rule":
                        #prepare the matcher object
                        matcher = Matcher(nlp.vocab)
                        pattern=item_dict["rule"] 
                        matcher.add("rule1", None, pattern)
                        doc = nlp(item)
                        matches = matcher(doc)
                        for match_id, start, end in matches:
                            span = doc[start:end]
                            res_match=span.text

                      
                    print("total match  ", item, res2)
                    print("rule match ",res_match)

                    if res2:
                        return_list.append(res2.group())
                    elif res_match:
                        return_list.append(res_match)


        return return_list

    # Extracts dates by Speciall pattern Recognition Models
    def extract_dates_pattern(self, search_list):
        result_list = []
        for item in search_list:
            dates = get_date_annotations(item)

            for i in dates:
                res = re.search(alpha_neumeric_regex, item)
                if res:
                    value = res.group()
                    key = item.replace(value, "").strip()
                    result_list.append(
                        (key, i.get_dictionary_values()["tags"]))

        return result_list

    # Extract Company names By Pattern Recognition Model
    def extract_company_pattern(self, search_list):
        result_list = []

        for item in search_list:
            company = get_companies(item)

            for i in company:
                result_list.append(i)

        return result_list

    # Extract Line Items from PreExtracted CSV Files
    def getLineItems(self, csv_path):
        df_csv = pd.read_csv(csv_path, delimiter=',')
        df_csv.columns = df_csv.columns.str.strip()
        total_rows = getSearchableRow(df_csv, total_items)
        if len(total_rows) > 0:
            total_row_data = df_csv.loc[total_rows[0]].to_dict()
            df_csv1 = df_csv.drop(total_rows, axis=0)
            df_csv = df_csv1

        return df_csv.to_dict()
