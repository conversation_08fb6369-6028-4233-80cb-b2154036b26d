# Generated by Django 3.1.1 on 2020-10-09 13:01

from django.db import migrations, models
import django.db.models.deletion
import jsonfield.fields


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='ExtractField',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(db_index=True, max_length=50)),
                ('description', models.TextField(blank=True, max_length=500, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='kvalue',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('f_name', models.CharField(db_index=True, max_length=240)),
                ('f_type', models.CharField(choices=[('Alpha', 'Alphaneumeric'), ('Date', 'Date'), ('Float', 'Float'), ('Money', 'Money'), ('String', 'String'), ('Int', 'Int'), ('Numstring', 'Numstring'), ('Rule', 'Rule')], max_length=240)),
                ('f_head', models.TextField(blank=True, max_length=2000)),
                ('f_tail', models.TextField(blank=True, max_length=2000)),
                ('f_strategy', models.CharField(blank=True, max_length=240)),
                ('f_rule', jsonfield.fields.JSONField(blank=True, default={}, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='MlModel',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(db_index=True, max_length=100, unique=True)),
                ('path', models.TextField(max_length=500)),
                ('active', models.BooleanField()),
            ],
        ),
        migrations.CreateModel(
            name='Regex',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(db_index=True, max_length=100, unique=True)),
                ('description', models.TextField(blank=True, max_length=500, null=True)),
                ('regex', models.CharField(max_length=200)),
            ],
        ),
        migrations.CreateModel(
            name='RegexGroupName',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(db_index=True, max_length=50)),
                ('description', models.TextField(blank=True, max_length=500, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='RegexGroupValue',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('field', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Invoice.regexgroupname')),
                ('value', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Invoice.regex')),
            ],
        ),
        migrations.CreateModel(
            name='ExtractFieldMapping',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('field', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Invoice.extractfield')),
                ('value', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Invoice.kvalue')),
            ],
        ),
        migrations.CreateModel(
            name='Configuration',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(db_index=True, max_length=100, unique=True)),
                ('description', models.TextField(blank=True, max_length=500, null=True)),
                ('extract_field', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Invoice.extractfield')),
            ],
        ),
    ]
