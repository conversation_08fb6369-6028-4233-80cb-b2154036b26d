# Generated by Django 3.1.1 on 2022-05-11 13:58

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import jsonfield.fields
import simple_history.models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('Invoice', '0019_auto_20220415_0006'),
    ]

    operations = [
        migrations.AddField(
            model_name='configuration',
            name='ai_fieldmatch_threshold',
            field=models.IntegerField(default=95),
        ),
        migrations.AlterField(
            model_name='kvalue',
            name='f_type',
            field=models.CharField(choices=[('Alpha', 'Alphaneumeric'), ('Date', 'Date'), ('Float', 'Float'), ('Numstring', 'Numstring'), ('Rule', 'Rule'), ('Rule for Value', 'Rule for Value'), ('Regex', 'Regex'), ('Address', 'Address')], max_length=240),
        ),
        migrations.CreateModel(
            name='Historicalkvalue',
            fields=[
                ('id', models.IntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('f_name', models.CharField(db_index=True, max_length=240)),
                ('display_name', models.CharField(blank=True, max_length=240)),
                ('f_type', models.CharField(choices=[('Alpha', 'Alphaneumeric'), ('Date', 'Date'), ('Float', 'Float'), ('Numstring', 'Numstring'), ('Rule', 'Rule'), ('Rule for Value', 'Rule for Value'), ('Regex', 'Regex'), ('Address', 'Address')], max_length=240)),
                ('f_head', models.TextField(blank=True, max_length=2000)),
                ('f_tail', models.TextField(blank=True, max_length=2000)),
                ('f_strategy', models.CharField(blank=True, max_length=240)),
                ('f_rule', jsonfield.fields.JSONField(blank=True, default={}, null=True)),
                ('f_algo_type', models.CharField(choices=[('NearestDistance', 'NearestDistance'), ('Horizontal', 'Horizontal')], max_length=240)),
                ('masking', models.BooleanField(default=True)),
                ('match_threshhold', models.IntegerField(default=85)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField()),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('f_regex_field', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='Invoice.regexgroupname')),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'historical kvalue',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': 'history_date',
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
    ]
