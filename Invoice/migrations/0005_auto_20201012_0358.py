# Generated by Django 3.1.1 on 2020-10-12 03:58

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('Invoice', '0004_auto_20201009_1340'),
    ]

    operations = [
        migrations.AddField(
            model_name='extractfieldmapping',
            name='active',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='kvalue',
            name='f_algo_type',
            field=models.CharField(choices=[('NearestDistance', 'NearestDistance'), ('Horizontal', 'Horizontal')], default='NearestDistance', max_length=240),
        ),
    ]
