# Generated by Django 3.1.1 on 2022-06-01 13:45

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('Invoice', '0020_auto_20220511_1358'),
    ]

    operations = [
        migrations.CreateModel(
            name='Query',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('displayname', models.CharField(max_length=200)),
                ('fieldname', models.Char<PERSON>ield(max_length=200)),
                ('query', models.TextField(blank=True, max_length=500, null=True)),
                ('description', models.TextField(blank=True, max_length=500, null=True)),
                ('vendorfieldnames', models.TextField(max_length=5000)),
                ('missingfieldnames', models.TextField(max_length=5000)),
                ('extractfield', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Invoice.extractfield')),
            ],
        ),
    ]
