# Generated by Django 3.1.1 on 2021-06-15 11:09

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('Invoice', '0013_addressentity'),
    ]

    operations = [
        migrations.AddField(
            model_name='addressentity',
            name='name',
            field=models.CharField(default='one', max_length=200),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='addressentity',
            name='entity',
            field=models.CharField(default='PLACE', max_length=200),
            preserve_default=False,
        ),
        migrations.CreateModel(
            name='TaxItem',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('taxlist', models.TextField(max_length=5000)),
                ('description', models.TextField(max_length=500)),
                ('extractfield', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Invoice.extractfield')),
            ],
        ),
        migrations.CreateModel(
            name='GeneralEntity',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('fieldtype', models.CharField(choices=[('ORG', 'ORG')], max_length=240)),
                ('fullstring', models.TextField(max_length=500)),
                ('entity', models.CharField(max_length=200)),
                ('extractfield', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Invoice.extractfield')),
            ],
        ),
    ]
