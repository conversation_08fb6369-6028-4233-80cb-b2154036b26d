# Generated by Django 3.1.1 on 2021-09-27 09:47

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('Invoice', '0016_lineitemexclusion'),
    ]

    operations = [
       
       
      
        migrations.AddField(
            model_name='configuration',
            name='max_pagescan',
            field=models.IntegerField(default=10),
        ),
        #"""
        migrations.AddField(
            model_name='lineitemheaders',
            name='display_name',
            field=models.CharField(db_index=True, default=django.utils.timezone.now, max_length=100, unique=True),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='lineitemheaders',
            name='name',
            field=models.CharField(db_index=True, max_length=100),
        ),
        migrations.CreateModel(
            name='VendorFields',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('fieldnames', models.TextField(max_length=5000)),
                ('extractfield', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Invoice.extractfield')),
            ],
        ),
        #""" 
  
    ]

"""
 migrations.AddField(
            model_name='configuration',
            name='ai_fieldmatch_threshold',
            field=models.IntegerField(default=95),
        ),
"""