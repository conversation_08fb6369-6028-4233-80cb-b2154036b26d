# Generated by Django 3.1.1 on 2021-08-04 12:18

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('Invoice', '0015_auto_20210615_1116'),
    ]

    operations = [
        migrations.CreateModel(
            name='LineItemExclusion',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(db_index=True, max_length=100, unique=True)),
                ('type', models.CharField(max_length=100)),
                ('field_list', models.TextField(max_length=5000)),
                ('active', models.BooleanField()),
                ('field', models.ForeignKey(default=None, on_delete=django.db.models.deletion.CASCADE, to='Invoice.extractfield')),
            ],
        ),
    ]
