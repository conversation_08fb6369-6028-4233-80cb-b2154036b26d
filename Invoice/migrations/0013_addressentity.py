# Generated by Django 3.1.1 on 2021-06-03 10:59

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('Invoice', '0012_auto_20210601_1550'),
    ]

    operations = [
        migrations.CreateModel(
            name='AddressEntity',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('fieldtype', models.CharField(choices=[('PLACE', 'PLACE'), ('CITY-COUNTRY', 'CITY-COUNTRY'), ('EXCLUDE', 'EXCLUDE')], max_length=240)),
                ('longstring', models.TextField(max_length=500)),
                ('entity', models.CharField(blank=True, max_length=200, null=True)),
                ('extractfield', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Invoice.extractfield')),
            ],
        ),
    ]
