import io
import os
import scipy.misc
import numpy as np
import six
import time
import glob
#from IPython.display import display

from six import BytesIO

import matplotlib
import matplotlib.pyplot as plt
from PIL import  ImageDraw, ImageFont
#import Image

import tensorflow as tf
from object_detection.utils import ops as utils_ops
from object_detection.utils import label_map_util
from object_detection.utils import visualization_utils as vis_util
from PIL import Image
from AMS.extract_settings import *
import cv2


lb_path_=None

if run_mode=='local':
    lb_path_="labelmap.pbtxt"
else:
    lb_path_="../labelmap.pbtxt"
    
category_index = label_map_util.create_category_index_from_labelmap(lb_path_, use_display_name=True)

def load_image_into_numpy_array(path):


    """Load an image from file into a numpy array.

    Puts image into numpy array to feed into tensorflow graph.
    Note that by convention we put it into a numpy array with shape
    (height, width, channels), where channels=3 for RGB.

    Args:
        path: a file path (this can be local or on colossus)

    Returns:
        uint8 numpy array with shape (img_height, img_width, 3)
    """
    img_data = tf.io.gfile.GFile(path, 'rb').read()
    image = Image.open(BytesIO(img_data))
    (im_width, im_height) = image.size
    return np.array(image.getdata()).reshape(
        (im_height, im_width, 3)).astype(np.uint8)

def run_inference_for_single_image(model, image):

    image = np.asarray(image)
    # The input needs to be a tensor, convert it using `tf.convert_to_tensor`.
    input_tensor = tf.convert_to_tensor(image)
    # The model expects a batch of images, so add an axis with `tf.newaxis`.
    input_tensor = input_tensor[tf.newaxis,...]

    # Run inference
    model_fn = model.signatures['serving_default']
    output_dict = model_fn(input_tensor)

    # All outputs are batches tensors.
    # Convert to numpy arrays, and take index [0] to remove the batch dimension.
    # We're only interested in the first num_detections.
    num_detections = int(output_dict.pop('num_detections'))
    output_dict = {key:value[0, :num_detections].numpy() 
                    for key,value in output_dict.items()}
    output_dict['num_detections'] = num_detections

    # detection_classes should be ints.
    output_dict['detection_classes'] = output_dict['detection_classes'].astype(np.int64)
    
    # Handle models with masks:
    if 'detection_masks' in output_dict:
        # Reframe the the bbox mask to the image size.
        detection_masks_reframed = utils_ops.reframe_box_masks_to_image_masks(
                output_dict['detection_masks'], output_dict['detection_boxes'],
                image.shape[0], image.shape[1])      
        detection_masks_reframed = tf.cast(detection_masks_reframed > 0.5,
                                        tf.uint8)
        output_dict['detection_masks_reframed'] = detection_masks_reframed.numpy()
        
    return output_dict

def predict(model,image,im_width,im_height):

    image_path=image
    #image_np = load_image_into_numpy_array(image_path)
    image_np = cv2.imread(image_path,1)
    
    output_dict = run_inference_for_single_image(model, image_np)

    boxes = np.squeeze(output_dict['detection_boxes'])
    scores = np.squeeze(output_dict['detection_scores'])
    #set a min thresh score, say 0.8
    min_score_thresh = 0.5
    bboxes = boxes[scores > min_score_thresh]

    final_box = []

    b_index=0
    conf=0
    for index, g in enumerate(scores):
        if (g > min_score_thresh):
            print(index,g)
            b_index=index
            conf=g
        
    print(" index ",b_index," conf : ",conf)
    #print(boxes[b_index])
    #bboxes=boxes[b_index]
    for box in bboxes:
        ymin, xmin, ymax, xmax = box
        #print(box)
        
        final_box.append([xmin * im_width, xmax * im_width, ymin * im_height, ymax * im_height])
        #print("final box ",final_box)
    print("final box",final_box)
    if len(final_box)>0:
        return (final_box[0],conf)
    else:
        return None


    



