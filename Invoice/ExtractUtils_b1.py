from fuzzywuzzy import fuzz 
from fuzzywuzzy import process 
from pyjarowinkler import distance
import os 
#PIL Imports
from PIL import Image
import math
import json
#Import Settings for 
from AMS.extract_settings import *
import spacy
from spacy.tokens import Doc

from collections import Counter 
from pyjarowinkler import distance as d
from scipy.spatial import distance as sp_distance
from  AMS.settings import BASE_DIR
import pandas as pd 
from spacy.matcher import Matcher
import csv
import pandas as pd 




#Custom Tokenizer for tokeizing with whitespace 
class WhitespaceTokenizer(object):
    def __init__(self, vocab):
        self.vocab = vocab

    def __call__(self, text):
        words = text.split(' ')
        # All tokens 'own' a subsequent space character in this tokenizer
        spaces = [True] * len(words)
        return Doc(self.vocab, words=words, spaces=spaces)

class TableUtils:


    def get_rows_columns_map(self,table_result, blocks_map):
        rows = {}
        for relationship in table_result['Relationships']:
            if relationship['Type'] == 'CHILD':
                for child_id in relationship['Ids']:
                    cell = blocks_map[child_id]
                    if cell['BlockType'] == 'CELL':
                        row_index = cell['RowIndex']
                        col_index = cell['ColumnIndex']
                        if row_index not in rows:
                            # create new row
                            rows[row_index] = {}
                            
                        # get the text value
                        rows[row_index][col_index] = self.get_text(cell, blocks_map)
        return rows


   

    def get_table_csv_results(self,filename,jsonname,height):

        
        with open(jsonname) as json_file:
            data=json.load(json_file)
                
        print("File Read "+jsonname)
        
        response=data
        
    
    

        csv_list=[]
        # Get the text blocks
        blocks=response['Blocks']
        #pprint(blocks)

        tables_dict={}
        blocks_map = {}
        table_blocks = []
        for block in blocks:
            blocks_map[block['Id']] = block
            if block['BlockType'] == "TABLE":
                table_blocks.append(block)
                top_key= int(block['Geometry']['BoundingBox']['Top']*(height/10)) 
                tables_dict[top_key]=block

        if len(table_blocks) <= 0:
            return None

        #Print Table Blocks
        
    
        
        index=0
        for key in sorted(tables_dict.keys()):
            csv = ''
            
            blocks= tables_dict[key]
            csv += self.generate_table_csv(tables_dict[key], blocks_map, index +1)
            top= blocks['Geometry']['BoundingBox']['Top']
            bottom=blocks['Geometry']['BoundingBox']['Top'] +  blocks['Geometry']['BoundingBox']['Height']
            Confidence=blocks["Confidence"]
            csv_list.append( (csv,top,bottom,Confidence) )
            #csv += '\n\n'
            index=index+1
        
        return csv_list
        


    def generate_table_csv(self,table_result, blocks_map, table_index):
        rows = self.get_rows_columns_map(table_result, blocks_map)

        table_id = 'Table_' + str(table_index)
        
        # get cells.
        # csv = 'Table: {0}\n\n'.format(table_id)
        csv=''
        for row_index, cols in rows.items():
            
            for col_index, text in cols.items():
                csv += '{}'.format(text) + ","
            csv += '\n'
            
        csv += '\n\n\n'
        return csv

    def get_text(self,result, blocks_map):
        text = ''
        if 'Relationships' in result:
            for relationship in result['Relationships']:
                if relationship['Type'] == 'CHILD':
                    for child_id in relationship['Ids']:
                        word = blocks_map[child_id]
                        if word['BlockType'] == 'WORD':
                            text += word['Text'] + ' '
                            
                        """     
                        if word['BlockType'] == 'SELECTION_ELEMENT':
                            if word['SelectionStatus'] =='SELECTED':
                                text +=  'X '
                        """
        return text.replace(',','')





    def parse_main(self,filename,jsonname,height,mapping_file):
        table_csv = self.get_table_csv_results(filename,jsonname,height)
        if table_csv is None :
            return None 
        
        cv_path=jsonname.replace('.json','-csv')
        
       
        if not os.path.isdir(cv_path):
            os.mkdir(cv_path)
        
        
        counter=1
        file_info_dict={}
        print("table_csv ")
        print(table_csv)
        for table,top,bottom,confidence in table_csv:
            filename_=cv_path+"/"+str(counter)+'.csv'
            
            with open(filename_, "wt") as fout:
                fout.write(table)
            counter=counter+1
            file_info_dict[filename_]=[top,bottom,confidence]    
            print('CSV OUTPUT FILE: ', filename_,"top ",top,"bottom",bottom)

        
        
        fout.close()
        #dump length info to file 
        with open( mapping_file, 'w') as fp:
            json.dump(file_info_dict, fp)
    
        return cv_path



def checkDuplicate(item,list_):
    
    for i in list_:
        if item in i:
            return True
    return False

def findLowestScore(items):
    least_score=0
    least_item=[]
    result=[]
    count=0
    
   
    
    for item in items:
        if count==0:
            least_score=item[3]
            least_item.append(item)
        elif item[3]<least_score:
            least_score=item[3]
            least_item.clear()
            least_item.append(item)
            
    return least_item

def intersect(List1, List2):
    # empty list for values that match
    ret = []
    for i in List2:
        for j in List1:
            if i in j:
                ret.append(j)
    return ret


def getcordinates(list1,invoice_arr_dict):
    
    for item in invoice_arr_dict:
        if list1==invoice_arr_dict[item]:
            return item


#method for creating matrix of all invoice text 
#Returns cordinates with row 

def createInvocieArray(json_path):

    text_cordinates=[] 
    with open(json_path) as json_file:
        data=json.load(json_file)
        
        for blocks in data['Blocks']:
            temp_str=''
                    
            a=blocks["Geometry"]["Polygon"][0]['X']
            b=blocks["Geometry"]["Polygon"][0]['Y']
               
            c=blocks["Geometry"]["Polygon"][1]['X']
            d=blocks["Geometry"]["Polygon"][1]['Y']
                #print(blocks["Geometry"]["Polygon"])
                # blocks['Geometry']['BoundingBox']['Top'] 
                # (int)(b*100),(int)(((a+c)/2)*100)
            if (blocks["BlockType"]=='LINE' )  :
                text_cordinates.append((blocks["Text"],math.ceil(b*100), ((a+c)/2)*100,a*100, (c-a)*100,b ))
    
    line_dict={}
    for item in text_cordinates:
        line_no=item[1]
        line_no_a=line_no-1
        if (line_no in line_dict)  :
            temp=line_dict[line_no]
            temp.append(item)
            line_dict[line_no]=temp
        else:
            temp=[]
            temp.append(item)
            line_dict[line_no]=temp
    
    
    all_arr=[]
    invoice_arr_dict={}
    rows=0
    for item in line_dict.keys():
        #print(item)
        cols=0
        temp=[]
        for i in line_dict[item]:
            #print(type(i[0]))
            #invoice_arr[rows][cols]=i[0]
            temp.append(i[0])
            cols=cols+1
            #print(item,i[0])
        rows=rows+1
        all_arr.append(temp)
        #print(line_dict[item][0][5])
        invoice_arr_dict[line_dict[item][0][5]]=temp
    return invoice_arr_dict,all_arr
        
        
    





#utilities used for Preprocessing and Extraction of Data 
class ExtractUtils:
    #Wifth and height of Image
    width=0
    height=0
    #Dimesion of Tables in Invoice
    top=0
    bottom=0
    #Footer Lists of Invoice
    footer_list=[]
    footer_blocks={}

    #Stopwords
    stopwords={'$','ourselves', 'hers', 'between', 'yourself', 'but', 'again', 'there', 'about', 'once', 'during', 'out', 'very', 'having', 'with', 'they', 'own', 'an', 'be', 'some', 'for', 'do', 'its', 'yours', 'such', 'into', 'of', 'most', 'itself', 'other', 'off', 'is', 's', 'am', 'or', 'who', 'as', 'from', 'him', 'each', 'the', 'themselves', 'until', 'below', 'are', 'we', 'these', 'your', 'his', 'through', 'don', 'nor', 'me', 'were', 'her', 'more', 'himself', 'this', 'down', 'should', 'our', 'their', 'while', 'above', 'both', 'up', 'to', 'ours', 'had', 'she', 'all','when', 'at', 'any', 'before', 'them', 'same', 'and', 'been', 'have', 'in', 'will', 'on', 'does', 'yourselves', 'then', 'that', 'because', 'what', 'over', 'why', 'so', 'can', 'did', 'not', 'now', 'under', 'he', 'you', 'herself', 'has', 'just', 'where', 'too', 'only', 'myself', 'which', 'those', 'i', 'after', 'few', 'whom', 't', 'being', 'if', 'theirs', 'my', 'against', 'a', 'by', 'doing', 'it', 'how', 'further', 'was', 'here', 'than'}




    #fetches Height if the Image
    def getHeight(self,image_path):

        im = Image.open(image_path)
        width, height = im.size
        result={}
        result["width"]=width
        result["height"]=height
        self.width=width
        self.height=height
        return result

    #Returns Above and Below of Tables 
    def getTableDimensions(self,json_path):
        
        counter=0
        top=0
        bottom=0
        with open(json_path) as json_file:
            data=json.load(json_file)
            table_blocks = []
            final_block=None
            for blocks in data['Blocks']:
                if blocks["BlockType"]=='TABLE':
                    print("table found ....")
                    #print(blocks)
                    #print(".......")

                
                    


                      
          

        
        print("top",top,"bottom",bottom)
        result={}
        result["top"]=top
        result["bottom"]=bottom
        self.top=top
        self.bottom=bottom
        return result


    def createFooterLists(self,json_path,height,bottom):
        
        footer_blocks={}

        with open(json_path) as json_file:
            data=json.load(json_file)
            for blocks in data['Blocks']:
                temp_str=''
                if blocks["BlockType"]=='LINE' and blocks['Geometry']['BoundingBox']['Top']>bottom:
                    #print(blocks)
                    top_key= int(blocks['Geometry']['BoundingBox']['Top']*(height/10)) 

                    if (top_key in footer_blocks):
                        temp_list=footer_blocks[top_key]
                    elif ((top_key-1) in footer_blocks):
                        temp_list=footer_blocks[top_key-1]
                    else:
                        temp_list=[]

                    temp_list.append(blocks)
                    footer_blocks[top_key]= temp_list
                

        footer_list=[]
        for key,value in footer_blocks.items():

            temp_str=""
            for item in value:
                temp_str=temp_str+' '+item['Text']

            footer_list.append(temp_str) 

        self.footer_list = list(dict.fromkeys(footer_list))
        
        return footer_list  
    
    #Remove Stop words from Invoices

    def clean_stop_words(self,header_list,nlp):
        clean_list=[]
        for item in header_list:
            doc = nlp(item.strip())
            temp_str=""
            for token in doc:
                if not token.text.lower() in self.stopwords:
                
                    temp_str=temp_str+" "+token.text
            clean_list.append(temp_str)
        
        return clean_list 
    
    #Creating Invoice Text for Extracting Invoice  Items 
    def createText(self,json_path,height,width,top,bottom):
        text_cordinates=[]
        matches=[]

        with open(json_path) as json_file:
            data=json.load(json_file)
            for blocks in data['Blocks']:
                temp_str=''
                #top_key= int(blocks['Geometry']['BoundingBox']['Top']*(height)/10) 
                #Left_key= int(blocks['Geometry']['BoundingBox']['Top']*(height)/10)
                    
                a=blocks["Geometry"]["Polygon"][0]['X']
                b=blocks["Geometry"]["Polygon"][0]['Y']

                c=blocks["Geometry"]["Polygon"][1]['X']
                d=blocks["Geometry"]["Polygon"][1]['Y']
                    
                if (blocks["BlockType"]=='LINE' and blocks['Geometry']['BoundingBox']['Top']<top)  :
                    text_cordinates.append((blocks["Text"],a,b,c,d))
                
        count=0  

        for item in text_cordinates:
            sub_list=text_cordinates[count+1:count+20]
            temp_matches=[]
            distance_counter=[]
            count2=0

            for item2 in sub_list:
        
                a=(item[1],item[2])
                b=(item2[1],item2[2])
                dst = sp_distance.euclidean(a, b)
                myradians = math.atan2(b[1]-a[1], b[0]-a[0])
                degrees = math.degrees(myradians)
                degrees=int(degrees)
                #print(degrees,' -- ',item[0],' -- ',item2[0],' -- ',dst)
                if degrees>-4 and degrees<4 and dst<0.39:
                    temp_matches.append( (item[0],item2[0],degrees,dst) )
        
                

            temp_matches=findLowestScore(temp_matches) 

            if len(temp_matches)==0:
                matches.append((item[0]))
            else:
                for i in temp_matches:
                    #print("item",i)
                    matches.append(i)
            count+=1
     
        header_list=[]
        count_=0
        for item in matches:
            #print(str(type))
            if str(type(item))=="<class 'str'>":
                if not checkDuplicate(item,matches[0:count_]):
                    header_list.append(item)
            elif str(type(item))=="<class 'tuple'>":
                header_list.append(item[0]+" "+item[1])
            count_=count_+1
        

        return header_list



    def createWholeText(self,json_path,height,width):
        text_cordinates=[]
        matches=[]

        with open(json_path) as json_file:
            data=json.load(json_file)
            for blocks in data['Blocks']:
                temp_str=''
                #top_key= int(blocks['Geometry']['BoundingBox']['Top']*(height)/10) 
                #Left_key= int(blocks['Geometry']['BoundingBox']['Top']*(height)/10)
                    
                a=blocks["Geometry"]["Polygon"][0]['X']
                b=blocks["Geometry"]["Polygon"][0]['Y']

                c=blocks["Geometry"]["Polygon"][1]['X']
                d=blocks["Geometry"]["Polygon"][1]['Y']
                    
                if (blocks["BlockType"]=='LINE')  :
                    text_cordinates.append((blocks["Text"],a,b,c,d))
                
        count=0  

        for item in text_cordinates:
            sub_list=text_cordinates[count+1:count+20]
            temp_matches=[]
            distance_counter=[]
            count2=0

            for item2 in sub_list:
        
                a=(item[1],item[2])
                b=(item2[1],item2[2])
                dst = sp_distance.euclidean(a, b)
                myradians = math.atan2(b[1]-a[1], b[0]-a[0])
                degrees = math.degrees(myradians)
                degrees=int(degrees)
                #print(degrees,' -- ',item[0],' -- ',item2[0],' -- ',dst)
                if degrees>-4 and degrees<4 and dst<0.39:
                    temp_matches.append( (item[0],item2[0],degrees,dst) )
        
                

            temp_matches=findLowestScore(temp_matches) 

            if len(temp_matches)==0:
                matches.append((item[0]))
            else:
                for i in temp_matches:
                    #print("item",i)
                    matches.append(i)
            count+=1
     
        header_list=[]
        count_=0
        for item in matches:
            #print(str(type))
            if str(type(item))=="<class 'str'>":
                if not checkDuplicate(item,matches[0:count_]):
                    header_list.append(item)
            elif str(type(item))=="<class 'tuple'>":
                header_list.append(item[0]+" "+item[1])
            count_=count_+1
        

        return header_list

    #Create Footer text by Distance 
    def createTextFooter(self,json_path,height,width,top,bottom):
        text_cordinates=[]
        matches=[]

        with open(json_path) as json_file:
            data=json.load(json_file)
            for blocks in data['Blocks']:
                temp_str=''
                #top_key= int(blocks['Geometry']['BoundingBox']['Top']*(height)/10) 
                #Left_key= int(blocks['Geometry']['BoundingBox']['Top']*(height)/10)
                    
                a=blocks["Geometry"]["Polygon"][0]['X']
                b=blocks["Geometry"]["Polygon"][0]['Y']
                c=blocks["Geometry"]["Polygon"][1]['X']
                d=blocks["Geometry"]["Polygon"][1]['Y']
                    
                if (blocks["BlockType"]=='LINE' and blocks['Geometry']['BoundingBox']['Top']>bottom):
                    text_cordinates.append((blocks["Text"],a,b,c,d))
                
        count=0  

        for item in text_cordinates:
            sub_list=text_cordinates[count+1:count+20]
            temp_matches=[]
            distance_counter=[]
            count2=0

            for item2 in sub_list:
        
                a=(item[3],item[4])
                b=(item2[1],item2[2])
                dst = sp_distance.euclidean(a, b)
                myradians = math.atan2(b[1]-a[1], b[0]-a[0])
                degrees = math.degrees(myradians)
                degrees=int(degrees)
                print("Footer ",degrees,' -- ',item[0],' -- ',item2[0],' -- ',dst)
                if degrees>-4 and degrees<=4 and dst<0.50:
                    temp_matches.append( (item[0],item2[0],degrees,dst) )
        
                

            temp_matches=findLowestScore(temp_matches) 

            if len(temp_matches)==0:
                matches.append((item[0]))
            else:
                for i in temp_matches:
                    #print("item",i)
                    matches.append(i)
            count+=1
     
        footer_list=[]
        count_=0
        for item in matches:
            #print(str(type))
            if str(type(item))=="<class 'str'>":
                if not checkDuplicate(item,matches[0:count_]):
                    footer_list.append(item)
            elif str(type(item))=="<class 'tuple'>":
                footer_list.append(item[0]+" "+item[1])
            count_=count_+1
        

        return footer_list  

    def findLineItemsHeaders(self,df_csv):

        remove_list=[]
        for index, row in df_csv.iterrows():
                match_count=0
                for k,v in row.iteritems():
                        
                        #print(k,v,index)
                        for item in header_items:
                            ratio=fuzz.ratio(str(item).lower().strip(),str(v).lower().strip())
                            #print(ratio,item,' -- ',v)
                            if ratio>75:
                                match_count=match_count+1
                                
                if match_count>=4:
                    remove_list.append(index)
        if len(remove_list)>0:
            df_csv.columns = df_csv.iloc[remove_list[0]]
            df_csv = df_csv[remove_list[0]+1:]
        

        
        return df_csv

    def cleanExtraRows(self,df_csv):

        all_nan=[]
        for index, row in df_csv.iterrows():
            nan_count=0
            allnan=True
            for k,v in row.iteritems():
                if not str(v)=="nan":
                        allnan=False
            if allnan:
                all_nan.append(index)

        for item in all_nan:
             df_csv=df_csv.drop(item,axis=0)
   

 


        remove_list=[]
        for index, row in df_csv.iterrows():
                nan_count=0
                for k,v in row.iteritems():
                        #print(k,v,index)
                        if str(v)=="nan":
                            nan_count+=1
                        if nan_count>3:
                            remove_list.append(index)
        #print(remove_list)
        #df_csv = df_csv[0:] 
        remove_list = list(dict.fromkeys(remove_list))
        for item in remove_list:
             df_csv=df_csv.drop(item,axis=0)
       
        return df_csv


    #get Confidence level 
    def get_confidence_matrix(self,json_path):
        confidence_dict={}
        
        with open(json_path) as json_file:
            data=json.load(json_file)
            table_blocks = []
            for blocks in data['Blocks']:
                if blocks["BlockType"]=='LINE':
                    #print(blocks["Confidence"],blocks["Text"])
                    confidence_dict[str(blocks["Text"]).strip().replace(',','') ]=blocks["Confidence"]
                    

        return confidence_dict


        


    #DataFrame to o/p compatible Dictionry :
    def df_to_dict(self,df_csv,confidence_dict):
        total_items=[]
        print("df to dict")
        print(df_csv)
        for k,v in df_csv.to_dict().items():
            #print(k,' -- ',v)
            line_items=[]
            for key,value in v.items():
                print(k,' -- ',value )
                line_items.append({k:value})
            print("line item conf",line_items)
            total_items.append(line_items)

 
        
        final_dict=[]
        #for item in total_items:
        match_item=None
        for x in range(0,len(total_items[0])):
            temp_dict=[]
            for y in range(0,len(total_items)):
                
                for k1,v1 in total_items[y][x].items():
                    conf=""
                    #print(str(v1).strip() in confidence_dict.keys(),str(v1) )
                    match_item=self.findBestMatch(str(v1).strip(),confidence_dict.keys())
                    if len(str(v1).strip())>0:
                        print(str(v1).strip(),' -- ',match_item)
                        conf=confidence_dict[match_item] 
                    
                    
                    """
                    if str(v1).strip() in confidence_dict.keys():
                        conf=confidence_dict[str(v1).strip() ]
                    else:
                        conf="-"
                    """    
                        
                    temp_dict.append( {str(k1):str(v1),"confidence_level":conf}  )
            final_dict.append( {"fields":temp_dict} )
        
        return final_dict


    def findBestMatch(self,str_,match_list):

        max_score=0
        match_item=None

        for item in match_list:
            match=fuzz.partial_ratio(item,str_)
            if match > max_score:
                max_score=match
                match_item=item
        
        return match_item







        #Find Vendor Name by Position 
    def getVendorNameByPosition(self,header_list,company_list):
            
        vendor_name=None
        for item in header_list:
            for company in company_list:
                ratio=fuzz.token_set_ratio(item.lower(),company.lower())
                print(ratio,item.lower(),company.lower())
                if ratio>75:
                    vendor_name=item
                    break
            if not vendor_name==None:
                break
        return vendor_name

    #Detects where Line Items End in Table 
    def LineItemEnding_row(self,invoice_arr,count):
        total_count=[]
        total_row_count=0
        for row in invoice_arr:
            
            for col in row:

                for i1 in tax_items:
                    
                    ratio=fuzz.partial_ratio(str(col).lower().strip(),i1.lower().strip())
                    print("tax item: ",i1,"  col:  ",col, " ratio:",ratio)
                    if ratio>90 and total_row_count>count:
                        return total_row_count

                for i1 in total_items:
                    ratio=fuzz.partial_ratio(str(col).lower().strip(),i1)
                    if ratio>90 and total_row_count>count:
                        return total_row_count
            total_row_count=total_row_count+1
        return total_row_count


    def findLineItemTable_raw (self,csv_path):

        #Find Number of Files in the Directory 
        import os
        import pandas as pd
        path, dirs, files = next(os.walk(csv_path))
        file_count = len(files)
        print("file _count",file_count)
        print("csv_file",csv_path)
        invoice_table=""
        df_ = pd.DataFrame()
        if file_count==1:
            df=pd.read_csv(csv_path+'/'+files[0])
            
            break_outer=False
            for i in range(len(df)) :
                for item in df.iloc[i]:

                    for i1 in tax_items:
                        ratio=fuzz.partial_ratio(str(item).lower().strip(),i1.lower().strip())
                        if ratio>90:
                            break_outer=True

                    for i1 in total_items:
                        ratio=fuzz.partial_ratio(str(item).lower().strip(),i1.lower().strip())
                        if ratio>90:
                            break_outer=True   
                    

                if break_outer==True:
                    break
                df_=df_.append(df.iloc[i])
            
            return df_
        return None

           
            
        
        



    def findLineItemTable (self,csv_path):

        #Find Number of Files in the Directory 
        import os
        import pandas as pd
        path, dirs, files = next(os.walk(csv_path))
        file_count = len(files)
        print("file _count",file_count)
        print("csv_file",csv_path)
        invoice_table=""
        if file_count>1:
            header_found=False
            for f in files:

                df_csv=pd.read_csv(csv_path+'/'+f, delimiter = ',',header=None)

                for index, row in df_csv.iterrows():
                    match_count=0
                    for k,v in row.iteritems():
                            
                            print("header ",k,v,index)
                            for item in header_items:
                                ratio=fuzz.partial_ratio(str(item).lower().strip(),str(v).lower().strip())
                                print(ratio,item,' -- ',v)
                                if ratio>75:
                                    match_count=match_count+1
                    print("---------------------")  
                    print("match_count",match_count)                     
                    if match_count>=3:
                        header_found=True
                        invoice_table=f
                        break
        else:
            invoice_table=files[0]
        
        return invoice_table


   
    def findLineItemTable_V2 (self,csv_path):


        #Find Number of Files in the Directory 
        
        path, dirs, files = next(os.walk(csv_path))
        file_count = len(files)
        print("file _count",file_count)
        print("csv_file",csv_path)

        invoice_table=""
        scores_dict={}

        for f in files:
            scores=[]
            df_csv=pd.read_csv(csv_path+'/'+f, delimiter = ',',header=None)

            for index, row in df_csv.iterrows():
                match_count=0
                
                #Matching for Header Items 
                for k,v in row.iteritems():
                    for item in header_items:
                        ratio=fuzz.partial_ratio(str(item).lower().strip(),str(v).lower().strip())
                        ratio2=distance.get_jaro_distance(str(item).lower().strip(),str(v).lower().strip(), winkler=True, scaling=0.1)

                        if ratio>75:
                            match_count=match_count+1
                            #print(ratio,item,' -- ',v,ratio2)
                
                scores.append(match_count)

            scores_dict[f]=max(scores)

        return_file=None
        highiest=0
        for keys in scores_dict:
            if highiest<scores_dict[keys]:
                highiest=scores_dict[keys]
                return_file=keys

        return return_file
    
    #Finds Line Item Using Table Cordinates
    # Performs Type Checking of All Line Items 
    def findLineItems_V3(self,csv_path,json_path):


        invoice_arr = list(csv.reader(open(csv_path)))
        count=0
        for row in invoice_arr:
            match_count=0
            
            for col in row:
                
                
                for item in header_items:
                    ratio=fuzz.partial_ratio(str(item).lower().strip(),str(col).lower().strip())
                    #ratio2=distance.get_jaro_distance(str(item).lower().strip(),str(v).lower().strip(), winkler=True, scaling=0.1)
                    if ratio>75:
                        match_count=match_count+1
                        #print(ratio,item,' -- ',v,ratio2)
             
            print("Match Count",match_count)
            db_logger.info("Headers Match Count "+str(match_count),{"user":"" ,"entity":""} )
            db_logger.info("Headers Match Threshold "+str(3),{"user":"" ,"entity":""})

            if match_count>=2:
                print("found Line Item Header")
                print(invoice_arr[count], "Header Number " ,count )
                break
            count=count+1 
        #Find Lien Items Till Subtotal or total :

        #Find the Line Item Ending Part
        line_item_end_count= self.LineItemEnding_row(invoice_arr,count) 
        
        print("Line Item End Count ",line_item_end_count,"count :",count)

        #invoice_arr[total_row_count-1]    
        #invoice_arr=invoice_arr.remove( invoice_arr[total_row_count-1] )

        

        #removing empty rows from data 
        a=[]
        for i in invoice_arr:
            if len([x for x in i if x])>0:
                a.append(i)
        
        invoice_arr=a

        print("invoice_arr ")
        print(invoice_arr)

        invoice_arr_li=None
        invoice_arr_whole=invoice_arr

        if line_item_end_count is not None:
            #invoice_arr=invoice_arr[count:line_item_end_count+1]
            invoice_arr=invoice_arr[count:line_item_end_count]

        
        
        print("Initial Line Item *************** ")
        print(invoice_arr)
        print("Initial Line Item *************** ")

        print("line item count ",count ,len(invoice_arr))
        try:
            header=invoice_arr_whole[count]
        except:
            return pd.DataFrame(),0,0
        

        print("headers")
        print(header)

        header_checks={}
        c=0
        for i in header:
            for j in header_items_type :

                ratio=fuzz.partial_ratio(str(i).lower().strip(),str(j).lower().strip())
                #print(ratio,i,j,header_items_type[j])
                if(ratio>80):
                    print(ratio,i,j,header_items_type[j])
                    header_checks[c]=[i,header_items_type[j]]
            
            
               
                
                        
            c=c+1
        
        
    
        print("******* header checks **************")
        print(header_checks)
        print("******* header checks **************")

       
        """
        if c==count:
                c=c+1
                continue
            else:
        """

        #Performing Type Checking 
        c=0
        f_line_items=[]
        for row in invoice_arr:

            print("row checking" , row)
        
            
            cc=0
            score_check=[]
            for col in row:
                if cc in header_checks:
                    col_type=header_checks[cc][1]
                    print("Matching| type",col_type," col:",col)
                    for t in col_type:
                        matcher = Matcher(nlp.vocab, validate=True)
                            

                        if t=="num":
                            matcher.add("t", None, num)
                                
                            
                        if t=="string":
                            matcher.add("t", None, string)
                        
                        if not col.strip()=="":
                            doc = nlp(col.lower())
                            matches = matcher(doc)
                            if(len(matches)>0):
                                print("match found ", c,col,col_type)
                                score_check.append(1)
                            
                                
                            
                                
                            
                        
                
                        
                cc=cc+1
            print("length of scorecheck ",len(score_check))
            if(len(score_check)>=2):
                f_line_items.append(c)
                    
            c=c+1
        print("******************LINE ITEMS ***************************")
        search_line=[]
        lineitems=[]
        db_logger.info("Line Items Detected :",{"user":"" ,"entity":""} )
        for item in f_line_items:
            print(invoice_arr[item])
            db_logger.info( "item : "+ str(invoice_arr[item]).replace('%','') ,{"user":"" ,"entity":""} )
            lineitems.append( invoice_arr[item] )
            search_line= invoice_arr[item]

        print("******************LINE ITEMS ***************************")

        #convert invoice into matrix , along with cordinates 
        invoice_arr_dict,all_arr=createInvocieArray(json_path)
        
        #search_line=f_line_items[len(f_line_items)-1]
        print("search line ",search_line)
        bottom=0
        top=0
        for item in all_arr:
            res=intersect(search_line,item)
            if len(res)>2:
                print("bottom",res,getcordinates(item,invoice_arr_dict))
                bottom= getcordinates(item,invoice_arr_dict)
        print("count ",count)
        header_row=invoice_arr_whole[count]
        for item in all_arr:
            res=intersect(header_row,item)
            if len(res)>2:
                print("top",res,getcordinates(item,invoice_arr_dict))
                top= getcordinates(item,invoice_arr_dict)
        
        print("top",top)
        print("bottom",bottom)
        
        df = pd.DataFrame(lineitems, columns = header_row ) 

        print("dataframe ",df)
     





        return df,top,bottom
        
            






    #######################Finding Line Items by patterns ######################################
    #Finds Line Item Without Table Coridnates 
    #Hunts for tables, and Decides Line Items 
    def findLineItems_V2 (self,json_path):


        text_cordinates=[] 
        with open(json_path) as json_file:
            data=json.load(json_file)
            for blocks in data['Blocks']:

                temp_str=''
                a=blocks["Geometry"]["Polygon"][0]['X']
                b=blocks["Geometry"]["Polygon"][0]['Y']
               
                c=blocks["Geometry"]["Polygon"][1]['X']
                d=blocks["Geometry"]["Polygon"][1]['Y']

                if (blocks["BlockType"]=='LINE' )  :
                    text_cordinates.append((blocks["Text"],math.ceil(b*100), ((a+c)/2)*100,a*100, (c-a)*100,b ))
         
         #find all horizontal lines :
        line_dict={}
        for item in text_cordinates:
            line_no=item[1]
            line_no_a=line_no-1
            if (line_no in line_dict)  :
                temp=line_dict[line_no]
                temp.append(item)
                line_dict[line_no]=temp
            else:
                temp=[]
                temp.append(item)
                line_dict[line_no]=temp

        invoice_arr=[]
        invoice_arr_dict={}
        rows=0
        for item in line_dict.keys():
            cols=0
            temp=[]
            for i in line_dict[item]:
                temp.append(i[0])
                cols=cols+1
            rows=rows+1
            invoice_arr.append(temp)
            invoice_arr_dict[line_dict[item][0][5]]=temp
         
        #print("invoice_arr") 
        #print(invoice_arr) 
        #Identifying the Header Items 
        count=0
        for row in invoice_arr:
            match_count=0
            
            for col in row:
                
                
                for item in header_items:
                    ratio=fuzz.partial_ratio(str(item).lower().strip(),str(col).lower().strip())
                    #print("ratio >>>>",ratio,item,col)
                    #ratio2=distance.get_jaro_distance(str(item).lower().strip(),str(v).lower().strip(), winkler=True, scaling=0.1)
                    if ratio>75:
                        match_count=match_count+1
                        #print(ratio,item,' -- ',v,ratio2)
            print(header_items)
            print("match count",match_count)
            if match_count>=3:
                print("found Line Item")
                print(invoice_arr[count],count )
                break
            count=count+1

            #Finding the row of total or subtotal :
        total_count=[]
        total_row_count=0
        for row in invoice_arr:
            for col in row:
                if(len(col)>=len("total")):
                    ratio=fuzz.partial_ratio(str(col).lower().strip(),"total")
                    
                if ratio>90 and total_row_count>count:
                    total_count.append( (row,total_row_count,ratio) )
                    break
            total_row_count=total_row_count+1

        #Finalizing the Line Items into an array  :
        print("total count rows")
        print(total_count) 
        no_cols=len(invoice_arr[count])
        #print("no of columns ",no_cols)
        line_items_s1=[]
        c=0

        for row in invoice_arr:
            #and  len(row) in range(no_cols-3,no_cols+2)
            if c>=count and c<total_count[0][1] :
                line_items_s1.append(row)
            c=c+1
            
        print(" ***********************LINE ITEMS********************* ")
        print(line_items_s1)
        print(" ***********************LINE ITEMS********************* ")
      
        top=0
        bottom=0
        last_line_item=line_items_s1[len(line_items_s1)-1]

        print("last line item ",last_line_item)
        print("header line item",invoice_arr[count])
        for item in invoice_arr_dict:
            if invoice_arr_dict[item]==last_line_item:
                bottom=item
                #print("bottom",invoice_arr_dict[item],item)
            if invoice_arr_dict[item]==invoice_arr[count]:
                top=item
                #print("top ",invoice_arr_dict[item],item)

        print("top",top)
        print("bottom",bottom)

        #validating Line Items With DataTypes
        #Identifying the Header Data Type Items 
        header=invoice_arr[count]
        header_checks={}
        c=0
        for i in header:
            
            for j in header_items_type:
                ratio=fuzz.partial_ratio(str(i).lower().strip(),str(j).lower().strip())
                if(ratio>80):
                    print(ratio,i,j,header_items_type[j])
                    header_checks[c]=[i,header_items_type[j]]
            c=c+1

        #validating Line Items 

        c=0
        f_line_items=[]
        for row in line_items_s1:
        
            if c==0:
                c=c+1
                continue
            else:
                cc=0
                score_check=[]
                for col in row:
                    
                    if cc in header_checks:
                        col_type=header_checks[cc][1]
                        print(col,col_type)
                        
                        for t in col_type:
                            matcher = Matcher(nlp.vocab)
                            

                            if t=="num":
                                matcher.add("t", None, num)
                                
                            
                            if t=="string":
                                matcher.add("t", None, string)
                        
                            if not col.strip()=="":
                                doc = nlp(col)
                                matches = matcher(doc)
                                print(col,matches,col_type)
                                if(len(matches)>0):
                                    print(c,col,col_type,)
                                    score_check.append(1)
                            
                                
                            
                                
                            
                        
                
                        
                    cc=cc+1
            print("score check ",score_check)
            if(len(score_check)>2):
                f_line_items.append(c)
                    
            c=c+1

        print("*******************FINAL LINE ITEMS *************************")

        df = pd.DataFrame(line_items_s1, columns = invoice_arr[count]) 
        print("dataframe ",df)
            
        print("*******************FINAL LINE ITEMS *************************")
    



        return df,top,bottom



           
            
            


   

                    



                      

                   


            
            
    


        




                    
            



        
        
        
                    













        
            




