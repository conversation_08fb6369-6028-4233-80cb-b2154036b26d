from Invoice.DbConfig import *
from fuzzywuzzy import fuzz
import boto3
import trp.trp2 as t2


def makecall(image_path,question,field_name):
    field_list={}
    print("in make call",image_path)
    print("question ",question)
    # print("field_name ",field_name)
    with open(image_path, 'rb') as document:
        imageBytes = bytearray(document.read())
        print("in textract..")                    
        textract = boto3.client('textract',aws_access_key_id="********************",
                        aws_secret_access_key="6629LxPd+So34S4EJPxsxs0KOvCwt/AwrjZkAHwN")
        # Call Textract AnalyzeDocument by passing a document from local disk
        response2 = textract.analyze_document(
            Document={'Bytes': imageBytes},
            FeatureTypes=["QUERIES"],
            QueriesConfig={
                "Queries": [{
                    "Text": question,
                    "Alias": field_name
                }]
            })

        d = t2.TDocumentSchema().load(response2)
        page = d.pages[0]

        # get_query_answers returns a list of [query, alias, answer]
        query_answers = d.get_query_answers(page=page) 
        if len(query_answers)>0:
            if len(query_answers[0][2].strip())>0:
                field_list[field_name]=query_answers[0][2]
                print(question," ",query_answers[0][2])
    
    return field_list

def matchjson_and_condition(json_result2,condition_json):
    

    json_result={}
    temp_json=json_result2[:]
    for i in temp_json:
        #del i["confidence_level"]
        keys=list(i.keys())
        for id,ii in enumerate(keys):
            if ii=="confidence_level":
                del keys[id]
        #del keys["confidence_level"]
        keys=keys[0]
        json_result[keys]=i[keys]
    
    json_result =  {k.lower().strip(): v for k, v in json_result.items()}
    
    result={}
    for index, key in enumerate(condition_json):
        if index==0:
            result["c1"]=False
            if key in json_result:
                if type(condition_json[key]) == list:
                    for condition_val in condition_json[key]:
                        if fuzz.ratio(json_result[key],condition_val)>93:
                            result["c1"]=True
                            break
                else:
                    if fuzz.ratio(json_result[key],condition_json[key])>93:
                        result["c1"]=True
                        break
    
        elif index==2:
            result["c1"]=False
            if key in json_result:
                if type(condition_json[key]) == list:
                    for condition_val in condition_json[key]:
                        if fuzz.ratio(json_result[key],condition_val)>93:
                            result["c1"]=True
                            break
                else:
                    if fuzz.ratio(json_result[key],condition_json[key])>93:
                        result["c1"]=True
                        break

    # print("result",result)
    

    if "c2" in result:
            print("in c2")
            operator_result=eval( result["c1"]+" "+condition_json["operator"]+" "+result["c2"] )
            return operator_result
                
    else:
        if "c1" in result:
            return result["c1"]
             
    

    #return False


def getQuery(config_name,json_result2,image_list,prediction_result):
    
    field_results={}
    res_list=getquery(config_name) 
    json_result={}
    temp_json=json_result2[:]
    for i in temp_json:
        #del i["confidence_level"]
        keys=list(i.keys())
        for id,ii in enumerate(keys):
            if ii=="confidence_level":
                del keys[id]
        #del keys["confidence_level"]
        keys=keys[0]
        json_result[keys]=i[keys]
    
    
        
    

    json_result =  {k.lower().strip(): v for k, v in json_result.items()}
    
    # print("res_list ",res_list)
    # print("json ",json_result)
    #for item in res_list:
    
    result={}
    for item in res_list:
        q_fname=item["fieldname"]
        q_question=item["query"]
        q_json= item["json"]
        
        for index, key in enumerate(q_json):
            if index==0:
                if key in json_result:
                    if fuzz.ratio(json_result[key],q_json[key])>93:
                        result["c1"]=True
                    else:
                        result["c1"]=False

            elif index==2:
                if key in json_result:
                    if fuzz.ratio(json_result[key],q_json[key])>93:
                        result["c2"]=True
                    else:
                        result["c2"]=False

        # print("result",result)
        operator_result=None
        if "operator" in res_list:

            if "c2" in result:
                print("in c2")
                operator_result=eval( result["c1"]+" "+res_list["operator"]+" "+result["c2"] )
                # print("operator result ",operator_result)
                if operator_result:
                    for k,image in image_list.items():
                        if k in prediction_result:
                            if not prediction_result[k]["NI"]>=0.80  :
                                query_r=makecall(image,item["query"],item["fieldname"])
                                # print("query result ",query_r)
                                if item["fieldname"] in query_r:
                                    field_results[item["fieldname"]]=query_r[item["fieldname"]]
                                    #break
                        elif k==len(image_list)-1:
                            query_r=makecall(image,item["query"],item["fieldname"])
                            # print("query result ",query_r)
                            if item["fieldname"] in query_r:
                                field_results[item["fieldname"]]=query_r[item["fieldname"]]

        else:
            if "c1" in result and result["c1"]==True:
                print("in c1")
                # print("image_list")
                # print(image_list)
                for k,image in image_list.items():
                    if k in prediction_result: 
                        if not prediction_result[k]["NI"]>=0.80:
                            query_r=makecall(image,item["query"],item["fieldname"])
                            # print("query result ",query_r)
                            if item["fieldname"] in query_r:
                                field_results[item["fieldname"]]=query_r[item["fieldname"]]
                    elif k==len(image_list)-1:
                            query_r=makecall(image,item["query"],item["fieldname"])
                            # print("query result ",query_r)
                            if item["fieldname"] in query_r:
                                field_results[item["fieldname"]]=query_r[item["fieldname"]]
    
    return field_results