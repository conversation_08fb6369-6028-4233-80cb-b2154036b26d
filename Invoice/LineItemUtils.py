from fuzzywuzzy import fuzz

def getLineItemHeaderName(json_txt,header):

    final_header=[]

    for item in header:

        
        scores=[]
        for i in json_txt:
            fields=i["field_names"]
            name=i["name"]

            for f in fields:
                ratio=fuzz.ratio(f.lower().strip(),item.lower().strip())
                scores.append((f,ratio,name)) 
        
        scores.sort(key=lambda x:x[1],reverse=True)
        if scores[0][1]>=80:
            final_header.append(scores[0][2])
        else:
            final_header.append(item)
    
    return final_header



