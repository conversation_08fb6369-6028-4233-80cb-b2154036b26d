from Invoice.models import *
from fuzzywuzzy import fuzz
from fuzzywuzzy import process
from pyjarowinkler import distance

#fetch the extraction list from configuration name 



class FieldMatch:

    word_list=[]
    def  cerate_word_list(self,config_list):
        w_list=[]
        for config in config_list:

            head=config["head"]
            tail=config["tail"]
            name=config["name"]
            head_a=[]
            tail_a=[]

            if len(head.strip())>0:
                head_a=head.split(",")
            
            if len(tail.strip())>0:
                tail_a=tail.split(",")
            
            for h in head_a:
                for t in tail_a:
                    w_list.append((h.strip()+" "+t.strip(),name) )
        
        return w_list

    def get_field_label(self,field_name,w_list,match_threshold):


        match_ratio_word=None
        highiest=0
        item=field_name
        f_name=None

        for list_item in w_list:
            word=list_item[0]
            name=list_item[1]
            word=word.replace(":","")
            item=item.replace(":","")
            
            if word is None or name is None:
                continue 
            
            try:
                match_ratio = fuzz.ratio(item.lower().strip(), word.lower().strip())
                # jaro_distance= distance.get_jaro_distance(item.lower().strip(), word.lower().strip())
            except:
                continue
            #print(" Key Matching ",item[0].lower().strip(), word.lower().strip(),match_ratio)
            #or (jaro_distance*100)>80)
            #if (match_ratio > 95  and match_ratio>highiest):
            if (match_ratio>match_threshold  and match_ratio>highiest):
                #print(" Key Matching ",item ,' : ',item[0].lower().strip(), word.lower().strip(),match_ratio)

                highiest=match_ratio
                match_ratio_word=item
                f_name=name
        
        #print("Field_match",highiest,f_name,match_ratio_word)
        return (highiest,f_name,match_ratio_word)









        


        


        

   

   



    

    

    



    
