# Generated by Django 3.1.1 on 2021-09-13 13:09

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('Invoice', '0016_lineitemexclusion'),
    ]

    operations = [
        migrations.AddField(
            model_name='configuration',
            name='ai_fieldmatch_threshold',
            field=models.IntegerField(default=95),
        ),
        migrations.AddField(
            model_name='lineitemheaders',
            name='display_name',
            field=models.Char<PERSON>ield(db_index=True, default=django.utils.timezone.now, max_length=100),
            preserve_default=False,
        )
    ]
