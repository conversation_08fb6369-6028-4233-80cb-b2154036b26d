{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["csv_file_path=\"/home/<USER>/invoice/invoice-flow/checkout1/apautomation/Invoice/pdf/2327b538-d3f8-45b4-a212-587a61bb5a67invoice-csv/2.csv\"\n", "json_path=\"/home/<USER>/invoice/invoice-flow/checkout1/apautomation/Invoice/pdf/2327b538-d3f8-45b4-a212-587a61bb5a67invoice.json\"\n", "\n", "#from Invoice.ExtractUtils import * \n", "#extract_util_obj = ExtractUtils()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os \n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from Invoice.FieldSearch import *\n", "fieldsearch=FieldSearch()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mapping_data = \"\"\n", "table_dimensions=None\n", "unique_filename=\"2327b538-d3f8-45b4-a212-587a61bb5a67\"\n", "mapping_file=BASE_DIR+'/Invoice/pdf/'+unique_filename+'mapping.json'\n", "#csv_file_path=BASE_DIR +'/Invoice/pdf/'+unique_filename+'invoice-csv/'+csv_file_path\n", "with open(mapping_file) as f:\n", "    mapping_data = json.load(f)\n", "    #print(mapping_data)\n", "    #mapping_data[csv_file_path]\n", "    table_dimensions = mapping_data[csv_file_path]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["invoice_no={}\n", "invoice_no[\"name\"]=\"Invoice_No\"\n", "invoice_no[\"type\"]=\"Alpha\"\n", "invoice_no[\"strategy\"]=\"fuzz\"\n", "invoice_no[\"head\"]=[\"billing invoice\",\"billing\",\"invoice\"]\n", "invoice_no[\"tail\"]=[\"no\",\"no.\",\"id\",\"#\",\"number\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["field_value,field_conf= fieldsearch.search_field(json_path,invoice_no,table_dimensions[0],table_dimensions[1] )\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# get the results\n", "import json\n", "import boto3\n", "client = boto3.client(\n", "                    service_name='textract',\n", "                    region_name='us-east-1',\n", "                    endpoint_url='https://textract.us-east-1.amazonaws.com',\n", "            )\n", "feature_field=['TABLES']\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def reader( file):\n", "        with open(file, \"rb\") as image_file:\n", "            img_test = image_file.read()\n", "            bytes_test = bytearray(img_test)\n", "        return bytes_test\n", "    \n", "data_str =reader(\"/home/<USER>/invoice/extract1/1.png\")\n", "response = client.analyze_document(\n", "                    Document={'Bytes': data_str}, FeatureTypes=feature_field)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import tensorflow as tf\n", "from object_detection.utils import ops as utils_ops\n", "from object_detection.utils import label_map_util\n", "\n", "output_directory = 'inference_graph'"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_99594) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_99594) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_99594) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_99594) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_99594) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_99594) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_99594) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_99594) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_99594) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_99594) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_99594) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_99594) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_99594) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_99594) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_99594) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_99594) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_99594) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_99594) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_99594) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_99594) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_99594) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_99594) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_99594) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_99594) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64301) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64301) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64301) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64301) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64301) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64301) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64301) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64301) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64301) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64301) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64301) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64301) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64301) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64301) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64301) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64301) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64301) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64301) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64301) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64301) with ops with custom gradients. Will likely fail if a gradient is requested.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64301) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64301) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64301) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64301) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95986) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95986) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95986) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95986) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95986) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95986) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95986) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95986) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95986) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95986) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95986) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95986) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95986) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95986) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95986) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95986) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95986) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95986) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95986) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95986) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95986) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95986) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95986) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95986) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_89981) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_89981) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_89981) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_89981) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_89981) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_89981) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_89981) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_89981) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_89981) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_89981) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_89981) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_89981) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_89981) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_89981) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_89981) with ops with custom gradients. Will likely fail if a gradient is requested.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_89981) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_89981) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_89981) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_89981) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_89981) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_89981) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_89981) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_89981) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_89981) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_62681) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_62681) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_62681) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_62681) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_62681) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_62681) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_62681) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_62681) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_62681) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_62681) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_62681) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_62681) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_62681) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_62681) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_62681) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_62681) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_62681) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_62681) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_62681) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_62681) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_62681) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_62681) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_62681) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_62681) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_80626) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_80626) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_80626) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_80626) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_80626) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_80626) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_80626) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_80626) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_80626) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_80626) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_80626) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_80626) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_80626) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_80626) with ops with custom gradients. Will likely fail if a gradient is requested.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["WARNING:tensorflow:Importing a function (__inference_call_func_80626) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_80626) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_80626) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_80626) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_80626) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_80626) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_80626) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_80626) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_80626) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_80626) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_80626) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_80626) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_80626) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_80626) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_80626) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_80626) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_80626) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_80626) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_80626) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_80626) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_80626) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_80626) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_80626) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_80626) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_80626) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_80626) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_80626) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_80626) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_80626) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_80626) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_80626) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_80626) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_80626) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_80626) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_80626) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_80626) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_80626) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_80626) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_80626) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_80626) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86373) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86373) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86373) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86373) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86373) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86373) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86373) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86373) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86373) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86373) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86373) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86373) with ops with custom gradients. Will likely fail if a gradient is requested.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86373) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86373) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86373) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86373) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86373) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86373) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86373) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86373) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86373) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86373) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86373) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86373) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_18366) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_18366) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_18366) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_18366) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_18366) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_18366) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_18366) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_18366) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_18366) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_18366) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_18366) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_18366) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_18366) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_18366) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_18366) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_18366) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_18366) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_18366) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_18366) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_18366) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_18366) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_18366) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_18366) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_18366) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_18366) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_18366) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_18366) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_18366) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_18366) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_18366) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_18366) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_18366) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_18366) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_18366) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_18366) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_18366) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_18366) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_18366) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_18366) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_18366) with ops with custom gradients. Will likely fail if a gradient is requested.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["WARNING:tensorflow:Importing a function (__inference_call_func_18366) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_18366) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_18366) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_18366) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_18366) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_18366) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_18366) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_18366) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_18366) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_18366) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_18366) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_18366) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_18366) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_18366) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "TENSORFLOW Object Detection API Model Loaded ......\n"]}], "source": ["import os\n", "os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3' \n", "TF_OB_model = tf.saved_model.load(f'./{output_directory}/saved_model')\n", "print(\"TENSORFLOW Object Detection API Model Loaded ......\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["image=\"/home/<USER>/invoice/invoice-flow/inv-flow-dvc/apautomation/Invoice/pdf/1bfc20c0-89a6-451e-adb9-e987fe38f9ca_0.png\"\n", "json_path=\"/home/<USER>/invoice/invoice-flow/inv-flow-dvc/apautomation/Invoice/pdf/1bfc20c0-89a6-451e-adb9-e987fe38f9ca_0invoice.json\"\n", "mapping_json=json_path.replace(\"invoice.json\",\"mapping.json\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["spacy model laoded \n", "TENSORFLOW INVOICE DETECTION MODEL LOADED......\n"]}, {"ename": "FileNotFoundError", "evalue": "[Errno 2] No such file or directory: '/home/<USER>/invoice/invoice-flow/db_invocieflow/apautomation/Invoice/pdf/0719591a-e678-45e3-a4e1-788d6b5d5c1d_0.png'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mFileNotFoundError\u001b[0m                         <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<timed exec>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n", "\u001b[0;32m/home/<USER>/invoice/invoice-flow/inv-flow-dvc/apautomation/Invoice/Vendor_Extraction.py\u001b[0m in \u001b[0;36mgetVendors\u001b[0;34m(model, image, json_path)\u001b[0m\n\u001b[1;32m    232\u001b[0m     \u001b[0mvendor_list\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m\"td williamson\"\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\"welldyne\"\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    233\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 234\u001b[0;31m     \u001b[0mim\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mImage\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mopen\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mimage\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    235\u001b[0m     \u001b[0mim_width\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mim_height\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mim\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0msize\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    236\u001b[0m     \u001b[0mprint\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m\"image dim \"\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mim_height\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mim_width\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/anaconda3/lib/python3.7/site-packages/PIL/Image.py\u001b[0m in \u001b[0;36mopen\u001b[0;34m(fp, mode)\u001b[0m\n\u001b[1;32m   2841\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   2842\u001b[0m     \u001b[0;32mif\u001b[0m \u001b[0mfilename\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 2843\u001b[0;31m         \u001b[0mfp\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mbuiltins\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mopen\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mfilename\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m\"rb\"\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   2844\u001b[0m         \u001b[0mexclusive_fp\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;32mTrue\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   2845\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mFileNotFoundError\u001b[0m: [Errno 2] No such file or directory: '/home/<USER>/invoice/invoice-flow/db_invocieflow/apautomation/Invoice/pdf/0719591a-e678-45e3-a4e1-788d6b5d5c1d_0.png'"]}], "source": ["%%time\n", "from Invoice.Vendor_Extraction import *\n", "res=getVendors(TF_OB_model,image,json_path)\n", "res"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["#Read Mapping json \n", "with open(json_path) as f:\n", "     data = json.load(f)\n", "                   "]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["centralfoodequipment\n"]}], "source": ["import re\n", "import lexnlp.extract.en.urls\n", "\n", "for d in data[\"Blocks\"]:\n", "    if d[\"BlockType\"]==\"LINE\":\n", "        #print(d[\"Text\"])\n", "        text= d[\"Text\"]\n", "        emails = re.findall(r\"[a-z0-9\\.\\-+_]+@[a-z0-9\\.\\-+_]+\\.[a-z]+\", text)\n", "        url=list(lexnlp.extract.en.urls.get_urls(text))\n", "        #print(url )\n", "        if len(url)>0:\n", "            print(url[0].split(\".\")[1])\n", "            \n", "            \n", "            \n", "            \n", "                \n", "                    \n", "              \n", "                "]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["centralfoodequipment\n"]}], "source": ["from Invoice.Vendor_Extraction import *\n", "print(getVendorByMail(json_path))"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"text/plain": ["'gmail'"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["emails = re.findall(r\"[a-z0-9\\.\\-+_]+@[a-z0-9\\.\\-+_]+\\.[a-z]+\",\"<EMAIL>\")\n", "emails[0].split(\"@\")[1].split(\".\")[0]"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Adobe Inc. Adobe 100\n", "Beneficiary: Adobe Inc. Adobe 100\n"]}], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.4"}}, "nbformat": 4, "nbformat_minor": 2}