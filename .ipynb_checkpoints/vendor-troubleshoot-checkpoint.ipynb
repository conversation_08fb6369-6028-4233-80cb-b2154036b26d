{"cells": [{"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["inv_page_path=\"/home/<USER>/invoice/invoice-flow/inv-flow-dvc/db-repo/aavenir.invoiceflow.ai/Invoice/pdf/4f8d1f03-ba79-44d4-9d3e-b88f0b466dca_0.png\"\n", "json_path=\"/home/<USER>/invoice/invoice-flow/inv-flow-dvc/db-repo/aavenir.invoiceflow.ai/Invoice/pdf/4f8d1f03-ba79-44d4-9d3e-b88f0b466dca_0invoice.json\"\n", "image=\"/home/<USER>/invoice/invoice-flow/inv-flow-dvc/db-repo/aavenir.invoiceflow.ai/Invoice/pdf/4f8d1f03-ba79-44d4-9d3e-b88f0b466dca_0.png\"\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import os \n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\""]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": [" form_fields = []"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "== FOUND KEY : VALUE pairs ===\n", "\n", " temp_dict found  {'HST: ': ['30.44 ', 97.5], 'Subtotal: ': ['234.19 ', 97.0], 'Total Due: ': ['264.63 ', 95.0], 'Customer ': ['Number:********* ', 94.5], 'Invoice ': ['Date:03/23/2022 ', 93.0], 'Service: ': ['Weekly - WE ', 89.5], 'B.N.#: ': ['10076 7490 RT0001 ', 85.5], 'Bill To: ': ['T.D. WILLIAMSON <PERSON> 8300 LAWSON ROAD UNIT #101 MILTON, ON L9T 0A4 ', 79.5], 'Account ': ['', 66.5], 'Route:12 ': ['PETER MACISAAOMAR 2 5 2022 ', 57.0], 'Deliver To: ': ['T.D. WILLIAMSON 8300 LAWSON RD #1 MILTON, ON L9T 0A4 ', 45.0], 'Email: ': ['<EMAIL> ', 39.5], 'Please Remit Payment to : ': ['Canadian Linen and Uniform Service Corp., Box 51073, RPO Tyndall, Winnipeg, MB, R2X 3C6 ', 32.0], 'Fax: ': ['(************* ', 30.5], 'Ph: ': ['(416)849-5100 ', 26.0], 'Page 1 of 2 ': ['', 11.5]}\n"]}], "source": ["from Invoice.Forms_Extraction import *\n", "AI_Fields={}\n", "temp_dict=get_raw_values(json_path)\n", "print(\" temp_dict found \",temp_dict)\n", "AI_Fields.update(temp_dict)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["import django\n", "django.setup()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["NER 2 models laoded.....  \n", "TENSORFLOW INVOICE DETECTION MODEL LOADED......\n", "base diretory /home/<USER>/invoice/invoice-flow/inv-flow-dvc/db-repo/aavenir.invoiceflow.ai\n", "object detection API  <tensorflow.python.keras.engine.functional.Functional object at 0x7fa1f8aae350>\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_66189) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_95658) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_82437) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_86045) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_bifpn_layer_call_and_return_conditional_losses_64569) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_EfficientDet-D0_layer_call_and_return_conditional_losses_92050) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "WARNING:tensorflow:Importing a function (__inference_call_func_20938) with ops with custom gradients. Will likely fail if a gradient is requested.\n", "TENSORFLOW Object Detection API Model Loaded ......\n"]}], "source": ["from Invoice.LineSearch import *\n", "from AMS.extract_settings import *\n", "from Invoice.ExtractUtils import *\n", "from Invoice.FieldMatch import * "]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1, 2, 4, 5, 6, 8, 17, 18, 19, 20, 21, 31]\n", "regex_id  [6]\n", "regex_id  [6]\n", "regex_id  [7]\n", "regex_id  [8]\n", "match threshold  95\n"]}], "source": ["config_name=\"global_config\"\n", "field_match=FieldMatch()\n", "db_extrtaction_list=get_extraction_list(config_name,None)\n", "w_list=field_match.cerate_word_list(db_extrtaction_list)\n", "\n", "match_threshold=get_ai_fieldmatch_threshold(config_name)\n", "print(\"match threshold \",match_threshold)\n", "\n", "raw_dict=AI_Fields\n", "for key, value in raw_dict.items():\n", "        #print(key,' -- ',value)\n", "        #checking duplicate for extracted_field\n", "        #print(\"Match Field  checking \",key)\n", "\n", "        check_res=field_match.get_field_label(key,w_list,match_threshold) \n", "\n", "        key=key.replace(\":\",\"\").replace(\",\",\"\")\n", "        search_key=None\n", "        if check_res[1]==None:\n", "            data={key.strip():value[0].strip(),\"confidence_level\":value[1]}\n", "            search_key=key.strip()\n", "        else:\n", "            data={check_res[1].strip():value[0].strip(),\"confidence_level\":value[1]}\n", "            search_key=check_res[1].strip()\n", "\n", "        #form_fields.append(data)\n", "\n", "        #check for any duplicate present in the list \n", "\n", "        for d in form_fields:\n", "            l = [item.lower().strip() for item in list(d.keys())]\n", "            if search_key.lower().strip() in l:\n", "                print(\"Duplicate found \",d , \" :for \",check_res[1],\" , Match_score :\")\n", "                form_fields.remove(d)\n", "\n", "\n", "                #form_fields.append(d)\n", "                \"\"\"\n", "                temp_val=d[check_res[1]]\n", "                d[check_res[1]+\"_duplicate\"]=temp_val\n", "                t=d\n", "                del d[check_res[1]]\n", "                form_fields.append(t)\n", "                \"\"\"\n", "\n", "\n", "        form_fields.append(data)\n", "        #temp_data[key]=value"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["from Invoice.Vendor_Extraction import *\n", "from Invoice.DbConfig import *"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["length of vendorfields  0\n", "Key Value Vendor name  None\n", "companylist ......  ['mufg Investor Services', 'td williamson', 'welldyne', 'armanagement', 'bit', 'EDMONTON AB T6N 1N4', 'e-hps', 'K8B', 'MUFG CAPITAL ANALYTICS', 'na', '2249 S. 2700 W.']\n", "vendor list ...  ['mufg Investor Services', 'td williamson', 'welldyne', 'armanagement', 'bit', 'EDMONTON AB T6N 1N4', 'e-hps', 'K8B', 'MUFG CAPITAL ANALYTICS', 'na', '2249 S. 2700 W.']\n", "image dim  2589 2000\n", "0 0.57025874\n", " index  0  conf :  0.57025874\n", "final box [[46.74861580133438, 720.775306224823, 82.96061634644866, 345.96191677451134]]\n", "box value extracted  ([46.74861580133438, 720.775306224823, 82.96061634644866, 345.96191677451134], 0.57025874)\n", "get text from box  82.96061634644866 -3.251384198665619 345.96191677451134 820.775306224823\n", "ex text  Canadian\n", "ex text  LINEN & UNIFORM SERVICE\n", "text from box  ['Canadian', 'LINEN & UNIFORM SERVICE']\n", "boxtext\n", "['Canadian', 'LINEN & UNIFORM SERVICE']\n", "vendor  []\n", "vendor  ['LINEN & UNIFORM SERVICE']\n", "ven ext spacy  ['LINEN & UNIFORM SERVICE']\n", "get vendr by match  LINEN & UNIFORM SERVICE\n"]}], "source": ["kv_vendorname,kv_score=filter_vendor_fields(form_fields,config_name) \n", "print(\"Key Value Vendor name \",kv_vendorname)\n", "if kv_vendorname==None:\n", "    companylist=get_companynames(config_name)\n", "    ven_res=getVendors(TF_OB_model,image,json_path,companylist,config_name)\n", "    #vendor_match=getVendorbycsvMatch(header_list)\n", "    data={\"vendor_name\":ven_res[0],\"confidence_level\":ven_res[1] }\n", "    form_fields.append(data)\n", "else:\n", "    data={\"vendor_name\":kv_vendorname,\"confidence_level\":kv_score }\n", "    form_fields.append(data)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/plain": ["'global_config'"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["config_name"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/plain": ["[]"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["get_VendorFields(config_name)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Django Shell-Plus", "language": "python", "name": "django_extensions"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.4"}}, "nbformat": 4, "nbformat_minor": 2}