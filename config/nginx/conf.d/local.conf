# first we declare our upstream server, which is our Gunicorn application
upstream ams_server {
    # docker will automatically resolve this to the correct address
    # because we use the same name as the service: "djangoapp"
    server invoice:9100;
}
# now we declare our main server
server {

    listen 80;
    client_max_body_size 20M;
    server_name localhost;

    location / {
        # everything is passed to Gunicorn
        proxy_read_timeout 650s;
        proxy_connect_timeout 75s;
        proxy_pass http://ams_server;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $host;
        proxy_redirect off;
    }
    
}
