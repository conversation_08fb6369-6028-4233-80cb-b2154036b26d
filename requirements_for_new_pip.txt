spacy==2.1.6
lexnlp #0.2.7
#blis==0.4.1	#commenting because spacy uses different version of blis
boto3==1.22.0
botocore==1.25.0
catalogue==1.0.0
cymem==2.0.3
#dateparser==0.7.4 #commenting because lexnlp uses different version of dateparser
Django==2.2.5
djangorestframework-simplejwt==4.4.0
django-extensions==3.0.9
docopt==0.6.2
fuzzywuzzy==0.17.0
jellyfish==0.6.1
jmespath==0.9.5
murmurhash==1.0.2
#num2words==0.5.10 #commenting because lexnlp installs same version anyways
#plac==1.1.3 #commenting because spacy uses different version of plac
#preshed==3.0.2  #commenting because spacy uses different version of preshed
price-parser==0.3.3
#pycountry==19.8.18 #commenting because lexnlp uses different version of pycountry
pyjarowinkler==1.8
PyJWT==1.7.1
#regex==2020.4.4	#commenting because lexnlp uses different version of regex
#reporters-db==2.0.0	#commenting because lexnlp uses different version of reporters-db
s3transfer==0.3.3
sqlparse==0.3.1	
srsly==1.0.2
#thinc==7.4.0 # commenting because spacy uses different version of thinc 
tzlocal==2.0.0
#Unidecode==1.1.1 #commenting because lexnlp installs same version anyways
#us==2.0.2 #commenting because lexnlp installs same version anyways
wasabi==0.6.0
gunicorn==20.0.4
#pandas==1.0.3 #commenting because spacy uses different version of pandas
Pillow==7.1.2
#scipy==1.4.1 #commenting because spacy uses different version of scipy
#nltk==3.5.0 #commenting because lexnlp installs same version anyways
#scikit-learn==0.21.2  #commenting because lexnlp uses different version of scikit-learn
PyMuPDF==1.16.18
pdfplumber==0.5.20
datefinder
jsonschema==3.2.0
pdf2image==1.13.1
django-db-logger==0.1.9
opencv-python==********
#psutil==5.7.2 #commenting because spacy uses different version of pandas
gevent==22.10.2
Cython==0.29.21
jsonfield==3.1.0
django-modelclone==0.7.1

