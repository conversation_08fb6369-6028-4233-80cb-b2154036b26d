from django.db import models
from Invoice.models import Configuration
from jsonfield import <PERSON><PERSON><PERSON><PERSON>
# Create your models here.
class feedback(models.Model):
    name = models.CharField(max_length=100,db_index=True,unique=True)
    field_json= JSONField(default={},blank=True, null=True)
    description= models.CharField(max_length=500,db_index=True)
    extracted_json=JSONField(default={},blank=True, null=True)
    invoice_url= models.CharField(max_length=500,unique=True)
    configuration_id=models.ForeignKey(Configuration, default=None,db_index=True,on_delete=models.CASCADE)
    
    


