from Invoice.models import *

#fetch the extraction list from configuration name 



def save_feedback_db(config_name,name,field_json,description,extracted_json,invoice_url,instance_name):
    
    #fetch instance id 
    res=Configuration.objects.filter(name=config_name)
    snow_url=None
    config_id=None
    if res.exists():
        config=res.first()
        snow_url=config.snow_url
        config_id=config.id
    
    

    #save instance id 
    f = feedback(name = name, description = description, field_json = field_json,extracted_json=extracted_json,invoice_url=invoice_url,configuration_id=config_id)
    f.save()
    
    return True 


def get_extraction_list(config_name,algo_type):
    
    res=Configuration.objects.filter(name=config_name)
    extract_field_id=None
    extract_kvalue=[]
    if res.exists():
        config=res.first()
        extract_field_id=config.extract_field
    
    
    if extract_field_id is not None:
        mapping_id_q=ExtractFieldMapping.objects.filter(field=extract_field_id) 
        kvalue_id=[]

        for r in mapping_id_q:
            kvalue_id.append(r.value.id)

        print(kvalue_id)

        if algo_type is not None:
            field_mapping_q=  kvalue.objects.filter(id__in=kvalue_id,f_algo_type=algo_type )
        else:
            field_mapping_q=  kvalue.objects.filter(id__in=kvalue_id )


        
        

        if field_mapping_q.exists():

            for extract_field in field_mapping_q:
                #extract_field=field_mapping_q.first()
                extract_f_dict={}
                extract_f_dict["name"]=extract_field.f_name
                extract_f_dict["type"]=extract_field.f_type
                extract_f_dict["head"]=extract_field.f_head
                extract_f_dict["tail"]=extract_field.f_tail
                extract_f_dict["strategy"]=extract_field.f_strategy
                extract_f_dict["rule"]=extract_field.f_rule
                
                if extract_field.f_regex_field is not None:

                    
                    RegexGroupValues= RegexGroupValue.objects.filter(field=extract_field.f_regex_field.id )


                    regex_id=[]

                    for r in RegexGroupValues:
                        regex_id.append(r.value.id)
                    print("regex_id ",regex_id) 
                    regex=  Regex.objects.filter(id__in=regex_id )
                    extract_f_dict["regex"]=list(regex.values())
                else:
                    extract_f_dict["regex"]=None


                
                
                extract_kvalue.append(extract_f_dict)


            
            
    return extract_kvalue

#fetches Line Item Headers 
def get_header_fields(config_name):

    res=Configuration.objects.filter(name=config_name)
    extract_field_id=None
    extract_kvalue=[]
    if res.exists():
        config=res.first()
        extract_field_id=config.extract_field

    if extract_field_id is not None:
        header_res=LineItemHeaders.objects.filter(field=extract_field_id,active=True) 
        #kvalue_id=[]
        
        header_list=[]
        if header_res.exists():
            
            for header in header_res:
                temp_dict={}
                temp_dict["name"]=header.name
                l=header.field_names.split(",")
                l=[item.strip() for item in l]  
                temp_dict["field_names"]=l
                header_list.append(temp_dict)

            

    return header_list

    

        






    

    



    
