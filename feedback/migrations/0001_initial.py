# Generated by Django 3.1.1 on 2021-01-11 13:08

from django.db import migrations, models
import django.db.models.deletion
import jsonfield.fields


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('Invoice', '0010_auto_20201224_0248'),
    ]

    operations = [
        migrations.CreateModel(
            name='feedback',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(db_index=True, max_length=100, unique=True)),
                ('field_json', jsonfield.fields.J<PERSON><PERSON>ield(blank=True, default={}, null=True)),
                ('description', models.Char<PERSON>ield(db_index=True, max_length=500)),
                ('extracted_json', jsonfield.fields.JSONField(blank=True, default={}, null=True)),
                ('invoice_url', models.Char<PERSON>ield(max_length=500, unique=True)),
                ('customer_id', models.ForeignKey(default=None, on_delete=django.db.models.deletion.CASCADE, to='Invoice.configuration')),
            ],
        ),
    ]
