from django.shortcuts import render

# Create your views here.


class FeedBackRest(APIView):

    def post(self, request):

        json_body = json.loads(request.body.decode("utf-8"))

        description = json_body["description"]
        field_json = json_body["field_json"]
        extracted_json = json_body["extracted_json"]
        name= json_body["name"]
        base64= json_body["invoice_base64"]
        invoice_type = json_body["type"]
        
        
        
       
        print("********")

