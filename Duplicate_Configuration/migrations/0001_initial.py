# Generated by Django 3.1.1 on 2022-06-07 06:58

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('Invoice', '0021_query'),
    ]

    operations = [
        migrations.CreateModel(
            name='DuplicateConfiguration',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('duplicate_to', models.CharField(max_length=100)),
                ('kvalue_prefix', models.CharField(max_length=100)),
                ('is_successful', models.BooleanField(default=False)),
                ('message', models.TextField()),
                ('duplicate_from_config_name', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Invoice.configuration')),
                ('duplicate_from_extractField', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Invoice.extractfield')),
            ],
        ),
    ]
