from importlib.resources import path
from django.contrib import admin
from django.http import HttpResponseRedirect
from .models import DuplicateConfiguration as DupConfig
from django.conf.urls import url
from Invoice.models import *

import json
# Register your models here.
#admin.site.register(DuplicateConfiguration)
UPDATE_DATE = "06/06/2022"

@admin.register(DupConfig)
class DuplicateConfiguration(admin.ModelAdmin):
    #...
    #change_form_template = "entities/villain_changeform.html"
    change_form_template = 'admin/Duplicate_Configuration/change_form.html'
    list_display = ("duplicate_from_config_name", "duplicate_to","is_successful")
    list_filter = ("is_successful",)

    def response_change(self, request, obj):
        if "duplicate_configuration" in request.POST:
            print("here")
            return HttpResponseRedirect(".")
        return super().response_change(request, obj)


    def get_urls(self):
        urls = super().get_urls()
        my_urls = [
            url('duplicate_configuration/', self.duplicateConfiguration)
        ]
        return my_urls + urls

    
    def duplicateConfiguration(self, request):
        result = {"API Update Date":UPDATE_DATE}
        duplicate_from_config = request.POST.dict()['duplicate_from_config_name']
        duplicate_to = request.POST.dict()['duplicate_to']
        duplicate_from_extractField = request.POST.dict()['duplicate_from_extractField']
        kvalue_prefix = request.POST.dict()['kvalue_prefix']
        
      
        from_config_obj=Configuration.objects.filter(id=duplicate_from_config )
        from_config_obj=from_config_obj.first()
        if(from_config_obj != None ):
            print(from_config_obj.name)
            
            try:
                ## STEP 1 CREATE EXTRACT FIELDS 
                result['step1_extractField'] = {"status":"failed"}
                extract_field_name =duplicate_to.split("_config")[0]+"_extractfields"
                extractField = ExtractField(name=extract_field_name, description="Auto-generated mappings from : "+from_config_obj.name)
                extractField.save()
                result['step1_extractField'] = {"status":"ok","name":from_config_obj.name}
                #result = {"name":from_config_obj.name}

                ## STEP 2 CREATE CONFIGURATION
                result['step2_configuration'] = {"status":"failed"}

                configuration =Configuration(name=duplicate_to,description="Auto-generated mappings from : "+from_config_obj.name,company_names=from_config_obj.company_names,extract_field=extractField,snow_url=from_config_obj.snow_url,ai_fieldmatch_threshold=from_config_obj.ai_fieldmatch_threshold)#,max_pagescan=from_config_obj.max_pagescan
                configuration.save()
                result['step2_configuration'] = {"status":"ok","name":duplicate_to}

                ## STEP 3 DUPLICATE KVALUES BY LOOKING AT EXTRACT FIELD MAPPINGS
                result['step3_kvalues'] = {"status":"failed"}
                from_extractField = ExtractField.objects.filter(id=duplicate_from_extractField)
                from_extractField = from_extractField.first()
                extractFieldMappings = ExtractFieldMapping.objects.filter(field=from_extractField)
                extractFieldMappings = extractFieldMappings.all()
                #print(len(extractFieldMappings))
                #print(extractFieldMappings)
                if(kvalue_prefix != ""):
                    for efMapping in extractFieldMappings:
                        value = efMapping.value
                        if(len(value.display_name.split("_")) > 1):
                            display_name = kvalue_prefix+"_"+"_".join(value.display_name.split("_")[1:])
                        else:
                            display_name = kvalue_prefix+"_"+value.display_name
                        kv=kvalue(f_name = value.f_name, display_name = display_name, f_type=value.f_type, f_head=value.f_head, f_tail=value.f_tail, f_strategy=value.f_strategy, f_rule=value.f_rule, f_regex_field=value.f_regex_field, f_algo_type=value.f_algo_type)#,masking =value.masking, match_threshhold=value.match_threshhold 
                        kv.save()
                        efm = ExtractFieldMapping(field=extractField,value=kv,active=efMapping.active)
                        efm.save()
                                

                else:
                        result.step3_kvalues['message'] = "kvalue_prefix is required parameter and cannot be blank"
                    
                result['step3_kvalues'] = {"status":"ok","no_of_fields_added":len(extractFieldMappings)}

                ## STEP 4 DUPLICATE LINE ITEM HEADERS
                result['step4_LineItemHeaders'] = {"status":"failed"}
                liheaders = LineItemHeaders.objects.filter(field=from_extractField)
                for liheader in liheaders:
                    value = liheader.name
                    if(len(value.split("_")) > 1):
                        value = kvalue_prefix+"_"+"_".join(value.split("_")[1:])
                    else:
                        value = kvalue_prefix+"_"+value

                    displayValue = liheader.display_name
                    if(len(displayValue.split("_")) > 1):
                        displayValue = kvalue_prefix+"_"+"_".join(displayValue.split("_")[1:])
                    else:
                        displayValue = kvalue_prefix+"_"+displayValue
                    li = LineItemHeaders(display_name=displayValue,name=value,description=liheader.description,field_names=liheader.field_names,active=liheader.active,field=extractField)
                    li.save()
                result['step4_LineItemHeaders'] = {"status":"ok","no_of_fields_added":len(liheaders)}

                ## STEP 5 DUPLICATE vendor fields
                result['step5_VendorFields'] = {"status":"failed"}
                vendorfields = VendorFields.objects.filter(extractfield=from_extractField)
                for vendorfield in vendorfields:
                    vf = VendorFields(name=vendorfield.name,fieldnames=vendorfield.fieldnames,extractfield=extractField)
                    vf.save()
                result['step5_VendorFields'] = {"status":"ok","no_of_fields_added":len(vendorfields)}


                ## STEP 6 DUPLICATE Address entitys
                result['step6_AddressEntities'] = {"status":"failed"}
                addressEntities = AddressEntity.objects.filter(extractfield=from_extractField)
                for addressEntity in addressEntities:
                    ae = AddressEntity(name=addressEntity.name,fieldtype=addressEntity.fieldtype,longstring = addressEntity.longstring, entity = addressEntity.entity,extractfield=extractField)
                    ae.save()
                result['step6_AddressEntities'] = {"status":"ok","no_of_fields_added":len(addressEntities)}

                ## STEP 7 DUPLICATE General Entity
                result['step7_GeneralEntity'] = {"status":"failed"}
                generalEntities = GeneralEntity.objects.filter(extractfield=from_extractField)
                for generalEntity in generalEntities:
                    ge = GeneralEntity(name=generalEntity.name,fieldtype=generalEntity.fieldtype,fullstring = generalEntity.fullstring, entity = generalEntity.entity,extractfield=extractField)
                    ge.save()
                result['step7_GeneralEntity'] = {"status":"ok","no_of_fields_added":len(generalEntities)}

                ## STEP 8 DUPLICATE Line Item Exclusion
                result['step8_LineItemExclusion'] = {"status":"failed"}
                liExclusions = LineItemExclusion.objects.filter(field=from_extractField)
                for liExclusion in liExclusions:
                    value = liExclusion.name
                    if(len(value.split("_")) > 1):
                        value = kvalue_prefix+"_"+"_".join(value.split("_")[1:])
                    else:
                        value = kvalue_prefix+"_"+value

                    lie = LineItemExclusion(name=value,type=liExclusion.type,field_list = liExclusion.field_list, active = liExclusion.active,field=extractField)
                    lie.save()
                result['step8_LineItemExclusion'] = {"status":"ok","no_of_fields_added":len(liExclusions)}
                ## STEP 9 DUPLICATE query
                result['step9_query'] = {"status":"failed"}
                queryList = Query.objects.filter(extractfield=from_extractField)

                for qry in queryList:
                    value = qry.displayname
                    if(len(value.split("_")) > 1):
                        value = kvalue_prefix+"_"+"_".join(value.split("_")[1:])
                    else:
                        value = kvalue_prefix+"_"+value

                    lie = Query(displayname=value, fieldname=qry.fieldname, query = qry.query, description = qry.description, vendorfieldnames=qry.vendorfieldnames, missingfieldnames=qry.missingfieldnames,extractfield=extractField)
                    lie.save()

                result['step9_query'] = {"status":"ok","no_of_fields_added":len(queryList)}
                dc = DupConfig(duplicate_from_config_name=from_config_obj,duplicate_from_extractField=from_extractField,duplicate_to=duplicate_to,kvalue_prefix=kvalue_prefix,is_successful=True,message=result)
                dc.save()
            except Exception as e:
                print(e)
                result['Error'] = str(e)
                dc = DupConfig(duplicate_from_config_name=from_config_obj,duplicate_from_extractField=from_extractField,duplicate_to=duplicate_to,kvalue_prefix=kvalue_prefix,is_successful=False,message=result)
                dc.save()
            
        self.message_user(request, "Duplication of configuration completed! Please check the record to see more details.")
        return HttpResponseRedirect("../")
