from django.db import models
from Invoice.models import Configuration,ExtractField
# Create your models here.
class DuplicateConfiguration(models.Model):
    duplicate_from_config_name = models.ForeignKey(Configuration,on_delete=models.CASCADE)
    duplicate_from_extractField = models.ForeignKey(ExtractField,on_delete=models.CASCADE)
    duplicate_to = models.CharField(max_length=100)
    kvalue_prefix = models.CharField(max_length=100)
    is_successful = models.BooleanField(default=False)
    message = models.TextField()
    def __str__(self):
        return self.duplicate_from_config_name.name+":"+self.duplicate_to