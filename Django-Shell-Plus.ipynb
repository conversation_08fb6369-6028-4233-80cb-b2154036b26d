{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["csv_file_path=\"/home/<USER>/invoice/invoice-flow/db_invoiceflow2/apautomation/Invoice/pdf/246a6d24-e056-4495-af20-e26690ede5f5_0invoice-csv/1.csv\"\n", "json_path=\"/home/<USER>/invoice/invoice-flow/db_invoiceflow2/apautomation/Invoice/pdf/1649b349-abe4-4641-a0c3-f9bbefb99703_0invoice.json\"\n", "\n", "from Invoice.ExtractUtils import * \n", "extract_util_obj = ExtractUtils()"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import os \n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#df,top,bottom=extract_util_obj.findLineItems_V3( csv_file_path, json_path)\n", "df,top,bottom=extract_util_obj.findLineItems_V2(json_path)\n", "df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_arr=[['Line Item ', 'Ordered ', 'Cancelled ', 'Shipped ', '', 'Item Number Description ', 'Unit Price USD $ ', 'Amount USD $ ', ''], ['1 ', '1 ', 'O ', '1 ', 'PART: 2579-S3-E100D40-ND MFG : enDAQ / S3-E100D40 C0O : UNITED STATES ROHS3 COMP ', 'DESC: S3 VIBRATION SENSOR ECCN: EAR99 HTSUS: 9031.80.8085 ', '400.00000 ', '2400.00 T ', ''], [], [], []]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#[inv_arr[i] for i in range(0,1)]  \n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["confidence_dict=extract_util_obj.get_confidence_matrix(json_path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["line_items_dict=extract_util_obj.df_to_dict(df,confidence_dict)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["line_items_dict"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "with open(json_path) as json_file:\n", "    data=json.load(json_file)\n", "    table_blocks = []\n", "    #print(data[\"Blocks\"] )\n", "    for blocks in data['Blocks']:\n", "        #print(blocks[\"BlockType\"])\n", "        if blocks[\"BlockType\"]=='PAGE':\n", "            print( blocks )\n", "    #        #print(blocks[\"Confidence\"],blocks[\"Text\"])\n", "            \n", "            \n", "            \n", "                "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mapping_data = \"\"\n", "table_dimensions=None\n", "unique_filename=\"2327b538-d3f8-45b4-a212-587a61bb5a67\"\n", "mapping_file=BASE_DIR+'/Invoice/pdf/'+unique_filename+'mapping.json'\n", "#csv_file_path=BASE_DIR +'/Invoice/pdf/'+unique_filename+'invoice-csv/'+csv_file_path\n", "with open(mapping_file) as f:\n", "    mapping_data = json.load(f)\n", "    #print(mapping_data)\n", "    #mapping_data[csv_file_path]\n", "    table_dimensions = mapping_data[csv_file_path]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["base_path = BASE_DIR+'/Invoice/pdf/'\n", "invoice_path = base_path+unique_filename+\".png\"\n", "extract_util_obj.getHeight(invoice_path)\n", "            # extract_util_obj.getTableDimensions(json_path)\n", "header_list = extract_util_obj.createText(json_path, extract_util_obj.height, extract_util_obj.width, table_dimensions[0], table_dimensions[1])\n", "footer_list = extract_util_obj.createTextFooter(json_path, extract_util_obj.height, extract_util_obj.width, table_dimensions[0], table_dimensions[1])\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#header_list\n", "from Invoice.Extract import *\n", "extract_obj = Extract()\n", "company_pattern = extract_obj.extract_company_pattern(header_list+footer_list)\n", "companies=[]\n", "for i in company_pattern:\n", "    companies.append(str(i[0]) +\" \"+ str(i[1]) )\n", "                "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from Invoice.Vendor_Extraction import *"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dst_list=get_distance_list(json_path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#dst_list\n", "companies=addCompanyFromSpacy(companies,nlp,header_list+footer_list)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#companies\n", "prob_company=company_and_distance_list_intersect(companies,dst_list)\n", "final_company= getVendorByPosition(prob_company)\n", "len(final_company)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#check which of the vendors are near \n", "vendor_identifier=[\"service provider\",\"branch office\",\"\"]\n", "vendor_rules={\"one\":[{'LOWER': 'for'},{'OP': '?'},{'ENT_TYPE': 'ORG','OP':'*'}]  }"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["match_identifier=getVendorIdentifiers(dst_list,vendor_identifier)\n", "match_identifier"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_comp=getVendorsNearIdentifiers(prob_company,match_identifier)\n", "final_comp"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#Rule matcher For \n", "import spacy \n", "matcher = Matcher(nlp.vocab, validate=True)\n", "rule1=[{'LOWER': 'for'},{'OP': '?'},{'ENT_TYPE': 'ORG','OP':'*'}]       \n", "matcher.add(\"rule1\", None, rule1 )\n", "\n", "doc = nlp(\"For: NISA INDUSTRIAL SERVICES PVT. LTD.\")\n", "matches = matcher(doc)\n", "\n", "for match_id, start, end in matches:\n", "    print(doc[start:end])\n", "    #match_result.append( doc[start:end] )\n", "                \n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["png_path=\"/home/<USER>/invoice/invoice-flow/db_invocieflow/apautomation/Invoice/pdf/9b616523-661d-4320-8cc3-645d72985be1_0.png\"\n", "json_path=\"/home/<USER>/invoice/invoice-flow/db_invocieflow/apautomation/Invoice/pdf/9b616523-661d-4320-8cc3-645d72985be1_0invoice.json\"       \n", "            \n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from Invoice.Vendor_Extraction import *\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["vendor_list=[\"tdwilliamson\"]\n", "getVendorByMail(json_path,vendor_list)      "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["json_path=\"/home/<USER>/invoice/invoice-flow/db_invoiceflow2/apautomation/Invoice/pdf/fa6710af-1856-4e05-ad17-4e3b397caccc_0invoice.json\"\n", "img_path=\"/home/<USER>/invoice/invoice-flow/db_invoiceflow2/apautomation/Invoice/pdf/fa6710af-1856-4e05-ad17-4e3b397caccc_0.png\"\n", "mapping_path=\"/home/<USER>/invoice/invoice-flow/db_invoiceflow2/apautomation/Invoice/pdf/fa6710af-1856-4e05-ad17-4e3b397caccc_0mapping.json\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from PIL import Image\n", "from Invoice.ExtractUtils import * \n", "\n", "im = Image.open(img_path)\n", "width, height = im.size\n", "tb=TableUtils()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#csv=tb.parse_main(img_path,json_path,height,mapping_path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from tensorflow.keras.models import load_model\n", "from tensorflow.keras.preprocessing import image\n", "from Invoice.ML.TFPredictions import *\n", "#Machine Learning Model Loading \n", "#model_path=\"/home/<USER>/invoice/invoice-flow/db_invoiceflow2/apautomation/Invoice/ML/model.h5\"\n", "model_path=\"/home/<USER>/invoice/invoice-flow/db_invoiceflow2/apautomation/model.h5\"\n", "TFmodel2 = load_model(model_path)\n", "print(\"TENSORFLOW MODEL LOADED......\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#find line items v2 \n", "image_path=\"/home/<USER>/invoice/invoice-flow/db_invoiceflow2/apautomation/Invoice/pdf/ec593561-9f61-4f34-aa89-8340fd43b343_6.png\"\n", "I_res=predictInvoice(TFmodel2,image_path)\n", "I_res\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<h1>Field Search Debugging</h1>"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "os.environ.setdefault(\"DJANGO_SETTINGS_MODULE\", \"AMS.settings\")\n", "\n", "import django\n", "django.setup()\n", "\n", "from django.core.management import call_command"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import os \n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\""]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["#cintasjson_path=\"/home/<USER>/invoice/invoice-flow/inv-flow-dvc/db-repo/aavenir.invoiceflow.ai/Invoice/pdf/d258d74b-b2ef-4df0-9cb0-3296d1d53f97_0invoice.json\"\n", "json_path=\"/home/<USER>/invoice/invoice-flow/inv-flow-dvc/db-repo/aavenir.invoiceflow.ai/Invoice/pdf/366ada57-f06d-436b-8fab-f76e69c0fc44_0invoice.json\"\n", "#from Invoice.ExtractUtils import * \n", "#extract_util_obj = ExtractUtils()\n", "#form_fields = []\n", "from Invoice.FieldSearch import *\n", "#form_fields=getFormFieldsbyDistance(config_name,form_fields,json_path,table_dimensions)\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import os \n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\""]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["extract field id  global_extract_fields\n", "[3, 4, 5, 6, 7, 8, 17, 18, 19, 20, 21, 34, 34, 61, 63] algo type  Horizontal  hascondition_  True\n", "field mapping q\n", "<QuerySet [<kvalue: tdw sou invoice no>, <kvalue: welldyne  verizon global invoice no>]>\n"]}, {"data": {"text/plain": ["[{'name': 'invoice_no',\n", "  'type': 'Rule',\n", "  'head': 'invoice',\n", "  'tail': ',',\n", "  'strategy': 'fuzz',\n", "  'rule': {'extraction_rule': [{'TEXT': {'REGEX': '[a-zA-Z0-9]'},\n", "     'LENGTH': 9}],\n", "   'one': [{'LOWER': 'invoice'},\n", "    {'OP': '*'},\n", "    {'TEXT': {'REGEX': '[a-zA-Z0-9]'}, 'LENGTH': 9}]},\n", "  'conditionfield': {'ship_to': 'TDW WILLIAMSON CANADA ULC EDMONTON, ALBERTA'},\n", "  'hascondition': True,\n", "  'regex': None},\n", " {'name': 'invoice_no',\n", "  'type': 'Rule',\n", "  'head': 'Invoice Number,',\n", "  'tail': ',',\n", "  'strategy': 'fuzz',\n", "  'rule': {'extraction_rule': [{'TEXT': {'REGEX': '[0-9]'}, 'LENGTH': 10}],\n", "   'one': [{'LOWER': 'invoice number'},\n", "    {'OP': '*'},\n", "    {'TEXT': {'REGEX': '[0-9]'}, 'LENGTH': 10}]},\n", "  'conditionfield': {'vendor_name': 'verizon'},\n", "  'hascondition': True,\n", "  'regex': None}]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["from Invoice.DbConfig import *\n", "#db_extrtaction_list=get_extraction_list('sou_config','NearestDistance',hascondition_=True)\n", "db_extrtaction_list=get_extraction_list('global_config','Horizontal',hascondition_=True)\n", "db_extrtaction_list"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["from Invoice.models import *"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'name': 'invoice_no',\n", "  'type': 'Rule',\n", "  'head': 'Invoice Number,',\n", "  'tail': ',',\n", "  'strategy': 'fuzz',\n", "  'rule': {'extraction_rule': [{'TEXT': {'REGEX': '[0-9]'}, 'LENGTH': 10}],\n", "   'one': [{'LOWER': 'invoice number'},\n", "    {'OP': '*'},\n", "    {'TEXT': {'REGEX': '[0-9]'}, 'LENGTH': 10}]},\n", "  'conditionfield': {'vendor_name': 'verizon'},\n", "  'hascondition': True,\n", "  'regex': None}]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["#len(db_extrtaction_list)\n", "db_extrtaction_list[1:2]\n", "\n", "#res=kvalue.objects.filter(id__in=[28, 29, 27, 30, 26, 24, 20] )\n", "#res"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["for extract_field in db_extrtaction_list[1:2]:\n", "\n", "    f_name = None\n", "    f_type = None\n", "    f_head = None\n", "    f_tail = None\n", "    f_regex = None\n", "    f_entityname = None\n", "    f_rule = None\n", "\n", "    if \"name\" in extract_field.keys():\n", "        f_name = extract_field[\"name\"]\n", "\n", "    if \"type\" in extract_field.keys():\n", "        f_type = extract_field[\"type\"]\n", "\n", "    if \"head\" in extract_field.keys():\n", "        f_head = extract_field[\"head\"]\n", "        if len(f_head.strip())>0:\n", "            f_head=f_head.split(\",\")\n", "\n", "    if \"tail\" in extract_field.keys():\n", "        f_tail = extract_field[\"tail\"]\n", "        if len(f_tail.strip())>0:\n", "            f_tail=f_tail.split(\",\")\n", "\n", "    if \"regex\" in extract_field.keys():\n", "        f_regex = extract_field[\"regex\"]\n", "\n", "    if \"entity_name\" in extract_field.keys():\n", "        f_entityname = extract_field[\"regex\"]\n", "\n", "    if \"rule\" in extract_field.keys():\n", "        f_rule = extract_field[\"rule\"]\n", "\n", "    fuzz_result = None\n", "    regex_result = None\n", "    date_result = None\n", "    match_result = None\n", "\n", "    field_details={}\n", "    field_details[\"name\"]=f_name\n", "    field_details[\"type\"]=f_type\n", "    field_details[\"head\"]=f_head\n", "    field_details[\"tail\"]=f_tail\n", "    field_details[\"regex\"]=f_regex\n", "    field_details[\"entity_name\"]=f_entityname\n", "    field_details[\"rule\"]=f_rule"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'linesearch' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-12-d828497343e8>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[1;32m      3\u001b[0m \u001b[0;31m#condition_res\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      4\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m----> 5\u001b[0;31m \u001b[0mres\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mlinesearch\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0msearch_field\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mlines\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mfield_details\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m      6\u001b[0m \u001b[0mres\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mNameError\u001b[0m: name 'linesearch' is not defined"]}], "source": ["from Invoice.QueryConditions import *\n", "#condition_res=matchjson_and_condition(form_fields,extract_field[\"conditionfield\"])\n", "#condition_res\n", "\n", "res=linesearch.search_field(lines,field_details)\n", "res"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'name': 'asg_dan_invoice_q',\n", " 'type': 'Regex',\n", " 'head': ['invoice', ''],\n", " 'tail': ['', ''],\n", " 'regex': [{'id': 10,\n", "   'name': 'dantrawl invoiceno',\n", "   'description': '',\n", "   'regex': '[0-9a-zA-Z-]{10}'}],\n", " 'entity_name': None,\n", " 'rule': {}}"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["\"\"\" \n", "field_details['rule']={'one': [{'LOWER': 'date'},\n", "   {'LOWER': 'amount', 'OP': '?'},\n", "   {'OP': '*'},\n", "   {'ENT_TYPE': 'DATE'}]}\n", "\n", "field_details['type']=\"Rule\"\n", "\"\"\"\n", "field_details"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["form_fields=[{'Issue_Date': '2021-08-19 00:00:00', 'confidence_level': 99.**************}, {'currency': 'EUR', 'confidence_level': 95}, {'PACKING': 'CARDBOX', 'confidence_level': 98.5}, {'DHL ACC. #': '*********', 'confidence_level': 97.5}, {'DATE': '19.08.2021', 'confidence_level': 97.0}, {'DIM./ CM': '49 X 24 X 20', 'confidence_level': 96.0}, {'TOTAL EUR': '299,65 *', 'confidence_level': 94.5}, {'NO.': '9992.2', 'confidence_level': 94.5}, {'ACCOUNT': '10001', 'confidence_level': 93.5}, {'ATTN.': 'ACCOUNTS PAYABLE', 'confidence_level': 44.5}, {'DHL #': '24 6566 2150', 'confidence_level': 90.0}, {'WEIGHT/ KG': '1.3', 'confidence_level': 90.0}, {'DUE DATE': '18.09.2021', 'confidence_level': 88.5}, {'TEL.': '************', 'confidence_level': 80.5}, {'CONSIGNEE': 'F/V : NORTHERN JAEGER P/O. : JAEEN43934A C/O AMERICAN SEAFOODS CO. LLC 2001 WEST GARFIELD, PIER 90, BLDG. A-310 SEATTLE, WA 98119 / U. S. A.', 'confidence_level': 71.0}, {'Fax': '+49(421) 32 03 01', 'confidence_level': 50.0}, {'Ust-IdNr./VAT-No.': 'DE114422831', 'confidence_level': 47.0}, {'PAYMENT TERMS': '30 DAYS FROM DATE OF INVOICE. FOREIGN BANK CHARGES HAVE TO BE PAID FROM DEBTOR.', 'confidence_level': 43.5}, {'ACC.': 'IBAN DE74 2904 0090 0105 7066 00', 'confidence_level': 43.0}, {'Email': '<EMAIL>', 'confidence_level': 40.5}, {'Fon': '+49(421) 32 77 66', 'confidence_level': 39.0}, {'REMIT TO': 'COMMERZBANK AG, SCHUESSELKORB 5-11 D-28195 BREMEN/ GERMANY', 'confidence_level': 31.5}, {'TOTAL': '299,65', 'confidence_level': 29.0}, {'HTS CODE': '8536.50.9055', 'confidence_level': 24.5}, {'SUM TOTAL': '299,65 €', 'confidence_level': 18.0}, {'Bank': 'Commerzbank AG, Bremen', 'confidence_level': 18.0}, {'IBAN': 'DE74 2904 0090 0105 7066 00', 'confidence_level': 17.0}, {'0% VAT': '0,00 €', 'confidence_level': 15.5}, {'SEATTLE': 'WA 98121', 'confidence_level': 14.0}, {'vendor_name': 'MARITIME SPARE PARTS GMBH', 'confidence_level': 0.********}]\n", "inv_list={0: '/home/<USER>/invoice/invoice-flow/inv-flow-dvc/db-repo/aavenir.invoiceflow.ai/Invoice/pdf/4747e95b-e788-426e-9a51-eae8cf334b5e_0.png'}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["r={'one': [{'LOWER': 'date'},\n", "   {'LOWER': 'amount', 'OP': '?'},\n", "   {'OP': '*'},\n", "   {'ENT_TYPE': 'DATE'}]}\n", "\n", "#for i in r[0]:\n", "#    print(i)\n", "entity_type=None\n", "key=list(r.keys() )[0]\n", "for i in r[key]:\n", "    k=list(i.keys())[0]\n", "    if k==\"ENT_TYPE\":\n", "        entity_type=i[k]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nlp\n", "#import spacy \n", "#nlp=spacy.load(\"en_core_web_sm\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mapping_file=\"/home/<USER>/invoice/invoice-flow/inv-flow-dvc/db-repo/aavenir.invoiceflow.ai/Invoice/pdf/18311994-9202-496a-993b-91a201568054_0mapping.json\"\n", "\n", "with open(mapping_file) as f:\n", "    mapping_data = json.load(f)\n", "    \n", "table_util_obj = TableUtils()\n", "#mapping_data\n", "\n", "im = Image.open(inv_page_path)\n", "width, height = im.size\n", "\n", "csv_path = table_util_obj.parse_main(inv_page_path, json_path, height,mapping_file)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["top ....  0\n", "bottom......  0\n", "******************** asg_dan_invoice_q ******************************\n", "word list \n", "['invoice ', 'invoice ', ' ', ' ']\n", "-->dantrawl inc.invoice30\n", "-->invoiceinvoice100\n", "-->invoiceinvoice100\n", "-->6221 180th st. ne, arlington, wa 98223invoice13\n", "-->phone: (206)789-8840invoice15\n", "-->0130187-ininvoice24\n", "-->fax: (206)789-8973invoice0\n", "-->e-mail: sales@dantrawl.cominvoice18\n", "-->sold to:invoice13\n", "-->ship to:invoice27\n", "-->f/t katie anninvoice20\n", "-->f/t katie anninvoice20\n", "-->american seafoods company llc.invoice22\n", "-->c/o asc warehouseinvoice25\n", "-->2025 first ave ste# 900invoice20\n", "-->2001 west garfieldinvoice16\n", "-->seattle, wa 98121invoice8\n", "-->seattle, wa 98119invoice8\n", "-->date:invoice17\n", "-->8/11/2022invoice0\n", "-->quote/order no.: 0041678invoice13\n", "-->customer number:invoice17\n", "-->katieaninvoice29\n", "-->ship via:invoice38\n", "-->our truckinvoice25\n", "-->customer p.o.invoice20\n", "-->kanddk32812invoice11\n", "-->f.o.b.:invoice14\n", "-->arlingtoninvoice38\n", "-->confirm to:invoice22\n", "-->john leeinvoice27\n", "-->terms:invoice15\n", "-->2% 15 days; net 30 daysinvoice13\n", "-->item numberinvoice33\n", "-->unitinvoice36\n", "-->orderedinvoice29\n", "-->shippedinvoice29\n", "-->priceinvoice50\n", "-->amountinvoice15\n", "-->netconstinvoice27\n", "-->aft-end made for internal excluder fitting both fiska iiinvoice16\n", "-->eachinvoice18\n", "-->1invoice0\n", "-->1invoice0\n", "-->26,800.00invoice0\n", "-->26,800.00invoice0\n", "-->416/8 and pop trawl 416/8.invoice12\n", "-->made in 5 1/2\" x 6mm single and 5mm single andinvoice15\n", "-->doubleinvoice31\n", "-->riblines to have stainless masterlinks forward,invoice19\n", "-->middle and aft.invoice18\n", "-->flymeshes in spectra 3/8\"invoice19\n", "-->ready to be sewn on to trawl.invoice11\n", "-->netconstinvoice27\n", "-->internal excluder for fiska trawl ii 416/8 and popinvoice14\n", "-->eachinvoice18\n", "-->iinvoice25\n", "-->1invoice0\n", "-->24,925.00invoice0\n", "-->24,925.00invoice0\n", "-->trawl 416/8invoice0\n", "-->made in 14\"bk x 1000ply knotless (7\"bk sq.mesh)invoice15\n", "-->installed into aft end complete with entrance andinvoice21\n", "-->exit into lower panel.invoice28\n", "-->*invoice0\n", "-->complete with floats and parachutes.invoice19\n", "-->kan 2022invoice13\n", "-->123-k0h-562060invoice0\n", "-->o.b.invoice18\n", "-->8/15-22invoice0\n", "-->net invoice:invoice74\n", "-->51,725.00invoice0\n", "-->tracking no.:invoice30\n", "-->dt bl no 38828;invoice18\n", "-->freight:invoice13\n", "-->0.00invoice0\n", "-->sales tax:invoice12\n", "-->0.00invoice0\n", "-->invoice total (us dollars):invoice41\n", "-->51,725.00invoice0\n", "-->overdue accounts will be subject to interest at i 1/2% per month and any other collection charges.invoice11\n", "-->attorney fees, if necessary for collection, will be chargeable to customer.invoice15\n", "-->dantrawl inc.invoice30\n", "-->invoiceinvoice100\n", "-->6221 180th st. ne, arlington, wa 98223invoice13\n", "-->phone: (206)789-8840invoice15\n", "-->0130187-ininvoice24\n", "-->fax: (206)789-8973invoice0\n", "-->e-mail: sales@dantrawl.cominvoice18\n", "-->sold to:invoice13\n", "-->ship to:invoice27\n", "-->f/t katie anninvoice20\n", "-->f/t katie anninvoice20\n", "-->american seafoods company llc.invoice22\n", "-->c/o asc warehouseinvoice25\n", "-->2025 first ave ste# 900invoice20\n", "-->2001 west garfieldinvoice16\n", "-->seattle, wa 98121invoice8\n", "-->seattle, wa 98119invoice8\n", "-->date:invoice17\n", "-->8/11/2022invoice0\n", "-->quote/order no.: 0041678invoice13\n", "-->customer number:invoice17\n", "-->katieaninvoice29\n", "-->ship via:invoice38\n", "-->our truckinvoice25\n", "-->customer p.o.invoice20\n", "-->kanddk32812invoice11\n", "-->f.o.b.:invoice14\n", "-->arlingtoninvoice38\n", "-->confirm to:invoice22\n", "-->john leeinvoice27\n", "-->terms:invoice15\n", "-->2% 15 days; net 30 daysinvoice13\n", "-->item numberinvoice33\n", "-->unitinvoice36\n", "-->orderedinvoice29\n", "-->shippedinvoice29\n", "-->priceinvoice50\n", "-->amountinvoice15\n", "-->netconstinvoice27\n", "-->aft-end made for internal excluder fitting both fiska iiinvoice16\n", "-->eachinvoice18\n", "-->1invoice0\n", "-->1invoice0\n", "-->26,800.00invoice0\n", "-->26,800.00invoice0\n", "-->416/8 and pop trawl 416/8.invoice12\n", "-->made in 5 1/2\" x 6mm single and 5mm single andinvoice15\n", "-->doubleinvoice31\n", "-->riblines to have stainless masterlinks forward,invoice19\n", "-->middle and aft.invoice18\n", "-->flymeshes in spectra 3/8\"invoice19\n", "-->ready to be sewn on to trawl.invoice11\n", "-->netconstinvoice27\n", "-->internal excluder for fiska trawl ii 416/8 and popinvoice14\n", "-->eachinvoice18\n", "-->iinvoice25\n", "-->1invoice0\n", "-->24,925.00invoice0\n", "-->24,925.00invoice0\n", "-->trawl 416/8invoice0\n", "-->made in 14\"bk x 1000ply knotless (7\"bk sq.mesh)invoice15\n", "-->installed into aft end complete with entrance andinvoice21\n", "-->exit into lower panel.invoice28\n", "-->*invoice0\n", "-->complete with floats and parachutes.invoice19\n", "-->kan 2022invoice13\n", "-->123-k0h-562060invoice0\n", "-->o.b.invoice18\n", "-->8/15-22invoice0\n", "-->net invoice:invoice74\n", "-->51,725.00invoice0\n", "-->tracking no.:invoice30\n", "-->dt bl no 38828;invoice18\n", "-->freight:invoice13\n", "-->0.00invoice0\n", "-->sales tax:invoice12\n", "-->0.00invoice0\n", "-->invoice total (us dollars):invoice41\n", "-->51,725.00invoice0\n", "-->overdue accounts will be subject to interest at i 1/2% per month and any other collection charges.invoice11\n", "-->attorney fees, if necessary for collection, will be chargeable to customer.invoice15\n", "-->dantrawl inc.0\n", "-->invoice0\n", "-->6221 180th st. ne, arlington, wa 982230\n", "-->phone: (206)789-88400\n", "-->0130187-in0\n", "-->fax: (206)789-89730\n", "-->e-mail: sales@dantrawl.com0\n", "-->sold to:0\n", "-->ship to:0\n", "-->f/t katie ann0\n", "-->f/t katie ann0\n", "-->american seafoods company llc.0\n", "-->c/o asc warehouse0\n", "-->2025 first ave ste# 9000\n", "-->2001 west garfield0\n", "-->seattle, wa 981210\n", "-->seattle, wa 981190\n", "-->date:0\n", "-->8/11/20220\n", "-->quote/order no.: ********\n", "-->customer number:0\n", "-->katiean0\n", "-->ship via:0\n", "-->our truck0\n", "-->customer p.o.0\n", "-->kanddk328120\n", "-->f.o.b.:0\n", "-->arlington0\n", "-->confirm to:0\n", "-->john lee0\n", "-->terms:0\n", "-->2% 15 days; net 30 days0\n", "-->item number0\n", "-->unit0\n", "-->ordered0\n", "-->shipped0\n", "-->price0\n", "-->amount0\n", "-->netconst0\n", "-->aft-end made for internal excluder fitting both fiska ii0\n", "-->each0\n", "-->10\n", "-->10\n", "-->26,800.000\n", "-->26,800.000\n", "-->416/8 and pop trawl 416/8.0\n", "-->made in 5 1/2\" x 6mm single and 5mm single and0\n", "-->double0\n", "-->riblines to have stainless masterlinks forward,0\n", "-->middle and aft.0\n", "-->flymeshes in spectra 3/8\"0\n", "-->ready to be sewn on to trawl.0\n", "-->netconst0\n", "-->internal excluder for fiska trawl ii 416/8 and pop0\n", "-->each0\n", "-->i0\n", "-->10\n", "-->24,925.000\n", "-->24,925.000\n", "-->trawl 416/80\n", "-->made in 14\"bk x 1000ply knotless (7\"bk sq.mesh)0\n", "-->installed into aft end complete with entrance and0\n", "-->exit into lower panel.0\n", "-->*0\n", "-->complete with floats and parachutes.0\n", "-->kan 20220\n", "-->123-k0h-5620600\n", "-->o.b.0\n", "-->8/15-220\n", "-->net invoice:0\n", "-->51,725.000\n", "-->tracking no.:0\n", "-->dt bl no 38828;0\n", "-->freight:0\n", "-->0.000\n", "-->sales tax:0\n", "-->0.000\n", "-->invoice total (us dollars):0\n", "-->51,725.000\n", "-->overdue accounts will be subject to interest at i 1/2% per month and any other collection charges.0\n", "-->attorney fees, if necessary for collection, will be chargeable to customer.0\n", "-->dantrawl inc.0\n", "-->invoice0\n", "-->6221 180th st. ne, arlington, wa 982230\n", "-->phone: (206)789-88400\n", "-->0130187-in0\n", "-->fax: (206)789-89730\n", "-->e-mail: sales@dantrawl.com0\n", "-->sold to:0\n", "-->ship to:0\n", "-->f/t katie ann0\n", "-->f/t katie ann0\n", "-->american seafoods company llc.0\n", "-->c/o asc warehouse0\n", "-->2025 first ave ste# 9000\n", "-->2001 west garfield0\n", "-->seattle, wa 981210\n", "-->seattle, wa 981190\n", "-->date:0\n", "-->8/11/20220\n", "-->quote/order no.: ********\n", "-->customer number:0\n", "-->katiean0\n", "-->ship via:0\n", "-->our truck0\n", "-->customer p.o.0\n", "-->kanddk328120\n", "-->f.o.b.:0\n", "-->arlington0\n", "-->confirm to:0\n", "-->john lee0\n", "-->terms:0\n", "-->2% 15 days; net 30 days0\n", "-->item number0\n", "-->unit0\n", "-->ordered0\n", "-->shipped0\n", "-->price0\n", "-->amount0\n", "-->netconst0\n", "-->aft-end made for internal excluder fitting both fiska ii0\n", "-->each0\n", "-->10\n", "-->10\n", "-->26,800.000\n", "-->26,800.000\n", "-->416/8 and pop trawl 416/8.0\n", "-->made in 5 1/2\" x 6mm single and 5mm single and0\n", "-->double0\n", "-->riblines to have stainless masterlinks forward,0\n", "-->middle and aft.0\n", "-->flymeshes in spectra 3/8\"0\n", "-->ready to be sewn on to trawl.0\n", "-->netconst0\n", "-->internal excluder for fiska trawl ii 416/8 and pop0\n", "-->each0\n", "-->i0\n", "-->10\n", "-->24,925.000\n", "-->24,925.000\n", "-->trawl 416/80\n", "-->made in 14\"bk x 1000ply knotless (7\"bk sq.mesh)0\n", "-->installed into aft end complete with entrance and0\n", "-->exit into lower panel.0\n", "-->*0\n", "-->complete with floats and parachutes.0\n", "-->kan 20220\n", "-->123-k0h-5620600\n", "-->o.b.0\n", "-->8/15-220\n", "-->net invoice:0\n", "-->51,725.000\n", "-->tracking no.:0\n", "-->dt bl no 38828;0\n", "-->freight:0\n", "-->0.000\n", "-->sales tax:0\n", "-->0.000\n", "-->invoice total (us dollars):0\n", "-->51,725.000\n", "-->overdue accounts will be subject to interest at i 1/2% per month and any other collection charges.0\n", "-->attorney fees, if necessary for collection, will be chargeable to customer.0\n", "Best Match Found ('Invoice', 0.****************, 0.*****************, 0.****************, 0.*****************, (0.**************, 0.*****************), 98.**************)\n", "### DANTRAWL INC. degrees :-179 match word :Invoice | distance :0.****************\n", "### Invoice degrees :0 match word :Invoice | distance :0.*****************\n", "### 6221 180th St. NE, Arlington, WA 98223 degrees :177 match word :Invoice | distance :0.****************\n", "### Phone: (206)789-8840 degrees :176 match word :Invoice | distance :0.****************\n", "### 0130187-IN degrees :101 match word :Invoice | distance :0.****************\n", "### Fax: (206)789-8973 degrees :175 match word :Invoice | distance :0.****************\n", "### E-Mail: <EMAIL> degrees :174 match word :Invoice | distance :0.****************\n", "### Sold To: degrees :169 match word :Invoice | distance :0.8318417047484584\n", "### Ship To: degrees :151 match word :Invoice | distance :0.36333811160178897\n", "### F/T Katie Ann degrees :167 match word :Invoice | distance :0.8362515063869481\n", "### F/T Katie Ann degrees :145 match word :Invoice | distance :0.3711555013322141\n", "### American Seafoods Company LLC. degrees :164 match word :Invoice | distance :0.8388548200331317\n", "### C/O ASC Warehouse degrees :140 match word :Invoice | distance :0.37739932428969064\n", "### 2025 First Ave STE# 900 degrees :164 match word :Invoice | distance :0.8422403247753197\n", "### 2001 West Garfield degrees :139 match word :Invoice | distance :0.3845359104396707\n", "### Seattle, WA 98121 degrees :163 match word :Invoice | distance :0.8449771676882122\n", "### Seattle, WA 98119 degrees :137 match word :Invoice | distance :0.3912898207813693\n", "### Date: degrees :161 match word :Invoice | distance :0.8783977833081371\n", "### 8/11/2022 degrees :157 match word :Invoice | distance :0.7566315075617851\n", "### Quote/Order No.: 0041678 degrees :127 match word :Invoice | distance :0.4188507846861116\n", "### Customer Number: degrees :159 match word :Invoice | distance :0.881813419215834\n", "### KATIEAN degrees :156 match word :Invoice | distance :0.7610979151090089\n", "### Ship VIA: degrees :132 match word :Invoice | distance :0.4281629154300573\n", "### OUR TRUCK degrees :112 match word :Invoice | distance :0.34028746133838306\n", "### Customer P.O. degrees :159 match word :Invoice | distance :0.8856241417173851\n", "### KANDDK32812 degrees :154 match word :Invoice | distance :0.7664269402172822\n", "### F.O.B.: degrees :131 match word :Invoice | distance :0.4391953951982883\n", "### Arlington degrees :114 match word :Invoice | distance :0.35358605246291686\n", "### Confirm To: degrees :158 match word :Invoice | distance :0.8915697982091595\n", "### <PERSON> degrees :154 match word :Invoice | distance :0.7708455160576544\n", "### Terms: degrees :130 match word :Invoice | distance :0.44982257982020535\n", "### 2% 15 Days; Net 30 Days degrees :104 match word :Invoice | distance :0.3650976543908389\n", "### Item Number degrees :156 match word :Invoice | distance :0.9416723500656742\n", "### Unit degrees :135 match word :Invoice | distance :0.5368562000872208\n", "### Ordered degrees :125 match word :Invoice | distance :0.47046449244145655\n", "### Shipped degrees :115 match word :Invoice | distance :0.41726022135981455\n", "### Price degrees :98 match word :Invoice | distance :0.3590416340178956\n", "### Amount degrees :83 match word :Invoice | distance :0.33940792146768584\n", "### NETCONST degrees :154 match word :Invoice | distance :0.9531951968210075\n", "### Aft-end made for internal excluder fitting both Fiska II degrees :145 match word :Invoice | distance :0.8432464693466661\n", "### EACH degrees :132 match word :Invoice | distance :0.5573546312335765\n", "### 1 degrees :120 match word :Invoice | distance :0.4602409977353925\n", "### 1 degrees :110 match word :Invoice | distance :0.41875174839594975\n", "### 26,800.00 degrees :100 match word :Invoice | distance :0.39863719294883115\n", "### 26,800.00 degrees :82 match word :Invoice | distance :0.36925416363150465\n", "### 416/8 and <PERSON><PERSON> Trawl 416/8. degrees :148 match word :Invoice | distance :0.8476658010794589\n", "### Made in 5 1/2\" X 6mm single and 5mm single and degrees :143 match word :Invoice | distance :0.8481458959256227\n", "### double degrees :148 match word :Invoice | distance :0.8660589724132561\n", "### Riblines to have stainless masterlinks forward, degrees :141 match word :Invoice | distance :0.8604583842315292\n", "### middle and aft. degrees :145 match word :Invoice | distance :0.8784976965204031\n", "### Flymeshes in Spectra 3/8\" degrees :143 match word :Invoice | distance :0.872976374601927\n", "### Ready to be sewn on to trawl. degrees :141 match word :Invoice | distance :0.8799839288878257\n", "### NETCONST degrees :146 match word :Invoice | distance :1.0149691229518594\n", "### Internal excluder for fiska trawl II 416/8 and POP degrees :137 match word :Invoice | distance :0.9127186414924761\n", "### EACH degrees :123 match word :Invoice | distance :0.6580964384694056\n", "### I degrees :112 match word :Invoice | distance :0.5785512110909072\n", "### 1 degrees :105 match word :Invoice | distance :0.5461074414557645\n", "### 24,925.00 degrees :97 match word :Invoice | distance :0.5310393774672628\n", "### 24,925.00 degrees :84 match word :Invoice | distance :0.509242208629957\n", "### Trawl 416/8 degrees :141 match word :Invoice | distance :0.9199397662676403\n", "### Made in 14\"bk x 1000Ply knotless (7\"bk Sq.mesh) degrees :134 match word :Invoice | distance :0.9232027937634687\n", "### Installed into aft end complete with entrance and degrees :134 match word :Invoice | distance :0.9304915438310903\n", "### exit into lower panel. degrees :138 match word :Invoice | distance :0.9489307236494557\n", "### * degrees :139 match word :Invoice | distance :0.9564746503624192\n", "### Complete with floats and parachutes. degrees :134 match word :Invoice | distance :0.9458560077693335\n", "### KAN 2022 degrees :121 match word :Invoice | distance :0.7954822629737321\n", "### 123-k0H-562060 degrees :118 match word :Invoice | distance :0.8210608912875482\n", "### O.B. degrees :115 match word :Invoice | distance :0.7650090092064975\n", "### 8/15-22 degrees :113 match word :Invoice | distance :0.7888276805281923\n", "### Net Invoice: degrees :99 match word :Invoice | distance :0.7571350848333499\n", "### 51,725.00 degrees :87 match word :Invoice | distance :0.726353704863391\n", "### Tracking No.: degrees :136 match word :Invoice | distance :1.1424264827311874\n", "### DT BL No 38828; degrees :135 match word :Invoice | distance :1.1529031583936218\n", "### Freight: degrees :98 match word :Invoice | distance :0.****************\n", "### 0.00 degrees :86 match word :Invoice | distance :0.***************\n", "### Sales Tax: degrees :98 match word :Invoice | distance :0.****************\n", "### 0.00 degrees :86 match word :Invoice | distance :0.****************\n", "### Invoice Total (US Dollars): degrees :102 match word :Invoice | distance :0.****************\n", "### 51,725.00 degrees :87 match word :Invoice | distance :0.****************\n", "### Overdue accounts will be subject to interest at I 1/2% per month and any other collection charges. degrees :116 match word :Invoice | distance :1.****************\n", "### Attorney fees, if necessary for collection, will be chargeable to customer. degrees :116 match word :Invoice | distance :1.****************\n", "*************dist list ****************\n", "{}\n", "*************asg_dan_invoice_q********************\n", "match list {}\n", "match list keys []\n"]}], "source": ["\n", "f=FieldSearch()\n", "field_value,field_conf= f.search_field(json_path,field_details,0,0,\"LINE\" )"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["#field_value,field_conf\n", "res"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["0.028554411394438633  < 0.25"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'name': 'invoice_no', 'type': 'Rule', 'head': ['Invoice Number', ''], 'tail': ['', ''], 'regex': None, 'entity_name': None, 'rule': {'extraction_rule': [{'TEXT': {'REGEX': '[0-9]'}, 'LENGTH': 10}], 'one': [{'LOWER': 'invoice number'}, {'OP': '*'}, {'TEXT': {'REGEX': '[0-9]'}, 'LENGTH': 10}]}}\n", "word list \n", "['Invoice Number ', 'Invoice Number ', ' ', ' ']\n", "-->verizoninvoice number43\n", "-->po box 489 manage your account account number date dueinvoice number57\n", "-->newark, nj 07101-0489invoice number29\n", "-->www.vzw.com/mybusinessaccount *********-00001 04/01/20invoice number29\n", "-->change your address atinvoice number36\n", "-->invoice number ********** http://sso.verizonenterprise.cominvoice number100\n", "-->********/5200/ 1.716/mb/********.2 quick bill summary feb 10 - mar 09invoice number36\n", "-->welldynerx llc ******** previous balance (see back for details) $3,433.22invoice number36\n", "-->500 eagles landing dr msp 55 payment - thank you -$3,433.22invoice number29\n", "-->lakeland, fl 33810-2899invoice number14\n", "-->balance forward $.00invoice number36\n", "-->account charges and creditsinvoice number36\n", "-->includes late fee of $39.63 $39.63invoice number43\n", "-->monthly charges $2,650.00invoice number21\n", "-->usage and purchase chargesinvoice number29\n", "-->voice $.00invoice number60\n", "-->messaging $.00invoice number21\n", "-->datainvoice number0\n", "-->$.00invoice number0\n", "-->equipment charges $791.54invoice number29\n", "-->escented surchargesinvoice number36\n", "-->and other charges & credits -$153.80invoice number36\n", "-->mar 19 2020 taxes, governmental surcharges & fees $125.69invoice number36\n", "-->20invoice number0\n", "-->total current charges $3,453.06invoice number29\n", "-->total charges due by april 01, 2020 $3,453.06invoice number36\n", "-->pay from phone pay on the web questions:invoice number36\n", "-->#pmt (#768) at vzw.com/mybusinessaccount ************** or *611 from your phoneinvoice number29\n", "-->verizoninvoice number43\n", "-->bill date march 09, 2020invoice number36\n", "-->account number *********-00001invoice number57\n", "-->invoice number **********invoice number100\n", "-->welldynerx llcinvoice number29\n", "-->500 eagles landing dr total amount due by april 01, 2020invoice number36\n", "-->lakeland, fl 33810-2899invoice number14\n", "-->make check payable to verizon wireless.invoice number36\n", "-->please return this remit slip with payment.invoice number29\n", "-->$3,453.06invoice number0\n", "-->$ , ,invoice number20\n", "-->po box 660108invoice number23\n", "-->dallas, tx 75266-0108invoice number7\n", "-->**********010*********0000100000345306000003453064invoice number0\n", "-->notice: bank account and routing numbers will be retained to enable future payments by phone or online to opt out, call 1 -866 544 0401invoice number64\n", "-->verizoninvoice number43\n", "-->po box 489 manage your account account number date dueinvoice number57\n", "-->newark, nj 07101-0489invoice number29\n", "-->www.vzw.com/mybusinessaccount *********-00001 04/01/20invoice number29\n", "-->change your address atinvoice number36\n", "-->invoice number ********** http://sso.verizonenterprise.cominvoice number100\n", "-->********/5200/ 1.716/mb/********.2 quick bill summary feb 10 - mar 09invoice number36\n", "-->welldynerx llc ******** previous balance (see back for details) $3,433.22invoice number36\n", "-->500 eagles landing dr msp 55 payment - thank you -$3,433.22invoice number29\n", "-->lakeland, fl 33810-2899invoice number14\n", "-->balance forward $.00invoice number36\n", "-->account charges and creditsinvoice number36\n", "-->includes late fee of $39.63 $39.63invoice number43\n", "-->monthly charges $2,650.00invoice number21\n", "-->usage and purchase chargesinvoice number29\n", "-->voice $.00invoice number60\n", "-->messaging $.00invoice number21\n", "-->datainvoice number0\n", "-->$.00invoice number0\n", "-->equipment charges $791.54invoice number29\n", "-->escented surchargesinvoice number36\n", "-->and other charges & credits -$153.80invoice number36\n", "-->mar 19 2020 taxes, governmental surcharges & fees $125.69invoice number36\n", "-->20invoice number0\n", "-->total current charges $3,453.06invoice number29\n", "-->total charges due by april 01, 2020 $3,453.06invoice number36\n", "-->pay from phone pay on the web questions:invoice number36\n", "-->#pmt (#768) at vzw.com/mybusinessaccount ************** or *611 from your phoneinvoice number29\n", "-->verizoninvoice number43\n", "-->bill date march 09, 2020invoice number36\n", "-->account number *********-00001invoice number57\n", "-->invoice number **********invoice number100\n", "-->welldynerx llcinvoice number29\n", "-->500 eagles landing dr total amount due by april 01, 2020invoice number36\n", "-->lakeland, fl 33810-2899invoice number14\n", "-->make check payable to verizon wireless.invoice number36\n", "-->please return this remit slip with payment.invoice number29\n", "-->$3,453.06invoice number0\n", "-->$ , ,invoice number20\n", "-->po box 660108invoice number23\n", "-->dallas, tx 75266-0108invoice number7\n", "-->**********010*********0000100000345306000003453064invoice number0\n", "-->notice: bank account and routing numbers will be retained to enable future payments by phone or online to opt out, call 1 -866 544 0401invoice number64\n", "-->verizon0\n", "-->po box 489 manage your account account number date due0\n", "-->newark, nj 07101-04890\n", "-->www.vzw.com/mybusinessaccount *********-00001 04/01/200\n", "-->change your address at0\n", "-->invoice number ********** http://sso.verizonenterprise.com0\n", "-->********/5200/ 1.716/mb/********.2 quick bill summary feb 10 - mar 090\n", "-->welldynerx llc ******** previous balance (see back for details) $3,433.220\n", "-->500 eagles landing dr msp 55 payment - thank you -$3,433.220\n", "-->lakeland, fl 33810-28990\n", "-->balance forward $.000\n", "-->account charges and credits0\n", "-->includes late fee of $39.63 $39.630\n", "-->monthly charges $2,650.000\n", "-->usage and purchase charges0\n", "-->voice $.000\n", "-->messaging $.000\n", "-->data0\n", "-->$.000\n", "-->equipment charges $791.540\n", "-->escented surcharges0\n", "-->and other charges & credits -$153.800\n", "-->mar 19 2020 taxes, governmental surcharges & fees $125.690\n", "-->200\n", "-->total current charges $3,453.060\n", "-->total charges due by april 01, 2020 $3,453.060\n", "-->pay from phone pay on the web questions:0\n", "-->#pmt (#768) at vzw.com/mybusinessaccount ************** or *611 from your phone0\n", "-->verizon0\n", "-->bill date march 09, 20200\n", "-->account number *********-000010\n", "-->invoice number **********0\n", "-->welldynerx llc0\n", "-->500 eagles landing dr total amount due by april 01, 20200\n", "-->lakeland, fl 33810-28990\n", "-->make check payable to verizon wireless.0\n", "-->please return this remit slip with payment.0\n", "-->$3,453.060\n", "-->$ , ,0\n", "-->po box 6601080\n", "-->dallas, tx 75266-01080\n", "-->**********010*********00001000003453060000034530640\n", "-->notice: bank account and routing numbers will be retained to enable future payments by phone or online to opt out, call 1 -866 544 04010\n", "-->verizon0\n", "-->po box 489 manage your account account number date due0\n", "-->newark, nj 07101-04890\n", "-->www.vzw.com/mybusinessaccount *********-00001 04/01/200\n", "-->change your address at0\n", "-->invoice number ********** http://sso.verizonenterprise.com0\n", "-->********/5200/ 1.716/mb/********.2 quick bill summary feb 10 - mar 090\n", "-->welldynerx llc ******** previous balance (see back for details) $3,433.220\n", "-->500 eagles landing dr msp 55 payment - thank you -$3,433.220\n", "-->lakeland, fl 33810-28990\n", "-->balance forward $.000\n", "-->account charges and credits0\n", "-->includes late fee of $39.63 $39.630\n", "-->monthly charges $2,650.000\n", "-->usage and purchase charges0\n", "-->voice $.000\n", "-->messaging $.000\n", "-->data0\n", "-->$.000\n", "-->equipment charges $791.540\n", "-->escented surcharges0\n", "-->and other charges & credits -$153.800\n", "-->mar 19 2020 taxes, governmental surcharges & fees $125.690\n", "-->200\n", "-->total current charges $3,453.060\n", "-->total charges due by april 01, 2020 $3,453.060\n", "-->pay from phone pay on the web questions:0\n", "-->#pmt (#768) at vzw.com/mybusinessaccount ************** or *611 from your phone0\n", "-->verizon0\n", "-->bill date march 09, 20200\n", "-->account number *********-000010\n", "-->invoice number **********0\n", "-->welldynerx llc0\n", "-->500 eagles landing dr total amount due by april 01, 20200\n", "-->lakeland, fl 33810-28990\n", "-->make check payable to verizon wireless.0\n", "-->please return this remit slip with payment.0\n", "-->$3,453.060\n", "-->$ , ,0\n", "-->po box 6601080\n", "-->dallas, tx 75266-01080\n", "-->**********010*********00001000003453060000034530640\n", "-->notice: bank account and routing numbers will be retained to enable future payments by phone or online to opt out, call 1 -866 544 04010\n", "Best Match Found  [' Invoice Number ********** http://sso.verizonenterprise.com ', ' Invoice Number ********** ', ' Invoice Number ********** http://sso.verizonenterprise.com ', ' Invoice Number ********** ']\n", "in matchrule \n", "text is \n", " Invoice Number ********** http://sso.verizonenterprise.com \n", "extraction_rule  [{'TEXT': {'REGEX': '[0-9]'}, 'LENGTH': 10}]\n", "adding matcher [{'LOWER': 'invoice number'}, {'OP': '*'}, {'TEXT': {'REGEX': '[0-9]'}, 'LENGTH': 10}]\n", "results after matcher  []\n", "in matchrule \n", "text is \n", " Invoice Number ********** \n", "extraction_rule  [{'TEXT': {'REGEX': '[0-9]'}, 'LENGTH': 10}]\n", "adding matcher [{'LOWER': 'invoice number'}, {'OP': '*'}, {'TEXT': {'REGEX': '[0-9]'}, 'LENGTH': 10}]\n", "results after matcher  []\n", "in matchrule \n", "text is \n", " Invoice Number ********** http://sso.verizonenterprise.com \n", "extraction_rule  [{'TEXT': {'REGEX': '[0-9]'}, 'LENGTH': 10}]\n", "adding matcher [{'LOWER': 'invoice number'}, {'OP': '*'}, {'TEXT': {'REGEX': '[0-9]'}, 'LENGTH': 10}]\n", "results after matcher  []\n", "in matchrule \n", "text is \n", " Invoice Number ********** \n", "extraction_rule  [{'TEXT': {'REGEX': '[0-9]'}, 'LENGTH': 10}]\n", "adding matcher [{'LOWER': 'invoice number'}, {'OP': '*'}, {'TEXT': {'REGEX': '[0-9]'}, 'LENGTH': 10}]\n", "results after matcher  []\n"]}], "source": ["from Invoice.LineSearch import *\n", "linesearch=LineSearch() \n", "lines=linesearch.createLines(json_path)\n", "res=linesearch.search_field(lines,field_details) \n", "res"]}, {"cell_type": "raw", "metadata": {}, "source": ["print(res)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["raw_dict={'Invoice #: ': ['175341 ', 97.5], 'Page No.: ': ['2 of 2 ', 98.5], 'Cashier No.: ': ['137 ', 97.5], 'Purchase Order: ': ['Rover ', 97.5], 'A/R Number: ': ['648 ', 96.5], 'Folio #: ': ['226048 ', 96.5], 'Room No. ': ['301 ', 94.5], 'Departure ': ['08-31-22 ', 94.5], 'Arrival ': ['06-16-22 ', 93.5], 'Balance: ': ['$ 691.20 ', 63.5], 'Total: ': ['$ 691.20 ', 23.5], 'FAX ': ['************ ', 19.0]}"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["extract field id  sou_extractfields\n", "[25, 26, 27, 28, 30, 31, 32, 34, 7, 59, 29, 60] algo type  None  hascondition_  False\n", "field mapping q\n", "<QuerySet [<kvalue: sou_shipto>, <kvalue: sou_billto>, <kvalue: sou_terms>, <kvalue: sou_Issue Date>, <kvalue: sou_Invoice Number>, <kvalue: SOU_ASG_DSV Invoice No>]>\n", "regex_id  [10]\n", "95\n"]}], "source": ["from Invoice.FieldMatch import *\n", "config_name=\"american_seafoods\"\n", "\n", "field_match=FieldMatch()\n", "\n", "db_extrtaction_list=get_extraction_list(config_name,None,True)\n", "w_list=field_match.cerate_word_list(db_extrtaction_list)\n", "\n", "match_threshold=get_ai_fieldmatch_threshold(config_name)\n", "\n", "print(match_threshold)"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" Key Matching  Invoice #:   -  invoice #: ship to 24\n", " Key Matching  Invoice #:   -  invoice #: ship to 24\n", " Key Matching  Invoice #:   -  invoice #: ship-to 24\n", " Key Matching  Invoice #:   -  invoice #: ship-to 24\n", " Key Matching  Invoice #:   -  invoice #: bill to 24\n", " Key Matching  Invoice #:   -  invoice #: bill to 24\n", " Key Matching  Invoice #:   -  invoice #: bill to: department of technology services 23\n", " Key Matching  Invoice #:   -  invoice #: bill to: department of technology services 23\n", " Key Matching  Invoice #:   -  invoice #: bill to / facturé à: 33\n", " Key Matching  Invoice #:   -  invoice #: bill to / facturé à: 33\n", " Key Matching  Invoice #:   -  invoice #: bill-to 24\n", " Key Matching  Invoice #:   -  invoice #: bill-to 24\n", " Key Matching  Invoice #:   -  invoice #: terms 13\n", " Key Matching  Invoice #:   -  invoice #: terms 13\n", " Key Matching  Invoice #:   -  invoice #: issue 27\n", " Key Matching  Invoice #:   -  invoice #: issue date 30\n", " Key Matching  Invoice #:   -  invoice #: issue 27\n", " Key Matching  Invoice #:   -  invoice #: date 14\n", " Key Matching  Invoice #:   -  invoice #: invoice no 80\n", " Key Matching  Invoice #:   -  invoice #: invoice no. 76\n", " Key Matching  Invoice #:   -  invoice #: invoice id 80\n", " Key Matching  Invoice #:   -  invoice #: invoice # 95\n", " Key Matching  Invoice #:   -  invoice #: invoice number 67\n", " Key Matching  Invoice #:   -  invoice #: invoice 82\n", " Key Matching  Invoice #:   -  invoice #: invoice 82\n", " Key Matching  Invoice #:   -  invoice #: invoice: no 76\n", " Key Matching  Invoice #:   -  invoice #: invoice: no. 73\n", " Key Matching  Invoice #:   -  invoice #: invoice: id 76\n", " Key Matching  Invoice #:   -  invoice #: invoice: # 90\n", " Key Matching  Invoice #:   -  invoice #: invoice: number 64\n", " Key Matching  Invoice #:   -  invoice #: invoice: 89\n", " Key Matching  Invoice #:   -  invoice #: invoice: 89\n", " Key Matching  Invoice #:   -  invoice #: invoice id no 70\n", " Key Matching  Invoice #:   -  invoice #: invoice id no. 67\n", " Key Matching  Invoice #:   -  invoice #: invoice id id 70\n", " Key Matching  Invoice #:   -  invoice #: invoice id # 82\n", " Key Matching  Invoice #:   -  invoice #: invoice id number 59\n", " Key Matching  Invoice #:   -  invoice #: invoice id 80\n", " Key Matching  Invoice #:   -  invoice #: invoice id 80\n", " Key Matching  Invoice #:   -  invoice #: invoice # no 82\n", " Key Matching  Invoice #:   -  invoice #: invoice # no. 78\n", " Key Matching  Invoice #:   -  invoice #: invoice # id 82\n", " Key Matching  Invoice #:   -  invoice #: invoice # # 86\n", " Key Matching  Invoice #:   -  invoice #: invoice # number 69\n", " Key Matching  Invoice #:   -  invoice #: invoice # 95\n", " Key Matching  Invoice #:   -  invoice #: invoice # 95\n", " Key Matching  Invoice #:   -  invoice #: invoice no 80\n", " Key Matching  Invoice #:   -  invoice #: invoice no. 76\n", " Key Matching  Invoice #:   -  invoice #: invoice id 80\n", " Key Matching  Invoice #:   -  invoice #: invoice # 95\n", " Key Matching  Invoice #:   -  invoice #: invoice number 67\n", " Key Matching  Invoice #:   -  invoice #: invoice 82\n", " Key Matching  Invoice #:   -  invoice #: invoice 82\n", " Key Matching  Invoice #:   -  invoice #: invoice # no 82\n", " Key Matching  Invoice #:   -  invoice #: invoice # no. 78\n", " Key Matching  Invoice #:   -  invoice #: invoice # id 82\n", " Key Matching  Invoice #:   -  invoice #: invoice # # 86\n", " Key Matching  Invoice #:   -  invoice #: invoice # number 69\n", " Key Matching  Invoice #:   -  invoice #: invoice # 95\n", " Key Matching  Invoice #:   -  invoice #: invoice # 95\n", " Key Matching  Invoice #:   -  invoice #: no 33\n", " Key Matching  Invoice #:   -  invoice #: no. 31\n", " Key Matching  Invoice #:   -  invoice #: id 17\n", " Key Matching  Invoice #:   -  invoice #: # 18\n", " Key Matching  Invoice #:   -  invoice #: number 25\n", " Key Matching  Invoice #:   -  invoice #: invoice 82\n", " Key Matching  Invoice #:   -  invoice #: invoice 82\n", " Key Matching  Invoice #:   -  invoice #: invoice # 95\n", " Key Matching  Invoice #:   -  invoice #: invoice # 95\n", " Key Matching  Invoice #:   -  invoice #: invoice# 89\n", " Key Matching  Invoice #:   -  invoice #: invoice# 89\n", "Invoice #:  ['175341 ', 97.5] (0, None, None)\n", " Key Matching  Page No.:   -  page no.: ship to 38\n", " Key Matching  Page No.:   -  page no.: ship to 38\n", " Key Matching  Page No.:   -  page no.: ship-to 25\n", " Key Matching  Page No.:   -  page no.: ship-to 25\n", " Key Matching  Page No.:   -  page no.: bill to 25\n", " Key Matching  Page No.:   -  page no.: bill to 25\n", " Key Matching  Page No.:   -  page no.: bill to: department of technology services 24\n", " Key Matching  Page No.:   -  page no.: bill to: department of technology services 24\n", " Key Matching  Page No.:   -  page no.: bill to / facturé à: 21\n", " Key Matching  Page No.:   -  page no.: bill to / facturé à: 21\n", " Key Matching  Page No.:   -  page no.: bill-to 12\n", " Key Matching  Page No.:   -  page no.: bill-to 12\n", " Key Matching  Page No.:   -  page no.: terms 14\n", " Key Matching  Page No.:   -  page no.: terms 14\n", " Key Matching  Page No.:   -  page no.: issue 14\n", " Key Matching  Page No.:   -  page no.: issue date 21\n", " Key Matching  Page No.:   -  page no.: issue 14\n", " Key Matching  Page No.:   -  page no.: date 31\n", " Key Matching  Page No.:   -  page no.: invoice no 42\n", " Key Matching  Page No.:   -  page no.: invoice no. 50\n", " Key Matching  Page No.:   -  page no.: invoice id 21\n", " Key Matching  Page No.:   -  page no.: invoice # 22\n", " Key Matching  Page No.:   -  page no.: invoice number 26\n", " Key Matching  Page No.:   -  page no.: invoice 25\n", " Key Matching  Page No.:   -  page no.: invoice 25\n", " Key Matching  Page No.:   -  page no.: invoice: no 40\n", " Key Matching  Page No.:   -  page no.: invoice: no. 48\n", " Key Matching  Page No.:   -  page no.: invoice: id 30\n", " Key Matching  Page No.:   -  page no.: invoice: # 32\n", " Key Matching  Page No.:   -  page no.: invoice: number 25\n", " Key Matching  Page No.:   -  page no.: invoice: 35\n", " Key Matching  Page No.:   -  page no.: invoice: 35\n", " Key Matching  Page No.:   -  page no.: invoice id no 36\n", " Key Matching  Page No.:   -  page no.: invoice id no. 43\n", " Key Matching  Page No.:   -  page no.: invoice id id 18\n", " Key Matching  Page No.:   -  page no.: invoice id # 19\n", " Key Matching  Page No.:   -  page no.: invoice id number 23\n", " Key Matching  Page No.:   -  page no.: invoice id 21\n", " Key Matching  Page No.:   -  page no.: invoice id 21\n", " Key Matching  Page No.:   -  page no.: invoice # no 38\n", " Key Matching  Page No.:   -  page no.: invoice # no. 45\n", " Key Matching  Page No.:   -  page no.: invoice # id 19\n", " Key Matching  Page No.:   -  page no.: invoice # # 20\n", " Key Matching  Page No.:   -  page no.: invoice # number 24\n", " Key Matching  Page No.:   -  page no.: invoice # 22\n", " Key Matching  Page No.:   -  page no.: invoice # 22\n", " Key Matching  Page No.:   -  page no.: invoice no 42\n", " Key Matching  Page No.:   -  page no.: invoice no. 50\n", " Key Matching  Page No.:   -  page no.: invoice id 21\n", " Key Matching  Page No.:   -  page no.: invoice # 22\n", " Key Matching  Page No.:   -  page no.: invoice number 26\n", " Key Matching  Page No.:   -  page no.: invoice 25\n", " Key Matching  Page No.:   -  page no.: invoice 25\n", " Key Matching  Page No.:   -  page no.: invoice # no 38\n", " Key Matching  Page No.:   -  page no.: invoice # no. 45\n", " Key Matching  Page No.:   -  page no.: invoice # id 19\n", " Key Matching  Page No.:   -  page no.: invoice # # 20\n", " Key Matching  Page No.:   -  page no.: invoice # number 24\n", " Key Matching  Page No.:   -  page no.: invoice # 22\n", " Key Matching  Page No.:   -  page no.: invoice # 22\n", " Key Matching  Page No.:   -  page no.: no 36\n", " Key Matching  Page No.:   -  page no.: no. 50\n", " Key Matching  Page No.:   -  page no.: id 0\n", " Key Matching  Page No.:   -  page no.: # 0\n", " Key Matching  Page No.:   -  page no.: number 13\n", " Key Matching  Page No.:   -  page no.: invoice 25\n", " Key Matching  Page No.:   -  page no.: invoice 25\n", " Key Matching  Page No.:   -  page no.: invoice # 22\n", " Key Matching  Page No.:   -  page no.: invoice # 22\n", " Key Matching  Page No.:   -  page no.: invoice# 24\n", " Key Matching  Page No.:   -  page no.: invoice# 24\n", "Page No.:  ['2 of 2 ', 98.5] (0, None, None)\n", " Key Matching  Cashier No.:   -  cashier no.: ship to 53\n", " Key Matching  Cashier No.:   -  cashier no.: ship to 53\n", " Key Matching  Cashier No.:   -  cashier no.: ship-to 42\n", " Key Matching  Cashier No.:   -  cashier no.: ship-to 42\n", " Key Matching  Cashier No.:   -  cashier no.: bill to 32\n", " Key Matching  Cashier No.:   -  cashier no.: bill to 32\n", " Key Matching  Cashier No.:   -  cashier no.: bill to: department of technology services 22\n", " Key Matching  Cashier No.:   -  cashier no.: bill to: department of technology services 22\n", " Key Matching  Cashier No.:   -  cashier no.: bill to / facturé à: 25\n", " Key Matching  Cashier No.:   -  cashier no.: bill to / facturé à: 25\n", " Key Matching  Cashier No.:   -  cashier no.: bill-to 21\n", " Key Matching  Cashier No.:   -  cashier no.: bill-to 21\n", " Key Matching  Cashier No.:   -  cashier no.: terms 24\n", " Key Matching  Cashier No.:   -  cashier no.: terms 24\n", " Key Matching  Cashier No.:   -  cashier no.: issue 24\n", " Key Matching  Cashier No.:   -  cashier no.: issue date 27\n", " Key Matching  Cashier No.:   -  cashier no.: issue 24\n", " Key Matching  Cashier No.:   -  cashier no.: date 25\n", " Key Matching  Cashier No.:   -  cashier no.: invoice no 45\n", " Key Matching  Cashier No.:   -  cashier no.: invoice no. 52\n", " Key Matching  Cashier No.:   -  cashier no.: invoice id 27\n", " Key Matching  Cashier No.:   -  cashier no.: invoice # 29\n", " Key Matching  Cashier No.:   -  cashier no.: invoice number 31\n", " Key Matching  Cashier No.:   -  cashier no.: invoice 32\n", " Key Matching  Cashier No.:   -  cashier no.: invoice 32\n", " Key Matching  Cashier No.:   -  cashier no.: invoice: no 43\n", " Key Matching  Cashier No.:   -  cashier no.: invoice: no. 50\n", " Key Matching  Cashier No.:   -  cashier no.: invoice: id 35\n", " Key Matching  Cashier No.:   -  cashier no.: invoice: # 36\n", " Key Matching  Cashier No.:   -  cashier no.: invoice: number 30\n", " Key Matching  Cashier No.:   -  cashier no.: invoice: 40\n", " Key Matching  Cashier No.:   -  cashier no.: invoice: 40\n", " Key Matching  Cashier No.:   -  cashier no.: invoice id no 40\n", " Key Matching  Cashier No.:   -  cashier no.: invoice id no. 46\n", " Key Matching  Cashier No.:   -  cashier no.: invoice id id 24\n", " Key Matching  Cashier No.:   -  cashier no.: invoice id # 25\n", " Key Matching  Cashier No.:   -  cashier no.: invoice id number 28\n", " Key Matching  Cashier No.:   -  cashier no.: invoice id 27\n", " Key Matching  Cashier No.:   -  cashier no.: invoice id 27\n", " Key Matching  Cashier No.:   -  cashier no.: invoice # no 42\n", " Key Matching  Cashier No.:   -  cashier no.: invoice # no. 48\n", " Key Matching  Cashier No.:   -  cashier no.: invoice # id 25\n", " Key Matching  Cashier No.:   -  cashier no.: invoice # # 26\n", " Key Matching  Cashier No.:   -  cashier no.: invoice # number 29\n", " Key Matching  Cashier No.:   -  cashier no.: invoice # 29\n", " Key Matching  Cashier No.:   -  cashier no.: invoice # 29\n", " Key Matching  Cashier No.:   -  cashier no.: invoice no 45\n", " Key Matching  Cashier No.:   -  cashier no.: invoice no. 52\n", " Key Matching  Cashier No.:   -  cashier no.: invoice id 27\n", " Key Matching  Cashier No.:   -  cashier no.: invoice # 29\n", " Key Matching  Cashier No.:   -  cashier no.: invoice number 31\n", " Key Matching  Cashier No.:   -  cashier no.: invoice 32\n", " Key Matching  Cashier No.:   -  cashier no.: invoice 32\n", " Key Matching  Cashier No.:   -  cashier no.: invoice # no 42\n", " Key Matching  Cashier No.:   -  cashier no.: invoice # no. 48\n", " Key Matching  Cashier No.:   -  cashier no.: invoice # id 25\n", " Key Matching  Cashier No.:   -  cashier no.: invoice # # 26\n", " Key Matching  Cashier No.:   -  cashier no.: invoice # number 29\n", " Key Matching  Cashier No.:   -  cashier no.: invoice # 29\n", " Key Matching  Cashier No.:   -  cashier no.: invoice # 29\n", " Key Matching  Cashier No.:   -  cashier no.: no 29\n", " Key Matching  Cashier No.:   -  cashier no.: no. 40\n", " Key Matching  Cashier No.:   -  cashier no.: id 14\n", " Key Matching  Cashier No.:   -  cashier no.: # 0\n", " Key Matching  Cashier No.:   -  cashier no.: number 22\n", " Key Matching  Cashier No.:   -  cashier no.: invoice 32\n", " Key Matching  Cashier No.:   -  cashier no.: invoice 32\n", " Key Matching  Cashier No.:   -  cashier no.: invoice # 29\n", " Key Matching  Cashier No.:   -  cashier no.: invoice # 29\n", " Key Matching  Cashier No.:   -  cashier no.: invoice# 30\n", " Key Matching  Cashier No.:   -  cashier no.: invoice# 30\n", "Cashier No.:  ['137 ', 97.5] (0, None, None)\n", " Key Matching  Purchase Order:   -  purchase order: ship to 27\n", " Key Matching  Purchase Order:   -  purchase order: ship to 27\n", " Key Matching  Purchase Order:   -  purchase order: ship-to 18\n", " Key Matching  Purchase Order:   -  purchase order: ship-to 18\n", " Key Matching  Purchase Order:   -  purchase order: bill to 18\n", " Key Matching  Purchase Order:   -  purchase order: bill to 18\n", " Key Matching  Purchase Order:   -  purchase order: bill to: department of technology services 28\n", " Key Matching  Purchase Order:   -  purchase order: bill to: department of technology services 28\n", " Key Matching  Purchase Order:   -  purchase order: bill to / facturé à: 23\n", " Key Matching  Purchase Order:   -  purchase order: bill to / facturé à: 23\n", " Key Matching  Purchase Order:   -  purchase order: bill-to 9\n", " Key Matching  Purchase Order:   -  purchase order: bill-to 9\n", " Key Matching  Purchase Order:   -  purchase order: terms 20\n", " Key Matching  Purchase Order:   -  purchase order: terms 20\n", " Key Matching  Purchase Order:   -  purchase order: issue 20\n", " Key Matching  Purchase Order:   -  purchase order: issue date 40\n", " Key Matching  Purchase Order:   -  purchase order: issue 20\n", " Key Matching  Purchase Order:   -  purchase order: date 21\n", " Key Matching  Purchase Order:   -  purchase order: invoice no 32\n", " Key Matching  Purchase Order:   -  purchase order: invoice no. 31\n", " Key Matching  Purchase Order:   -  purchase order: invoice id 32\n", " Key Matching  Purchase Order:   -  purchase order: invoice # 25\n", " Key Matching  Purchase Order:   -  purchase order: invoice number 34\n", " Key Matching  Purchase Order:   -  purchase order: invoice 18\n", " Key Matching  Purchase Order:   -  purchase order: invoice 18\n", " Key Matching  Purchase Order:   -  purchase order: invoice: no 31\n", " Key Matching  Purchase Order:   -  purchase order: invoice: no. 30\n", " Key Matching  Purchase Order:   -  purchase order: invoice: id 31\n", " Key Matching  Purchase Order:   -  purchase order: invoice: # 24\n", " Key Matching  Purchase Order:   -  purchase order: invoice: number 33\n", " Key Matching  Purchase Order:   -  purchase order: invoice: 26\n", " Key Matching  Purchase Order:   -  purchase order: invoice: 26\n", " Key Matching  Purchase Order:   -  purchase order: invoice id no 29\n", " Key Matching  Purchase Order:   -  purchase order: invoice id no. 28\n", " Key Matching  Purchase Order:   -  purchase order: invoice id id 29\n", " Key Matching  Purchase Order:   -  purchase order: invoice id # 30\n", " Key Matching  Purchase Order:   -  purchase order: invoice id number 38\n", " Key Matching  Purchase Order:   -  purchase order: invoice id 32\n", " Key Matching  Purchase Order:   -  purchase order: invoice id 32\n", " Key Matching  Purchase Order:   -  purchase order: invoice # no 30\n", " Key Matching  Purchase Order:   -  purchase order: invoice # no. 29\n", " Key Matching  Purchase Order:   -  purchase order: invoice # id 30\n", " Key Matching  Purchase Order:   -  purchase order: invoice # # 23\n", " Key Matching  Purchase Order:   -  purchase order: invoice # number 32\n", " Key Matching  Purchase Order:   -  purchase order: invoice # 25\n", " Key Matching  Purchase Order:   -  purchase order: invoice # 25\n", " Key Matching  Purchase Order:   -  purchase order: invoice no 32\n", " Key Matching  Purchase Order:   -  purchase order: invoice no. 31\n", " Key Matching  Purchase Order:   -  purchase order: invoice id 32\n", " Key Matching  Purchase Order:   -  purchase order: invoice # 25\n", " Key Matching  Purchase Order:   -  purchase order: invoice number 34\n", " Key Matching  Purchase Order:   -  purchase order: invoice 18\n", " Key Matching  Purchase Order:   -  purchase order: invoice 18\n", " Key Matching  Purchase Order:   -  purchase order: invoice # no 30\n", " Key Matching  Purchase Order:   -  purchase order: invoice # no. 29\n", " Key Matching  Purchase Order:   -  purchase order: invoice # id 30\n", " Key Matching  Purchase Order:   -  purchase order: invoice # # 23\n", " Key Matching  Purchase Order:   -  purchase order: invoice # number 32\n", " Key Matching  Purchase Order:   -  purchase order: invoice # 25\n", " Key Matching  Purchase Order:   -  purchase order: invoice # 25\n", " Key Matching  Purchase Order:   -  purchase order: no 12\n", " Key Matching  Purchase Order:   -  purchase order: no. 11\n", " Key Matching  Purchase Order:   -  purchase order: id 12\n", " Key Matching  Purchase Order:   -  purchase order: # 0\n", " Key Matching  Purchase Order:   -  purchase order: number 29\n", " Key Matching  Purchase Order:   -  purchase order: invoice 18\n", " Key Matching  Purchase Order:   -  purchase order: invoice 18\n", " Key Matching  Purchase Order:   -  purchase order: invoice # 25\n", " Key Matching  Purchase Order:   -  purchase order: invoice # 25\n", " Key Matching  Purchase Order:   -  purchase order: invoice# 17\n", " Key Matching  Purchase Order:   -  purchase order: invoice# 17\n", "Purchase Order:  ['Rover ', 97.5] (0, None, None)\n", " Key Matching  A/R Number:   -  a/r number: ship to 11\n", " Key Matching  A/R Number:   -  a/r number: ship to 11\n", " Key Matching  A/R Number:   -  a/r number: ship-to 0\n", " Key Matching  A/R Number:   -  a/r number: ship-to 0\n", " Key Matching  A/R Number:   -  a/r number: bill to 11\n", " Key Matching  A/R Number:   -  a/r number: bill to 11\n", " Key Matching  A/R Number:   -  a/r number: bill to: department of technology services 23\n", " Key Matching  A/R Number:   -  a/r number: bill to: department of technology services 23\n", " Key Matching  A/R Number:   -  a/r number: bill to / facturé à: 32\n", " Key Matching  A/R Number:   -  a/r number: bill to / facturé à: 32\n", " Key Matching  A/R Number:   -  a/r number: bill-to 11\n", " Key Matching  A/R Number:   -  a/r number: bill-to 11\n", " Key Matching  A/R Number:   -  a/r number: terms 25\n", " Key Matching  A/R Number:   -  a/r number: terms 25\n", " Key Matching  A/R Number:   -  a/r number: issue 25\n", " Key Matching  A/R Number:   -  a/r number: issue date 19\n"]}, {"name": "stdout", "output_type": "stream", "text": [" Key Matching  A/R Number:   -  a/r number: issue 25\n", " Key Matching  A/R Number:   -  a/r number: date 27\n", " Key Matching  A/R Number:   -  a/r number: invoice no 19\n", " Key Matching  A/R Number:   -  a/r number: invoice no. 18\n", " Key Matching  A/R Number:   -  a/r number: invoice id 19\n", " Key Matching  A/R Number:   -  a/r number: invoice # 20\n", " Key Matching  A/R Number:   -  a/r number: invoice number 56\n", " Key Matching  A/R Number:   -  a/r number: invoice 22\n", " Key Matching  A/R Number:   -  a/r number: invoice 22\n", " Key Matching  A/R Number:   -  a/r number: invoice: no 27\n", " Key Matching  A/R Number:   -  a/r number: invoice: no. 26\n", " Key Matching  A/R Number:   -  a/r number: invoice: id 27\n", " Key Matching  A/R Number:   -  a/r number: invoice: # 29\n", " Key Matching  A/R Number:   -  a/r number: invoice: number 54\n", " Key Matching  A/R Number:   -  a/r number: invoice: 32\n", " Key Matching  A/R Number:   -  a/r number: invoice: 32\n", " Key Matching  A/R Number:   -  a/r number: invoice id no 17\n", " Key Matching  A/R Number:   -  a/r number: invoice id no. 16\n", " Key Matching  A/R Number:   -  a/r number: invoice id id 17\n", " Key Matching  A/R Number:   -  a/r number: invoice id # 17\n", " Key Matching  A/R Number:   -  a/r number: invoice id number 50\n", " Key Matching  A/R Number:   -  a/r number: invoice id 19\n", " Key Matching  A/R Number:   -  a/r number: invoice id 19\n", " Key Matching  A/R Number:   -  a/r number: invoice # no 17\n", " Key Matching  A/R Number:   -  a/r number: invoice # no. 17\n", " Key Matching  A/R Number:   -  a/r number: invoice # id 17\n", " Key Matching  A/R Number:   -  a/r number: invoice # # 18\n", " Key Matching  A/R Number:   -  a/r number: invoice # number 52\n", " Key Matching  A/R Number:   -  a/r number: invoice # 20\n", " Key Matching  A/R Number:   -  a/r number: invoice # 20\n", " Key Matching  A/R Number:   -  a/r number: invoice no 19\n", " Key Matching  A/R Number:   -  a/r number: invoice no. 18\n", " Key Matching  A/R Number:   -  a/r number: invoice id 19\n", " Key Matching  A/R Number:   -  a/r number: invoice # 20\n", " Key Matching  A/R Number:   -  a/r number: invoice number 56\n", " Key Matching  A/R Number:   -  a/r number: invoice 22\n", " Key Matching  A/R Number:   -  a/r number: invoice 22\n", " Key Matching  A/R Number:   -  a/r number: invoice # no 17\n", " Key Matching  A/R Number:   -  a/r number: invoice # no. 17\n", " Key Matching  A/R Number:   -  a/r number: invoice # id 17\n", " Key Matching  A/R Number:   -  a/r number: invoice # # 18\n", " Key Matching  A/R Number:   -  a/r number: invoice # number 52\n", " Key Matching  A/R Number:   -  a/r number: invoice # 20\n", " Key Matching  A/R Number:   -  a/r number: invoice # 20\n", " Key Matching  A/R Number:   -  a/r number: no 15\n", " Key Matching  A/R Number:   -  a/r number: no. 14\n", " Key Matching  A/R Number:   -  a/r number: id 0\n", " Key Matching  A/R Number:   -  a/r number: # 0\n", " Key Matching  A/R Number:   -  a/r number: number 71\n", " Key Matching  A/R Number:   -  a/r number: invoice 22\n", " Key Matching  A/R Number:   -  a/r number: invoice 22\n", " Key Matching  A/R Number:   -  a/r number: invoice # 20\n", " Key Matching  A/R Number:   -  a/r number: invoice # 20\n", " Key Matching  A/R Number:   -  a/r number: invoice# 21\n", " Key Matching  A/R Number:   -  a/r number: invoice# 21\n", "A/R Number:  ['648 ', 96.5] (0, None, None)\n", " Key Matching  Folio #:   -  folio #: ship to 27\n", " Key Matching  Folio #:   -  folio #: ship to 27\n", " Key Matching  Folio #:   -  folio #: ship-to 27\n", " Key Matching  Folio #:   -  folio #: ship-to 27\n", " Key Matching  Folio #:   -  folio #: bill to 27\n", " Key Matching  Folio #:   -  folio #: bill to 27\n", " Key Matching  Folio #:   -  folio #: bill to: department of technology services 20\n", " Key Matching  Folio #:   -  folio #: bill to: department of technology services 20\n", " Key Matching  Folio #:   -  folio #: bill to / facturé à: 29\n", " Key Matching  Folio #:   -  folio #: bill to / facturé à: 29\n", " Key Matching  Folio #:   -  folio #: bill-to 27\n", " Key Matching  Folio #:   -  folio #: bill-to 27\n", " Key Matching  Folio #:   -  folio #: terms 0\n", " Key Matching  Folio #:   -  folio #: terms 0\n", " Key Matching  Folio #:   -  folio #: issue 15\n", " Key Matching  Folio #:   -  folio #: issue date 22\n", " Key Matching  Folio #:   -  folio #: issue 15\n", " Key Matching  Folio #:   -  folio #: date 0\n", " Key Matching  Folio #:   -  folio #: invoice no 33\n", " Key Matching  Folio #:   -  folio #: invoice no. 32\n", " Key Matching  Folio #:   -  folio #: invoice id 33\n", " Key Matching  Folio #:   -  folio #: invoice # 47\n", " Key Matching  Folio #:   -  folio #: invoice number 27\n", " Key Matching  Folio #:   -  folio #: invoice 27\n", " Key Matching  Folio #:   -  folio #: invoice 27\n", " Key Matching  Folio #:   -  folio #: invoice: no 32\n", " Key Matching  Folio #:   -  folio #: invoice: no. 30\n", " Key Matching  Folio #:   -  folio #: invoice: id 32\n", " Key Matching  Folio #:   -  folio #: invoice: # 44\n", " Key Matching  Folio #:   -  folio #: invoice: number 26\n", " Key Matching  Folio #:   -  folio #: invoice: 38\n", " Key Matching  Folio #:   -  folio #: invoice: 38\n", " Key Matching  Folio #:   -  folio #: invoice id no 29\n", " Key Matching  Folio #:   -  folio #: invoice id no. 27\n", " Key Matching  Folio #:   -  folio #: invoice id id 29\n", " Key Matching  Folio #:   -  folio #: invoice id # 40\n", " Key Matching  Folio #:   -  folio #: invoice id number 24\n", " Key Matching  Folio #:   -  folio #: invoice id 33\n", " Key Matching  Folio #:   -  folio #: invoice id 33\n", " Key Matching  Folio #:   -  folio #: invoice # no 40\n", " Key Matching  Folio #:   -  folio #: invoice # no. 38\n", " Key Matching  Folio #:   -  folio #: invoice # id 40\n", " Key Matching  Folio #:   -  folio #: invoice # # 42\n", " Key Matching  Folio #:   -  folio #: invoice # number 33\n", " Key Matching  Folio #:   -  folio #: invoice # 47\n", " Key Matching  Folio #:   -  folio #: invoice # 47\n", " Key Matching  Folio #:   -  folio #: invoice no 33\n", " Key Matching  Folio #:   -  folio #: invoice no. 32\n", " Key Matching  Folio #:   -  folio #: invoice id 33\n", " Key Matching  Folio #:   -  folio #: invoice # 47\n", " Key Matching  Folio #:   -  folio #: invoice number 27\n", " Key Matching  Folio #:   -  folio #: invoice 27\n", " Key Matching  Folio #:   -  folio #: invoice 27\n", " Key Matching  Folio #:   -  folio #: invoice # no 40\n", " Key Matching  Folio #:   -  folio #: invoice # no. 38\n", " Key Matching  Folio #:   -  folio #: invoice # id 40\n", " Key Matching  Folio #:   -  folio #: invoice # # 42\n", " Key Matching  Folio #:   -  folio #: invoice # number 33\n", " Key Matching  Folio #:   -  folio #: invoice # 47\n", " Key Matching  Folio #:   -  folio #: invoice # 47\n", " Key Matching  Folio #:   -  folio #: no 20\n", " Key Matching  Folio #:   -  folio #: no. 18\n", " Key Matching  Folio #:   -  folio #: id 20\n", " Key Matching  Folio #:   -  folio #: # 22\n", " Key Matching  Folio #:   -  folio #: number 0\n", " Key Matching  Folio #:   -  folio #: invoice 27\n", " Key Matching  Folio #:   -  folio #: invoice 27\n", " Key Matching  Folio #:   -  folio #: invoice # 47\n", " Key Matching  Folio #:   -  folio #: invoice # 47\n", " Key Matching  Folio #:   -  folio #: invoice# 38\n", " Key Matching  Folio #:   -  folio #: invoice# 38\n", "Folio #:  ['226048 ', 96.5] (0, None, None)\n", " Key Matching  Room No.   -  room no. ship to 27\n", " Key Matching  Room No.   -  room no. ship to 27\n", " Key Matching  Room No.   -  room no. ship-to 13\n", " Key Matching  Room No.   -  room no. ship-to 13\n", " Key Matching  Room No.   -  room no. bill to 27\n", " Key Matching  Room No.   -  room no. bill to 27\n", " Key Matching  Room No.   -  room no. bill to: department of technology services 20\n", " Key Matching  Room No.   -  room no. bill to: department of technology services 20\n", " Key Matching  Room No.   -  room no. bill to / facturé à: 14\n", " Key Matching  Room No.   -  room no. bill to / facturé à: 14\n", " Key Matching  Room No.   -  room no. bill-to 13\n", " Key Matching  Room No.   -  room no. bill-to 13\n", " Key Matching  Room No.   -  room no. terms 31\n", " Key Matching  Room No.   -  room no. terms 31\n", " Key Matching  Room No.   -  room no. issue 0\n", " Key Matching  Room No.   -  room no. issue date 11\n", " Key Matching  Room No.   -  room no. issue 0\n", " Key Matching  Room No.   -  room no. date 0\n", " Key Matching  Room No.   -  room no. invoice no 44\n", " Key Matching  Room No.   -  room no. invoice no. 53\n", " Key Matching  Room No.   -  room no. invoice id 22\n", " Key Matching  Room No.   -  room no. invoice # 24\n", " Key Matching  Room No.   -  room no. invoice number 27\n", " Key Matching  Room No.   -  room no. invoice 27\n", " Key Matching  Room No.   -  room no. invoice 27\n", " Key Matching  Room No.   -  room no. invoice: no 42\n", " Key Matching  Room No.   -  room no. invoice: no. 50\n", " Key Matching  Room No.   -  room no. invoice: id 21\n", " Key Matching  Room No.   -  room no. invoice: # 22\n", " Key Matching  Room No.   -  room no. invoice: number 26\n", " Key Matching  Room No.   -  room no. invoice: 25\n", " Key Matching  Room No.   -  room no. invoice: 25\n", " Key Matching  Room No.   -  room no. invoice id no 38\n", " Key Matching  Room No.   -  room no. invoice id no. 45\n", " Key Matching  Room No.   -  room no. invoice id id 19\n", " Key Matching  Room No.   -  room no. invoice id # 20\n", " Key Matching  Room No.   -  room no. invoice id number 24\n", " Key Matching  Room No.   -  room no. invoice id 22\n", " Key Matching  Room No.   -  room no. invoice id 22\n", " Key Matching  Room No.   -  room no. invoice # no 40\n", " Key Matching  Room No.   -  room no. invoice # no. 48\n", " Key Matching  Room No.   -  room no. invoice # id 20\n", " Key Matching  Room No.   -  room no. invoice # # 21\n", " Key Matching  Room No.   -  room no. invoice # number 25\n", " Key Matching  Room No.   -  room no. invoice # 24\n", " Key Matching  Room No.   -  room no. invoice # 24\n", " Key Matching  Room No.   -  room no. invoice no 44\n", " Key Matching  Room No.   -  room no. invoice no. 53\n", " Key Matching  Room No.   -  room no. invoice id 22\n", " Key Matching  Room No.   -  room no. invoice # 24\n", " Key Matching  Room No.   -  room no. invoice number 27\n", " Key Matching  Room No.   -  room no. invoice 27\n", " Key Matching  Room No.   -  room no. invoice 27\n", " Key Matching  Room No.   -  room no. invoice # no 40\n", " Key Matching  Room No.   -  room no. invoice # no. 48\n", " Key Matching  Room No.   -  room no. invoice # id 20\n", " Key Matching  Room No.   -  room no. invoice # # 21\n", " Key Matching  Room No.   -  room no. invoice # number 25\n", " Key Matching  Room No.   -  room no. invoice # 24\n", " Key Matching  Room No.   -  room no. invoice # 24\n", " Key Matching  Room No.   -  room no. no 40\n", " Key Matching  Room No.   -  room no. no. 55\n", " Key Matching  Room No.   -  room no. id 0\n", " Key Matching  Room No.   -  room no. # 0\n", " Key Matching  Room No.   -  room no. number 14\n", " Key Matching  Room No.   -  room no. invoice 27\n", " Key Matching  Room No.   -  room no. invoice 27\n", " Key Matching  Room No.   -  room no. invoice # 24\n", " Key Matching  Room No.   -  room no. invoice # 24\n", " Key Matching  Room No.   -  room no. invoice# 25\n", " Key Matching  Room No.   -  room no. invoice# 25\n", "Room No.  ['301 ', 94.5] (0, None, None)\n", " Key Matching  Departure   -  departure ship to 25\n", " Key Matching  Departure   -  departure ship to 25\n", " Key Matching  Departure   -  departure ship-to 25\n", " Key Matching  Departure   -  departure ship-to 25\n", " Key Matching  Departure   -  departure bill to 12\n", " Key Matching  Departure   -  departure bill to 12\n", " Key Matching  Departure   -  departure bill to: department of technology services 31\n", " Key Matching  Departure   -  departure bill to: department of technology services 31\n", " Key Matching  Departure   -  departure bill to / facturé à: 28\n", " Key Matching  Departure   -  departure bill to / facturé à: 28\n", " Key Matching  Departure   -  departure bill-to 12\n", " Key Matching  Departure   -  departure bill-to 12\n", " Key Matching  Departure   -  departure terms 29\n", " Key Matching  Departure   -  departure terms 29\n", " Key Matching  Departure   -  departure issue 29\n", " Key Matching  Departure   -  departure issue date 42\n", " Key Matching  Departure   -  departure issue 29\n", " Key Matching  Departure   -  departure date 62\n", " Key Matching  Departure   -  departure invoice no 11\n", " Key Matching  Departure   -  departure invoice no. 10\n", " Key Matching  Departure   -  departure invoice id 11\n", " Key Matching  Departure   -  departure invoice # 11\n", " Key Matching  Departure   -  departure invoice number 26\n", " Key Matching  Departure   -  departure invoice 12\n", " Key Matching  Departure   -  departure invoice 12\n", " Key Matching  Departure   -  departure invoice: no 10\n", " Key Matching  Departure   -  departure invoice: no. 10\n", " Key Matching  Departure   -  departure invoice: id 10\n", " Key Matching  Departure   -  departure invoice: # 11\n", " Key Matching  Departure   -  departure invoice: number 25\n", " Key Matching  Departure   -  departure invoice: 12\n", " Key Matching  Departure   -  departure invoice: 12\n", " Key Matching  Departure   -  departure invoice id no 9\n", " Key Matching  Departure   -  departure invoice id no. 9\n", " Key Matching  Departure   -  departure invoice id id 9\n", " Key Matching  Departure   -  departure invoice id # 10\n", " Key Matching  Departure   -  departure invoice id number 23\n", " Key Matching  Departure   -  departure invoice id 11\n", " Key Matching  Departure   -  departure invoice id 11\n", " Key Matching  Departure   -  departure invoice # no 10\n", " Key Matching  Departure   -  departure invoice # no. 9\n", " Key Matching  Departure   -  departure invoice # id 10\n", " Key Matching  Departure   -  departure invoice # # 10\n", " Key Matching  Departure   -  departure invoice # number 24\n", " Key Matching  Departure   -  departure invoice # 11\n", " Key Matching  Departure   -  departure invoice # 11\n", " Key Matching  Departure   -  departure invoice no 11\n", " Key Matching  Departure   -  departure invoice no. 10\n", " Key Matching  Departure   -  departure invoice id 11\n", " Key Matching  Departure   -  departure invoice # 11\n", " Key Matching  Departure   -  departure invoice number 26\n", " Key Matching  Departure   -  departure invoice 12\n", " Key Matching  Departure   -  departure invoice 12\n", " Key Matching  Departure   -  departure invoice # no 10\n", " Key Matching  Departure   -  departure invoice # no. 9\n", " Key Matching  Departure   -  departure invoice # id 10\n", " Key Matching  Departure   -  departure invoice # # 10\n", " Key Matching  Departure   -  departure invoice # number 24\n", " Key Matching  Departure   -  departure invoice # 11\n", " Key Matching  Departure   -  departure invoice # 11\n", " Key Matching  Departure   -  departure no 0\n", " Key Matching  Departure   -  departure no. 0\n", " Key Matching  Departure   -  departure id 18\n", " Key Matching  Departure   -  departure # 0\n", " Key Matching  Departure   -  departure number 27\n", " Key Matching  Departure   -  departure invoice 12\n", " Key Matching  Departure   -  departure invoice 12\n", " Key Matching  Departure   -  departure invoice # 11\n", " Key Matching  Departure   -  departure invoice # 11\n", " Key Matching  Departure   -  departure invoice# 12\n", " Key Matching  Departure   -  departure invoice# 12\n", "Departure  ['08-31-22 ', 94.5] (0, None, None)\n", " Key Matching  Arrival   -  arrival ship to 14\n", " Key Matching  Arrival   -  arrival ship to 14\n", " Key Matching  Arrival   -  arrival ship-to 14\n", " Key Matching  Arrival   -  arrival ship-to 14\n", " Key Matching  Arrival   -  arrival bill to 29\n", " Key Matching  Arrival   -  arrival bill to 29\n", " Key Matching  Arrival   -  arrival bill to: department of technology services 16\n", " Key Matching  Arrival   -  arrival bill to: department of technology services 16\n", " Key Matching  Arrival   -  arrival bill to / facturé à: 15\n", " Key Matching  Arrival   -  arrival bill to / facturé à: 15\n", " Key Matching  Arrival   -  arrival bill-to 29\n", " Key Matching  Arrival   -  arrival bill-to 29\n", " Key Matching  Arrival   -  arrival terms 17\n", " Key Matching  Arrival   -  arrival terms 17\n", " Key Matching  Arrival   -  arrival issue 17\n", " Key Matching  Arrival   -  arrival issue date 24\n", " Key Matching  Arrival   -  arrival issue 17\n", " Key Matching  Arrival   -  arrival date 18\n", " Key Matching  Arrival   -  arrival invoice no 24\n", " Key Matching  Arrival   -  arrival invoice no. 22\n", " Key Matching  Arrival   -  arrival invoice id 24\n", " Key Matching  Arrival   -  arrival invoice # 25\n", " Key Matching  Arrival   -  arrival invoice number 19\n", " Key Matching  Arrival   -  arrival invoice 29\n", " Key Matching  Arrival   -  arrival invoice 29\n", " Key Matching  Arrival   -  arrival invoice: no 22\n", " Key Matching  Arrival   -  arrival invoice: no. 21\n", " Key Matching  Arrival   -  arrival invoice: id 22\n", " Key Matching  Arrival   -  arrival invoice: # 24\n", " Key Matching  Arrival   -  arrival invoice: number 18\n", " Key Matching  Arrival   -  arrival invoice: 27\n", " Key Matching  Arrival   -  arrival invoice: 27\n", " Key Matching  Arrival   -  arrival invoice id no 20\n", " Key Matching  Arrival   -  arrival invoice id no. 19\n", " Key Matching  Arrival   -  arrival invoice id id 20\n", " Key Matching  Arrival   -  arrival invoice id # 21\n", " Key Matching  Arrival   -  arrival invoice id number 17\n", " Key Matching  Arrival   -  arrival invoice id 24\n", " Key Matching  Arrival   -  arrival invoice id 24\n", " Key Matching  Arrival   -  arrival invoice # no 21\n", " Key Matching  Arrival   -  arrival invoice # no. 20\n", " Key Matching  Arrival   -  arrival invoice # id 21\n", " Key Matching  Arrival   -  arrival invoice # # 22\n", " Key Matching  Arrival   -  arrival invoice # number 17\n", " Key Matching  Arrival   -  arrival invoice # 25\n", " Key Matching  Arrival   -  arrival invoice # 25\n", " Key Matching  Arrival   -  arrival invoice no 24\n", " Key Matching  Arrival   -  arrival invoice no. 22\n", " Key Matching  Arrival   -  arrival invoice id 24\n", " Key Matching  Arrival   -  arrival invoice # 25\n", " Key Matching  Arrival   -  arrival invoice number 19\n", " Key Matching  Arrival   -  arrival invoice 29\n", " Key Matching  Arrival   -  arrival invoice 29\n", " Key Matching  Arrival   -  arrival invoice # no 21\n", " Key Matching  Arrival   -  arrival invoice # no. 20\n", " Key Matching  Arrival   -  arrival invoice # id 21\n", " Key Matching  Arrival   -  arrival invoice # # 22\n", " Key Matching  Arrival   -  arrival invoice # number 17\n", " Key Matching  Arrival   -  arrival invoice # 25\n", " Key Matching  Arrival   -  arrival invoice # 25\n", " Key Matching  Arrival   -  arrival no 0\n", " Key Matching  Arrival   -  arrival no. 0\n", " Key Matching  Arrival   -  arrival id 22\n", " Key Matching  Arrival   -  arrival # 0\n", " Key Matching  Arrival   -  arrival number 15\n", " Key Matching  Arrival   -  arrival invoice 29\n", " Key Matching  Arrival   -  arrival invoice 29\n", " Key Matching  Arrival   -  arrival invoice # 25\n", " Key Matching  Arrival   -  arrival invoice # 25\n", " Key Matching  Arrival   -  arrival invoice# 27\n", " Key Matching  Arrival   -  arrival invoice# 27\n", "Arrival  ['06-16-22 ', 93.5] (0, None, None)\n", " Key Matching  Balance:   -  balance: ship to 0\n", " Key Matching  Balance:   -  balance: ship to 0\n", " Key Matching  Balance:   -  balance: ship-to 0\n", " Key Matching  Balance:   -  balance: ship-to 0\n", " Key Matching  Balance:   -  balance: bill to 27\n", " Key Matching  Balance:   -  balance: bill to 27\n", " Key Matching  Balance:   -  balance: bill to: department of technology services 24\n", " Key Matching  Balance:   -  balance: bill to: department of technology services 24\n", " Key Matching  Balance:   -  balance: bill to / facturé à: 36\n", " Key Matching  Balance:   -  balance: bill to / facturé à: 36\n", " Key Matching  Balance:   -  balance: bill-to 27\n", " Key Matching  Balance:   -  balance: bill-to 27\n", " Key Matching  Balance:   -  balance: terms 15\n", " Key Matching  Balance:   -  balance: terms 15\n", " Key Matching  Balance:   -  balance: issue 15\n", " Key Matching  Balance:   -  balance: issue date 22\n", " Key Matching  Balance:   -  balance: issue 15\n", " Key Matching  Balance:   -  balance: date 33\n", " Key Matching  Balance:   -  balance: invoice no 33\n", " Key Matching  Balance:   -  balance: invoice no. 32\n", " Key Matching  Balance:   -  balance: invoice id 33\n", " Key Matching  Balance:   -  balance: invoice # 35\n", " Key Matching  Balance:   -  balance: invoice number 27\n", " Key Matching  Balance:   -  balance: invoice 40\n", " Key Matching  Balance:   -  balance: invoice 40\n", " Key Matching  Balance:   -  balance: invoice: no 42\n", " Key Matching  Balance:   -  balance: invoice: no. 40\n", " Key Matching  Balance:   -  balance: invoice: id 42\n", " Key Matching  Balance:   -  balance: invoice: # 44\n", " Key Matching  Balance:   -  balance: invoice: number 35\n", " Key Matching  Balance:   -  balance: invoice: 50\n", " Key Matching  Balance:   -  balance: invoice: 50\n", " Key Matching  Balance:   -  balance: invoice id no 29\n", " Key Matching  Balance:   -  balance: invoice id no. 27\n", " Key Matching  Balance:   -  balance: invoice id id 29\n", " Key Matching  Balance:   -  balance: invoice id # 30\n", " Key Matching  Balance:   -  balance: invoice id number 24\n", " Key Matching  Balance:   -  balance: invoice id 33\n", " Key Matching  Balance:   -  balance: invoice id 33\n", " Key Matching  Balance:   -  balance: invoice # no 30\n", " Key Matching  Balance:   -  balance: invoice # no. 29\n", " Key Matching  Balance:   -  balance: invoice # id 30\n", " Key Matching  Balance:   -  balance: invoice # # 32\n", " Key Matching  Balance:   -  balance: invoice # number 25\n", " Key Matching  Balance:   -  balance: invoice # 35\n", " Key Matching  Balance:   -  balance: invoice # 35\n", " Key Matching  Balance:   -  balance: invoice no 33\n", " Key Matching  Balance:   -  balance: invoice no. 32\n", " Key Matching  Balance:   -  balance: invoice id 33\n", " Key Matching  Balance:   -  balance: invoice # 35\n", " Key Matching  Balance:   -  balance: invoice number 27\n", " Key Matching  Balance:   -  balance: invoice 40\n", " Key Matching  Balance:   -  balance: invoice 40\n", " Key Matching  Balance:   -  balance: invoice # no 30\n", " Key Matching  Balance:   -  balance: invoice # no. 29\n", " Key Matching  Balance:   -  balance: invoice # id 30\n", " Key Matching  Balance:   -  balance: invoice # # 32\n", " Key Matching  Balance:   -  balance: invoice # number 25\n", " Key Matching  Balance:   -  balance: invoice # 35\n", " Key Matching  Balance:   -  balance: invoice # 35\n", " Key Matching  Balance:   -  balance: no 20\n", " Key Matching  Balance:   -  balance: no. 18\n", " Key Matching  Balance:   -  balance: id 0\n", " Key Matching  Balance:   -  balance: # 0\n", " Key Matching  Balance:   -  balance: number 29\n", " Key Matching  Balance:   -  balance: invoice 40\n", " Key Matching  Balance:   -  balance: invoice 40\n", " Key Matching  Balance:   -  balance: invoice # 35\n", " Key Matching  Balance:   -  balance: invoice # 35\n", " Key Matching  Balance:   -  balance: invoice# 38\n", " Key Matching  Balance:   -  balance: invoice# 38\n", "Balance:  ['$ 691.20 ', 63.5] (0, None, None)\n", " Key Matching  Total:   -  total: ship to 31\n", " Key Matching  Total:   -  total: ship to 31\n", " Key Matching  Total:   -  total: ship-to 31\n", " Key Matching  Total:   -  total: ship-to 31\n", " Key Matching  Total:   -  total: bill to 31\n", " Key Matching  Total:   -  total: bill to 31\n", " Key Matching  Total:   -  total: bill to: department of technology services 17\n", " Key Matching  Total:   -  total: bill to: department of technology services 17\n", " Key Matching  Total:   -  total: bill to / facturé à: 31\n", " Key Matching  Total:   -  total: bill to / facturé à: 31\n", " Key Matching  Total:   -  total: bill-to 31\n", " Key Matching  Total:   -  total: bill-to 31\n", " Key Matching  Total:   -  total: terms 18\n", " Key Matching  Total:   -  total: terms 18\n", " Key Matching  Total:   -  total: issue 0\n", " Key Matching  Total:   -  total: issue date 12\n", " Key Matching  Total:   -  total: issue 0\n", " Key Matching  Total:   -  total: date 20\n", " Key Matching  Total:   -  total: invoice no 12\n", " Key Matching  Total:   -  total: invoice no. 12\n", " Key Matching  Total:   -  total: invoice id 12\n", " Key Matching  Total:   -  total: invoice # 13\n", " Key Matching  Total:   -  total: invoice number 10\n", " Key Matching  Total:   -  total: invoice 15\n", " Key Matching  Total:   -  total: invoice 15\n", " Key Matching  Total:   -  total: invoice: no 24\n", " Key Matching  Total:   -  total: invoice: no. 22\n", " Key Matching  Total:   -  total: invoice: id 24\n", " Key Matching  Total:   -  total: invoice: # 25\n", " Key Matching  Total:   -  total: invoice: number 19\n", " Key Matching  Total:   -  total: invoice: 29\n", " Key Matching  Total:   -  total: invoice: 29\n", " Key Matching  Total:   -  total: invoice id no 11\n", " Key Matching  Total:   -  total: invoice id no. 10\n", " Key Matching  Total:   -  total: invoice id id 11\n", " Key Matching  Total:   -  total: invoice id # 11\n", " Key Matching  Total:   -  total: invoice id number 9\n", " Key Matching  Total:   -  total: invoice id 12\n", " Key Matching  Total:   -  total: invoice id 12\n", " Key Matching  Total:   -  total: invoice # no 11\n", " Key Matching  Total:   -  total: invoice # no. 11\n", " Key Matching  Total:   -  total: invoice # id 11\n", " Key Matching  Total:   -  total: invoice # # 12\n", " Key Matching  Total:   -  total: invoice # number 9\n", " Key Matching  Total:   -  total: invoice # 13\n", " Key Matching  Total:   -  total: invoice # 13\n", " Key Matching  Total:   -  total: invoice no 12\n", " Key Matching  Total:   -  total: invoice no. 12\n", " Key Matching  Total:   -  total: invoice id 12\n", " Key Matching  Total:   -  total: invoice # 13\n", " Key Matching  Total:   -  total: invoice number 10\n", " Key Matching  Total:   -  total: invoice 15\n", " Key Matching  Total:   -  total: invoice 15\n", " Key Matching  Total:   -  total: invoice # no 11\n", " Key Matching  Total:   -  total: invoice # no. 11\n"]}, {"name": "stdout", "output_type": "stream", "text": [" Key Matching  Total:   -  total: invoice # id 11\n", " Key Matching  Total:   -  total: invoice # # 12\n", " Key Matching  Total:   -  total: invoice # number 9\n", " Key Matching  Total:   -  total: invoice # 13\n", " Key Matching  Total:   -  total: invoice # 13\n", " Key Matching  Total:   -  total: no 25\n", " Key Matching  Total:   -  total: no. 22\n", " Key Matching  Total:   -  total: id 0\n", " Key Matching  Total:   -  total: # 0\n", " Key Matching  Total:   -  total: number 0\n", " Key Matching  Total:   -  total: invoice 15\n", " Key Matching  Total:   -  total: invoice 15\n", " Key Matching  Total:   -  total: invoice # 13\n", " Key Matching  Total:   -  total: invoice # 13\n", " Key Matching  Total:   -  total: invoice# 14\n", " Key Matching  Total:   -  total: invoice# 14\n", "Total:  ['$ 691.20 ', 23.5] (0, None, None)\n", " Key Matching  FAX   -  fax ship to 0\n", " Key Matching  FAX   -  fax ship to 0\n", " Key Matching  FAX   -  fax ship-to 0\n", " Key Matching  FAX   -  fax ship-to 0\n", " Key Matching  FAX   -  fax bill to 0\n", " Key Matching  FAX   -  fax bill to 0\n", " Key Matching  FAX   -  fax bill to: department of technology services 4\n", " Key Matching  FAX   -  fax bill to: department of technology services 4\n", " Key Matching  FAX   -  fax bill to / facturé à: 17\n", " Key Matching  FAX   -  fax bill to / facturé à: 17\n", " Key Matching  FAX   -  fax bill-to 0\n", " Key Matching  FAX   -  fax bill-to 0\n", " Key Matching  FAX   -  fax terms 0\n", " Key Matching  FAX   -  fax terms 0\n", " Key Matching  FAX   -  fax issue 0\n", " Key Matching  FAX   -  fax issue date 15\n", " Key Matching  FAX   -  fax issue 0\n", " Key Matching  FAX   -  fax date 29\n", " Key Matching  FAX   -  fax invoice no 0\n", " Key Matching  FAX   -  fax invoice no. 0\n", " Key Matching  FAX   -  fax invoice id 0\n", " Key Matching  FAX   -  fax invoice # 0\n", " Key Matching  FAX   -  fax invoice number 0\n", " Key Matching  FAX   -  fax invoice 0\n", " Key Matching  FAX   -  fax invoice 0\n", " Key Matching  FAX   -  fax invoice: no 0\n", " Key Matching  FAX   -  fax invoice: no. 0\n", " Key Matching  FAX   -  fax invoice: id 0\n", " Key Matching  FAX   -  fax invoice: # 0\n", " Key Matching  FAX   -  fax invoice: number 0\n", " Key Matching  FAX   -  fax invoice: 0\n", " Key Matching  FAX   -  fax invoice: 0\n", " Key Matching  FAX   -  fax invoice id no 0\n", " Key Matching  FAX   -  fax invoice id no. 0\n", " Key Matching  FAX   -  fax invoice id id 0\n", " Key Matching  FAX   -  fax invoice id # 0\n", " Key Matching  FAX   -  fax invoice id number 0\n", " Key Matching  FAX   -  fax invoice id 0\n", " Key Matching  FAX   -  fax invoice id 0\n", " Key Matching  FAX   -  fax invoice # no 0\n", " Key Matching  FAX   -  fax invoice # no. 0\n", " Key Matching  FAX   -  fax invoice # id 0\n", " Key Matching  FAX   -  fax invoice # # 0\n", " Key Matching  FAX   -  fax invoice # number 0\n", " Key Matching  FAX   -  fax invoice # 0\n", " Key Matching  FAX   -  fax invoice # 0\n", " Key Matching  FAX   -  fax invoice no 0\n", " Key Matching  FAX   -  fax invoice no. 0\n", " Key Matching  FAX   -  fax invoice id 0\n", " Key Matching  FAX   -  fax invoice # 0\n", " Key Matching  FAX   -  fax invoice number 0\n", " Key Matching  FAX   -  fax invoice 0\n", " Key Matching  FAX   -  fax invoice 0\n", " Key Matching  FAX   -  fax invoice # no 0\n", " Key Matching  FAX   -  fax invoice # no. 0\n", " Key Matching  FAX   -  fax invoice # id 0\n", " Key Matching  FAX   -  fax invoice # # 0\n", " Key Matching  FAX   -  fax invoice # number 0\n", " Key Matching  FAX   -  fax invoice # 0\n", " Key Matching  FAX   -  fax invoice # 0\n", " Key Matching  FAX   -  fax no 0\n", " Key Matching  FAX   -  fax no. 0\n", " Key Matching  FAX   -  fax id 0\n", " Key Matching  FAX   -  fax # 0\n", " Key Matching  FAX   -  fax number 0\n", " Key Matching  FAX   -  fax invoice 0\n", " Key Matching  FAX   -  fax invoice 0\n", " Key Matching  FAX   -  fax invoice # 0\n", " Key Matching  FAX   -  fax invoice # 0\n", " Key Matching  FAX   -  fax invoice# 0\n", " Key Matching  FAX   -  fax invoice# 0\n", "FAX  ['************ ', 19.0] (0, None, None)\n"]}], "source": ["for key, value in raw_dict.items():\n", "    \n", "    #print(key,' -- ',value)\n", "    #checking duplicate for extracted_field\n", "    #print(\"Match Field  checking \",key)\n", "\n", "    check_res=get_field_label2(key,w_list,match_threshold) \n", "    print(key,value,check_res)\n", "    key=key.replace(\":\",\"\").replace(\",\",\"\")\n", "    search_key=None\n", "    if check_res[1]==None:\n", "        data={key.strip():value[0].strip(),\"confidence_level\":value[1]}\n", "        search_key=key.strip()\n", "    else:\n", "        data={check_res[1].strip():value[0].strip(),\"confidence_level\":value[1]}\n", "        search_key=check_res[1].strip()\n", "    \n", "    #print(data)\n", "                    \n", "            \n"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": [" def get_field_label2(field_name,w_list,match_threshold):\n", "\n", "\n", "        match_ratio_word=None\n", "        highiest=0\n", "        item=field_name\n", "        f_name=None\n", "\n", "        #print(\"length of word list \",len(w_list),\" Key \",field_name)\n", "\n", "        for list_item in w_list:\n", "            word=list_item[0]\n", "            name=list_item[1]\n", "            \n", "            if word is None or name is None:\n", "                continue \n", "            \n", "            try:\n", "                match_ratio = fuzz.ratio(item.lower().strip(), word.lower().strip())\n", "                jaro_distance= distance.get_jaro_distance(item.lower().strip(), word.lower().strip())\n", "                \n", "            except:\n", "                continue\n", "            #print(\" Key Matching \",item[0].lower().strip(), word.lower().strip(),match_ratio)\n", "            #or (jaro_distance*100)>80)\n", "            #if (match_ratio > 95  and match_ratio>highiest):\n", "            print(\" Key Matching \",item ,' - ',item.lower().strip(), word.lower().strip(),match_ratio)\n", "            if (match_ratio>match_threshold  and match_ratio>highiest):\n", "                #print(\" Key Matching \",item ,' : ',item[0].lower().strip(), word.lower().strip(),match_ratio)\n", "\n", "                highiest=match_ratio\n", "                match_ratio_word=item\n", "                f_name=name\n", "        \n", "        #print(\"Field_match\",highiest,f_name,match_ratio_word)\n", "\n", "        return (highiest,f_name,match_ratio_word)\n", "\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/plain": ["<re.Match object; span=(0, 25), match='Capital Analytics (16819)'>"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["word_list = []\n", "f_details=field_details\n", "for h in f_details[\"head\"]:\n", "    for t in f_details[\"tail\"]:\n", "        word_list.append(h.strip()+\" \"+t.strip())\n", "\n", "print(\"word list \")\n", "print(word_list)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["match_ratio_word=None\n", "highiest=0\n", "for word in word_list:\n", "\n", "    for item in text_cordinates:\n", "        match_ratio = fuzz.ratio(item[0].lower().strip(), word.lower().strip())\n", "        jaro_distance= distance.get_jaro_distance(item[0].lower().strip(), word.lower().strip())\n", "        print(\"-->\",item[0].lower().strip(), word.lower().strip(),match_ratio)\n", "        if (match_ratio>85 or (jaro_distance*100)>85) and match_ratio>highiest:\n", "            highiest=match_ratio\n", "            match_ratio_word=item\n", "\n", "print(\"Best Match Found \",match_ratio_word)"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["0.19773030120538143 >0.15"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doc=nlp(\"T.D. Williamson Canada ULC\")\n", "doc.ents"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## paragraph detection "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import cv2\n", "import numpy as np\n", "\n", "# Load image, grayscale, Gaussian blur, <PERSON><PERSON>'s threshold\n", "img_path=\"/home/<USER>/invoice/invoice-flow/inv-flow-dvc/db-repo/300bba72-b844-4e53-85c9-79bbd2b44b53_0.png\"\n", "image = cv2.imread(img_path)\n", "gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)\n", "blur = cv2.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(gray, (7,7), 0)\n", "thresh = cv2.threshold(blur, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)[1]\n", "\n", "# Create rectangular structuring element and dilate\n", "kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (9,9))\n", "dilate = cv2.dilate(thresh, kernel, iterations=4)\n", "\n", "# Find contours and draw rectangle\n", "cnts = cv2.findContours(dilate, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)\n", "cnts = cnts[0] if len(cnts) == 2 else cnts[1]\n", "for c in cnts:\n", "    x,y,w,h = cv2.boundingRect(c)\n", "    cv2.rectangle(image, (x, y), (x + w, y + h), (36,255,12), 2)\n", "\n", "#cv2.imshow('thresh', thresh)\n", "#cv2.imshow('dilate', dilate)\n", "#cv2.imshow('image', image)\n", "cv2.imwrite(\"../result.png\",image)\n", "print(\"written\")\n", "#cv2.wait<PERSON><PERSON>()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<h1>Blocks Of Text </h1>\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#open cv to find countour\n", "\n", "file_name = '/home/<USER>/invoice/invoice-flow/inv-flow-dvc/db-repo/one.png'\n", "import cv2 \n", "#import pytesseract \n", "  \n", "# Mention the installed location of Tesseract-OCR in your system \n", "#pytesseract.pytesseract.tesseract_cmd = 'System_path_to_tesseract.exe'\n", "  \n", "# Read image from which text needs to be extracted \n", "img = cv2.imread(file_name) \n", "  \n", "# Preprocessing the image starts \n", "  \n", "# Convert the image to gray scale \n", "gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY) \n", "  \n", "# Performing OTSU threshold \n", "ret, thresh1 = cv2.threshold(gray, 0, 255, cv2.THRESH_OTSU | cv2.THRESH_BINARY_INV) \n", "  \n", "# Specify structure shape and kernel size.  \n", "# Kernel size increases or decreases the area  \n", "# of the rectangle to be detected. \n", "# A smaller value like (10, 10) will detect  \n", "# each word instead of a sentence. \n", "rect_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (300, 300)) \n", "  \n", "# Appplying dilation on the threshold image \n", "dilation = cv2.dilate(thresh1, rect_kernel, iterations = 1) \n", "  \n", "# Finding contours \n", "#contours, hierarchy = cv2.findContours(dilation, cv2.RETR_EXTERNAL,  \n", "#                                                 cv2.CHAIN_APPROX_NONE) \n", "  \n", "contours,hierarchy = cv2.findContours(thresh1,cv2.RETR_EXTERNAL,cv2.CHAIN_APPROX_SIMPLE )\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for c in contours:\n", "    rect = cv2.boundingRect(c)\n", "    #if rect[2] > 500 or rect[3] > 500 : continue\n", "\n", "    #print (cv2.contourArea(c))\n", "    x,y,w,h = rect\n", "    cv2.rectangle(img,(x-20,y-20),(x+w+20,y+h+20),(0,0,255),-1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cv2.imwrite(\"../result.jpg\",img)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import cv2\n", "import numpy as np\n", "\n", "# Load image, grayscale, Gaussian blur, <PERSON><PERSON>'s threshold\n", "image = cv2.imread(\"../result.jpg\")\n", "gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)\n", "blur = cv2.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(gray, (7,7), 0)\n", "thresh = cv2.threshold(blur, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)[1]\n", "\n", "# Create rectangular structuring element and dilate\n", "kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (5,5))\n", "dilate = cv2.dilate(thresh, kernel, iterations=4)\n", "\n", "# Find contours and draw rectangle\n", "cnts = cv2.findContours(dilate, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)\n", "cnts = cnts[0] if len(cnts) == 2 else cnts[1]\n", "print(len(cnts))\n", "for c in cnts:\n", "    x,y,w,h = cv2.boundingRect(c)\n", "    cv2.rectangle(image, (x, y), (x + w, y + h), (255,255,0), 5)\n", "\n", "cv2.imwrite(\"../result2.jpg\",img)\n", "print(\"done\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Django Shell-Plus", "language": "python", "name": "django_extensions"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.4"}}, "nbformat": 4, "nbformat_minor": 2}