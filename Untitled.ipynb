{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["csv_file_path=\"/home/<USER>/invoice/invoice-flow/checkout1/apautomation/Invoice/pdf/2327b538-d3f8-45b4-a212-587a61bb5a67invoice-csv/2.csv\"\n", "json_path=\"/home/<USER>/invoice/invoice-flow/checkout1/apautomation/Invoice/pdf/2327b538-d3f8-45b4-a212-587a61bb5a67invoice.json\"\n", "\n", "#from Invoice.ExtractUtils import * \n", "#extract_util_obj = ExtractUtils()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os \n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from Invoice.FieldSearch import *\n", "fieldsearch=FieldSearch()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mapping_data = \"\"\n", "table_dimensions=None\n", "unique_filename=\"2327b538-d3f8-45b4-a212-587a61bb5a67\"\n", "mapping_file=BASE_DIR+'/Invoice/pdf/'+unique_filename+'mapping.json'\n", "#csv_file_path=BASE_DIR +'/Invoice/pdf/'+unique_filename+'invoice-csv/'+csv_file_path\n", "with open(mapping_file) as f:\n", "    mapping_data = json.load(f)\n", "    #print(mapping_data)\n", "    #mapping_data[csv_file_path]\n", "    table_dimensions = mapping_data[csv_file_path]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["invoice_no={}\n", "invoice_no[\"name\"]=\"Invoice_No\"\n", "invoice_no[\"type\"]=\"Alpha\"\n", "invoice_no[\"strategy\"]=\"fuzz\"\n", "invoice_no[\"head\"]=[\"billing invoice\",\"billing\",\"invoice\"]\n", "invoice_no[\"tail\"]=[\"no\",\"no.\",\"id\",\"#\",\"number\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["field_value,field_conf= fieldsearch.search_field(json_path,invoice_no,table_dimensions[0],table_dimensions[1] )\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# get the results\n", "import json\n", "import boto3\n", "client = boto3.client(\n", "                    service_name='textract',\n", "                    region_name='us-east-1',\n", "                    endpoint_url='https://textract.us-east-1.amazonaws.com',\n", "            )\n", "feature_field=['TABLES']\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def reader( file):\n", "        with open(file, \"rb\") as image_file:\n", "            img_test = image_file.read()\n", "            bytes_test = bytearray(img_test)\n", "        return bytes_test\n", "    \n", "data_str =reader(\"/home/<USER>/invoice/extract1/1.png\")\n", "response = client.analyze_document(\n", "                    Document={'Bytes': data_str}, FeatureTypes=feature_field)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import tensorflow as tf\n", "from object_detection.utils import ops as utils_ops\n", "from object_detection.utils import label_map_util\n", "\n", "output_directory = 'inference_graph'"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3' \n", "TF_OB_model = tf.saved_model.load(f'./{output_directory}/saved_model')\n", "print(\"TENSORFLOW Object Detection API Model Loaded ......\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["image=\"/home/<USER>/invoice/invoice-flow/inv-flow-dvc/apautomation/Invoice/pdf/6dce29fd-34b6-43bd-b979-d2fe0f1b3768_0.png\"\n", "json_path=\"/home/<USER>/invoice/invoice-flow/inv-flow-dvc/apautomation/Invoice/pdf/6dce29fd-34b6-43bd-b979-d2fe0f1b3768_0invoice.json\"\n", "mapping_json=json_path.replace(\"invoice.json\",\"mapping.json\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%time\n", "from Invoice.Vendor_Extraction import *\n", "res=getVendors(TF_OB_model,image,json_path)\n", "res"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from urllib.parse import urlparse\n", "url=\"www.centralfoodequipment.com\"\n", "parsed_uri = urlparse(str(url))\n", "print(parsed_uri)\n", "if parsed_uri.netloc!=\"\":\n", "    \n", "    domain=parsed_uri.netloc.split(\".\")\n", "else:\n", "    domain=parsed_uri.path.split(\".\")\n", "    \n", "print(domain)\n", "                   "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import re\n", "import lexnlp.extract.en.urls\n", "\n", "for d in data[\"Blocks\"]:\n", "    if d[\"BlockType\"]==\"LINE\":\n", "        #print(d[\"Text\"])\n", "        text= d[\"Text\"]\n", "        emails = re.findall(r\"[a-z0-9\\.\\-+_]+@[a-z0-9\\.\\-+_]+\\.[a-z]+\", text)\n", "        url=list(lexnlp.extract.en.urls.get_urls(text))\n", "        #print(url )\n", "        if len(url)>0:\n", "            print(url[0].split(\".\")[1])\n", "            \n", "            \n", "            \n", "            \n", "                \n", "                    \n", "              \n", "                "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from Invoice.Vendor_Extraction import *\n", "print(getVendorByMail(json_path))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["emails = re.findall(r\"[a-z0-9\\.\\-+_]+@[a-z0-9\\.\\-+_]+\\.[a-z]+\",\"<EMAIL>\")\n", "emails[0].split(\"@\")[1].split(\".\")[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import spacy\n", "#nlp=spacy.load(\"/home/<USER>/invoice/invoice-flow/inv-flow-dvc/apautomation/spacy_model/Jul_20\")\n", "nlp=spacy.load(\"en_core_web_md\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def spacy_geturl(text):\n", "    urls=[]\n", "    doc=nlp(text)\n", "    for e in doc:\n", "        if e.like_url:\n", "            urls.append(e)\n", "\n", "    \n", "    \n", "    return urls"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'BlockType': 'LINE', 'Confidence': 99.56158447265625, 'Text': 'INVOICE', 'Geometry': {'BoundingBox': {'Width': 0.12170088291168213, 'Height': 0.015145071782171726, 'Left': 0.03257957473397255, 'Top': 0.012613716535270214}, 'Polygon': [{'X': 0.03257957473397255, 'Y': 0.012613716535270214}, {'X': 0.15428045392036438, 'Y': 0.012613716535270214}, {'X': 0.15428045392036438, 'Y': 0.02775878831744194}, {'X': 0.03257957473397255, 'Y': 0.02775878831744194}]}, 'Id': '960c8117-b707-48a6-a692-95b8dbfe100c', 'Relationships': [{'Type': 'CHILD', 'Ids': ['0b44d509-fc9c-48f5-ab49-674f57eaf69a']}]} 0.015145071782171726\n", "{'BlockType': 'LINE', 'Confidence': 99.67623138427734, 'Text': '1 of 1', 'Geometry': {'BoundingBox': {'Width': 0.058277685195207596, 'Height': 0.009624906815588474, 'Left': 0.832781195640564, 'Top': 0.01537875272333622}, 'Polygon': [{'X': 0.832781195640564, 'Y': 0.01537875272333622}, {'X': 0.8910588622093201, 'Y': 0.01537875272333622}, {'X': 0.8910588622093201, 'Y': 0.02500366047024727}, {'X': 0.832781195640564, 'Y': 0.02500366047024727}]}, 'Id': '1f594b3c-e765-4cdd-ac6f-be508857a360', 'Relationships': [{'Type': 'CHILD', 'Ids': ['037d5be5-50e3-4d62-9fe5-3c7f4ee64000', 'ea1419a2-534e-405d-9ee9-b45eed0b640b', '612642bc-ccb0-48f0-8d64-03214901d3e3']}]} 0.009624906815588474\n", "{'BlockType': 'LINE', 'Confidence': 99.92891693115234, 'Text': 'cme', 'Geometry': {'BoundingBox': {'Width': 0.23148104548454285, 'Height': 0.04973457008600235, 'Left': 0.45077377557754517, 'Top': 0.029116148129105568}, 'Polygon': [{'X': 0.45077377557754517, 'Y': 0.029116148129105568}, {'X': 0.6822548508644104, 'Y': 0.029116148129105568}, {'X': 0.6822548508644104, 'Y': 0.07885071635246277}, {'X': 0.45077377557754517, 'Y': 0.07885071635246277}]}, 'Id': '75135c31-017b-427d-b536-5f677cdff12d', 'Relationships': [{'Type': 'CHILD', 'Ids': ['2d6d3a18-52e4-4736-b5dd-ef3e5b1d9985']}]} 0.04973457008600235\n", "{'BlockType': 'LINE', 'Confidence': 99.1102066040039, 'Text': 'Bill To:', 'Geometry': {'BoundingBox': {'Width': 0.07789835333824158, 'Height': 0.01045423187315464, 'Left': 0.07309974730014801, 'Top': 0.18940794467926025}, 'Polygon': [{'X': 0.07309974730014801, 'Y': 0.18940794467926025}, {'X': 0.1509981006383896, 'Y': 0.18940794467926025}, {'X': 0.1509981006383896, 'Y': 0.19986216723918915}, {'X': 0.07309974730014801, 'Y': 0.19986216723918915}]}, 'Id': 'ce63180e-cffd-47f6-97e1-83ef613cddd9', 'Relationships': [{'Type': 'CHILD', 'Ids': ['2a76d932-dc70-46cd-95da-11a3971a1327', '6d44867f-df9c-4e94-98a8-0bc37a68b391']}]} 0.01045423187315464\n", "{'BlockType': 'LINE', 'Confidence': 99.35572052001953, 'Text': 'Ship To:', 'Geometry': {'BoundingBox': {'Width': 0.07739538699388504, 'Height': 0.011509150266647339, 'Left': 0.5094971060752869, 'Top': 0.1929406374692917}, 'Polygon': [{'X': 0.5094971060752869, 'Y': 0.1929406374692917}, {'X': 0.5868925452232361, 'Y': 0.1929406374692917}, {'X': 0.5868925452232361, 'Y': 0.20444978773593903}, {'X': 0.5094971060752869, 'Y': 0.20444978773593903}]}, 'Id': '36e65610-3aa9-4cc5-9f8b-f46b4295038c', 'Relationships': [{'Type': 'CHILD', 'Ids': ['32d558bf-1163-4770-a5d1-5a85f62c2d04', 'b8b236bb-045d-4cfa-9cbf-c61ffbae0749']}]} 0.011509150266647339\n", "{'BlockType': 'LINE', 'Confidence': 99.89234924316406, 'Text': 'Invoice', 'Geometry': {'BoundingBox': {'Width': 0.06872010231018066, 'Height': 0.009126320481300354, 'Left': 0.8564733862876892, 'Top': 0.19861246645450592}, 'Polygon': [{'X': 0.8564733862876892, 'Y': 0.19861246645450592}, {'X': 0.9251934885978699, 'Y': 0.19861246645450592}, {'X': 0.9251934885978699, 'Y': 0.20773878693580627}, {'X': 0.8564733862876892, 'Y': 0.20773878693580627}]}, 'Id': 'b0a53d8e-ad83-4100-87b6-13589ba31b75', 'Relationships': [{'Type': 'CHILD', 'Ids': ['89f8397b-c789-491b-aec6-f13976dd3020']}]} 0.009126320481300354\n", "{'BlockType': 'LINE', 'Confidence': 99.8611068725586, 'Text': '<PERSON><PERSON><PERSON> HOPKINS UNIVERSITY', 'Geometry': {'BoundingBox': {'Width': 0.23761942982673645, 'Height': 0.010981863364577293, 'Left': 0.08462849259376526, 'Top': 0.20481951534748077}, 'Polygon': [{'X': 0.08462849259376526, 'Y': 0.20481951534748077}, {'X': 0.3222479224205017, 'Y': 0.20481951534748077}, {'X': 0.3222479224205017, 'Y': 0.2158013880252838}, {'X': 0.08462849259376526, 'Y': 0.2158013880252838}]}, 'Id': 'b1b38e9d-64e9-4a5f-8748-724daed69665', 'Relationships': [{'Type': 'CHILD', 'Ids': ['f5cc1ef5-db21-4f96-bec0-7b834c0e6125', 'a811d343-ea4a-45dd-94f2-dd089d9fb79b', '78486bcf-3c21-4d88-8586-f02c332e0784']}]} 0.010981863364577293\n", "{'BlockType': 'LINE', 'Confidence': 99.86701965332031, 'Text': 'THE JOHNS HOPKINS HOSPITAL', 'Geometry': {'BoundingBox': {'Width': 0.25797849893569946, 'Height': 0.010434535332024097, 'Left': 0.5222697854042053, 'Top': 0.21029579639434814}, 'Polygon': [{'X': 0.5222697854042053, 'Y': 0.21029579639434814}, {'X': 0.7802482843399048, 'Y': 0.21029579639434814}, {'X': 0.7802482843399048, 'Y': 0.22073033452033997}, {'X': 0.5222697854042053, 'Y': 0.22073033452033997}]}, 'Id': '6672bd16-7b9e-4043-8c06-a78168d5748c', 'Relationships': [{'Type': 'CHILD', 'Ids': ['94cd5e87-a2c7-45ff-ab91-e9d2c86afbce', 'af9abde4-fbf6-4a4a-81ac-1763e9deb148', '5c78c5d9-c864-42e5-a935-f58b5e5c7d58', 'e5028c46-f5ae-4b17-9a60-ec329c2662c8']}]} 0.010434535332024097\n", "{'BlockType': 'LINE', 'Confidence': 99.40376281738281, 'Text': '20303937', 'Geometry': {'BoundingBox': {'Width': 0.07721537351608276, 'Height': 0.009961709380149841, 'Left': 0.8553777933120728, 'Top': 0.2133590281009674}, 'Polygon': [{'X': 0.8553777933120728, 'Y': 0.2133590281009674}, {'X': 0.9325931668281555, 'Y': 0.2133590281009674}, {'X': 0.9325931668281555, 'Y': 0.22332073748111725}, {'X': 0.8553777933120728, 'Y': 0.22332073748111725}]}, 'Id': '210e48ec-e626-4d82-aacc-9770bb2f40a4', 'Relationships': [{'Type': 'CHILD', 'Ids': ['585854ec-0693-44c2-92b6-b73cdae4b12f']}]} 0.009961709380149841\n", "{'BlockType': 'LINE', 'Confidence': 99.77144622802734, 'Text': 'JH A/P SHARED SERVICE CENTER', 'Geometry': {'BoundingBox': {'Width': 0.27640005946159363, 'Height': 0.010671341791749, 'Left': 0.08490289002656937, 'Top': 0.21894286572933197}, 'Polygon': [{'X': 0.08490289002656937, 'Y': 0.21894286572933197}, {'X': 0.3613029420375824, 'Y': 0.21894286572933197}, {'X': 0.3613029420375824, 'Y': 0.22961421310901642}, {'X': 0.08490289002656937, 'Y': 0.22961421310901642}]}, 'Id': 'd23ac886-9715-4be5-bae9-b4dbf93f3983', 'Relationships': [{'Type': 'CHILD', 'Ids': ['fc760acb-2671-46bb-81b5-0c183fbd93db', '0050d460-8c76-49f2-916e-190c57c042f6', 'bda0ea63-bead-4e9f-af8b-43ce4d1088ae', 'ca66c57e-92f9-4911-a066-b10b87cb2569', '72523ac3-d626-4a14-968b-bcedb943cc16']}]} 0.010671341791749\n", "{'BlockType': 'LINE', 'Confidence': 99.82137298583984, 'Text': '1780 E FAYETTE STREET', 'Geometry': {'BoundingBox': {'Width': 0.20786532759666443, 'Height': 0.010368362069129944, 'Left': 0.5230731964111328, 'Top': 0.22375893592834473}, 'Polygon': [{'X': 0.5230731964111328, 'Y': 0.22375893592834473}, {'X': 0.7309385538101196, 'Y': 0.22375893592834473}, {'X': 0.7309385538101196, 'Y': 0.23412729799747467}, {'X': 0.5230731964111328, 'Y': 0.23412729799747467}]}, 'Id': 'cf6533f8-5019-478d-abd0-1347b586700f', 'Relationships': [{'Type': 'CHILD', 'Ids': ['4b8b527a-93ba-49ce-8f4f-efdc533bacfe', '908d92f1-6879-49e0-8765-b518ce056e02', '0eb78e24-d318-498a-99e0-b15444f89f52', '10e2a20d-5d73-4982-b399-2c6e65d0e433']}]} 0.010368362069129944\n", "{'BlockType': 'LINE', 'Confidence': 99.54397583007812, 'Text': 'Date', 'Geometry': {'BoundingBox': {'Width': 0.03918445110321045, 'Height': 0.008371710777282715, 'Left': 0.863792896270752, 'Top': 0.22719480097293854}, 'Polygon': [{'X': 0.863792896270752, 'Y': 0.22719480097293854}, {'X': 0.9029773473739624, 'Y': 0.22719480097293854}, {'X': 0.9029773473739624, 'Y': 0.23556651175022125}, {'X': 0.863792896270752, 'Y': 0.23556651175022125}]}, 'Id': 'f210df28-eb86-488f-84b4-04ea1acc8937', 'Relationships': [{'Type': 'CHILD', 'Ids': ['017bf08a-bdaa-4deb-947e-bb185864487a']}]} 0.008371710777282715\n", "{'BlockType': 'LINE', 'Confidence': 99.77648162841797, 'Text': 'PO BOX 33499', 'Geometry': {'BoundingBox': {'Width': 0.11670301109552383, 'Height': 0.010190003551542759, 'Left': 0.08508056402206421, 'Top': 0.23269125819206238}, 'Polygon': [{'X': 0.08508056402206421, 'Y': 0.23269125819206238}, {'X': 0.20178356766700745, 'Y': 0.23269125819206238}, {'X': 0.20178356766700745, 'Y': 0.24288126826286316}, {'X': 0.08508056402206421, 'Y': 0.24288126826286316}]}, 'Id': 'b577dc2e-0e81-4257-8aa9-5095ff06fff7', 'Relationships': [{'Type': 'CHILD', 'Ids': ['0e306ada-b5c0-4bf3-a808-a1e629025771', 'a208634b-4e9e-4046-8c5a-233439990c6d', '795ac1a2-09c2-4aa3-bb8d-a76fed9bacca']}]} 0.010190003551542759\n", "{'BlockType': 'LINE', 'Confidence': 99.36239624023438, 'Text': 'PO 2004660474', 'Geometry': {'BoundingBox': {'Width': 0.1268453150987625, 'Height': 0.010463342070579529, 'Left': 0.5232920050621033, 'Top': 0.23803949356079102}, 'Polygon': [{'X': 0.5232920050621033, 'Y': 0.23803949356079102}, {'X': 0.6501373052597046, 'Y': 0.23803949356079102}, {'X': 0.6501373052597046, 'Y': 0.24850283563137054}, {'X': 0.5232920050621033, 'Y': 0.24850283563137054}]}, 'Id': '0bddc35f-7c75-470c-bed1-25bb59978127', 'Relationships': [{'Type': 'CHILD', 'Ids': ['4a264d15-0f46-4ecc-8ef7-f69955560fb1', 'e8e39af9-6933-4e25-b2ed-f423ea9ff31e']}]} 0.010463342070579529\n", "{'BlockType': 'LINE', 'Confidence': 99.32086181640625, 'Text': 'BALTIMORE', 'Geometry': {'BoundingBox': {'Width': 0.08917292952537537, 'Height': 0.009790420532226562, 'Left': 0.08437332510948181, 'Top': 0.24747678637504578}, 'Polygon': [{'X': 0.08437332510948181, 'Y': 0.24747678637504578}, {'X': 0.17354625463485718, 'Y': 0.24747678637504578}, {'X': 0.17354625463485718, 'Y': 0.25726720690727234}, {'X': 0.08437332510948181, 'Y': 0.25726720690727234}]}, 'Id': 'ab336140-8924-4a46-85e9-3273f0bfdf5f', 'Relationships': [{'Type': 'CHILD', 'Ids': ['46e6ede3-6350-4946-8f3e-94241f5e3445']}]} 0.009790420532226562\n", "{'BlockType': 'LINE', 'Confidence': 99.4130859375, 'Text': '06/04/20', 'Geometry': {'BoundingBox': {'Width': 0.0764128565788269, 'Height': 0.010521754622459412, 'Left': 0.8559623956680298, 'Top': 0.2416754513978958}, 'Polygon': [{'X': 0.8559623956680298, 'Y': 0.2416754513978958}, {'X': 0.9323752522468567, 'Y': 0.2416754513978958}, {'X': 0.9323752522468567, 'Y': 0.2521972060203552}, {'X': 0.8559623956680298, 'Y': 0.2521972060203552}]}, 'Id': 'd7671ebf-8390-4640-8517-a691e7d95d4e', 'Relationships': [{'Type': 'CHILD', 'Ids': ['6f5c96d7-289d-4083-8eb8-32705cfec60a']}]} 0.010521754622459412\n", "{'BlockType': 'LINE', 'Confidence': 99.78610229492188, 'Text': 'MD 21218', 'Geometry': {'BoundingBox': {'Width': 0.07989232987165451, 'Height': 0.010286599397659302, 'Left': 0.2903769910335541, 'Top': 0.24814921617507935}, 'Polygon': [{'X': 0.2903769910335541, 'Y': 0.24814921617507935}, {'X': 0.3702693283557892, 'Y': 0.24814921617507935}, {'X': 0.3702693283557892, 'Y': 0.25843581557273865}, {'X': 0.2903769910335541, 'Y': 0.25843581557273865}]}, 'Id': '6c3e9d2d-aa5d-442f-8db8-6ef6624a5dc2', 'Relationships': [{'Type': 'CHILD', 'Ids': ['d7610bcd-2a89-48a2-a729-e64c3bf7f150', '0dd25522-a631-460f-bd68-336ba8551539']}]} 0.010286599397659302\n", "{'BlockType': 'LINE', 'Confidence': 99.433837890625, 'Text': 'BALTIMORE', 'Geometry': {'BoundingBox': {'Width': 0.08976995944976807, 'Height': 0.010111421346664429, 'Left': 0.5224732160568237, 'Top': 0.25269007682800293}, 'Polygon': [{'X': 0.5224732160568237, 'Y': 0.25269007682800293}, {'X': 0.6122431755065918, 'Y': 0.25269007682800293}, {'X': 0.6122431755065918, 'Y': 0.26280149817466736}, {'X': 0.5224732160568237, 'Y': 0.26280149817466736}]}, 'Id': '6a5eca40-1b9c-498d-82ac-5fa4bea9a7d8', 'Relationships': [{'Type': 'CHILD', 'Ids': ['709b94d1-29b8-4fef-b18a-2e69978eab31']}]} 0.010111421346664429\n", "{'BlockType': 'LINE', 'Confidence': 99.86109924316406, 'Text': 'MD 21231', 'Geometry': {'BoundingBox': {'Width': 0.07843581587076187, 'Height': 0.010100096464157104, 'Left': 0.7294095754623413, 'Top': 0.2535828948020935}, 'Polygon': [{'X': 0.7294095754623413, 'Y': 0.2535828948020935}, {'X': 0.807845413684845, 'Y': 0.2535828948020935}, {'X': 0.807845413684845, 'Y': 0.2636829912662506}, {'X': 0.7294095754623413, 'Y': 0.2636829912662506}]}, 'Id': '65e6fc06-5b58-4402-b992-22144221826c', 'Relationships': [{'Type': 'CHILD', 'Ids': ['f8a14c31-fc3c-440e-a49e-a3df64b37d85', '263157cd-3586-4c91-8dc1-8159f422c65d']}]} 0.010100096464157104\n", "{'BlockType': 'LINE', 'Confidence': 99.93695831298828, 'Text': 'Time', 'Geometry': {'BoundingBox': {'Width': 0.03930032253265381, 'Height': 0.009933531284332275, 'Left': 0.8628628849983215, 'Top': 0.2590828835964203}, 'Polygon': [{'X': 0.8628628849983215, 'Y': 0.2590828835964203}, {'X': 0.9021632075309753, 'Y': 0.2590828835964203}, {'X': 0.9021632075309753, 'Y': 0.26901641488075256}, {'X': 0.8628628849983215, 'Y': 0.26901641488075256}]}, 'Id': '6bd4c1e1-fc53-46ad-a01f-271563f0eef8', 'Relationships': [{'Type': 'CHILD', 'Ids': ['8b9aafd9-c6ee-4984-a09e-34f2315b4337']}]} 0.009933531284332275\n", "{'BlockType': 'LINE', 'Confidence': 95.32241821289062, 'Text': 'JUN 0 9 2020', 'Geometry': {'BoundingBox': {'Width': 0.12029936164617538, 'Height': 0.02833518572151661, 'Left': 0.40655505657196045, 'Top': 0.27192115783691406}, 'Polygon': [{'X': 0.40655505657196045, 'Y': 0.27192115783691406}, {'X': 0.526854395866394, 'Y': 0.27192115783691406}, {'X': 0.526854395866394, 'Y': 0.3002563416957855}, {'X': 0.40655505657196045, 'Y': 0.3002563416957855}]}, 'Id': 'f8dbc031-e037-4e5d-970d-237222a93e30', 'Relationships': [{'Type': 'CHILD', 'Ids': ['08b90a1b-8d73-439b-b29b-d2d190f31a5b', '042af7c0-3a0e-4b1c-b649-afb984535ffa', '39a6ebcd-1015-4563-ae42-b45c1b520ebd', '0588c6b4-6c4d-4758-9662-3e471061d114']}]} 0.02833518572151661\n", "{'BlockType': 'LINE', 'Confidence': 98.30797576904297, 'Text': '22:45:55', 'Geometry': {'BoundingBox': {'Width': 0.07626652717590332, 'Height': 0.009905606508255005, 'Left': 0.8486059904098511, 'Top': 0.27788305282592773}, 'Polygon': [{'X': 0.8486059904098511, 'Y': 0.27788305282592773}, {'X': 0.9248725175857544, 'Y': 0.27788305282592773}, {'X': 0.9248725175857544, 'Y': 0.28778865933418274}, {'X': 0.8486059904098511, 'Y': 0.28778865933418274}]}, 'Id': 'f313e8cc-1651-44bb-9c41-26dde6046da8', 'Relationships': [{'Type': 'CHILD', 'Ids': ['6271a066-c5a2-46a1-84e6-739cf8ed454a']}]} 0.009905606508255005\n", "{'BlockType': 'LINE', 'Confidence': 98.48590850830078, 'Text': 'Co/Cust No', 'Geometry': {'BoundingBox': {'Width': 0.09963671118021011, 'Height': 0.009683012962341309, 'Left': 0.08456812053918839, 'Top': 0.29365652799606323}, 'Polygon': [{'X': 0.08456812053918839, 'Y': 0.29365652799606323}, {'X': 0.1842048317193985, 'Y': 0.29365652799606323}, {'X': 0.1842048317193985, 'Y': 0.30333954095840454}, {'X': 0.08456812053918839, 'Y': 0.30333954095840454}]}, 'Id': '3decdc87-eedb-487f-9477-3d693456fad9', 'Relationships': [{'Type': 'CHILD', 'Ids': ['c4e5784b-c850-41d8-be1f-432980f3102a', '9cf03488-61e2-4a3b-ae7f-3067ece4c85a']}]} 0.009683012962341309\n", "{'BlockType': 'LINE', 'Confidence': 99.67760467529297, 'Text': 'Customer PO', 'Geometry': {'BoundingBox': {'Width': 0.11032459139823914, 'Height': 0.010095869190990925, 'Left': 0.2162478268146515, 'Top': 0.29515910148620605}, 'Polygon': [{'X': 0.2162478268146515, 'Y': 0.29515910148620605}, {'X': 0.3265724182128906, 'Y': 0.29515910148620605}, {'X': 0.3265724182128906, 'Y': 0.3052549660205841}, {'X': 0.2162478268146515, 'Y': 0.3052549660205841}]}, 'Id': 'f1ecd25f-a295-403d-94cd-97f64644e004', 'Relationships': [{'Type': 'CHILD', 'Ids': ['0475a93f-193b-4d2c-8211-b06bee9a0781', '4ca09045-1a42-4c75-a1c5-2138692f66bf']}]} 0.010095869190990925\n", "{'BlockType': 'LINE', 'Confidence': 99.5079574584961, 'Text': 'Ter Sales Rep', 'Geometry': {'BoundingBox': {'Width': 0.13340802490711212, 'Height': 0.012247569859027863, 'Left': 0.43917855620384216, 'Top': 0.2965245842933655}, 'Polygon': [{'X': 0.43917855620384216, 'Y': 0.2965245842933655}, {'X': 0.5725865960121155, 'Y': 0.2965245842933655}, {'X': 0.5725865960121155, 'Y': 0.30877214670181274}, {'X': 0.43917855620384216, 'Y': 0.30877214670181274}]}, 'Id': '73b0c7d2-1c65-48a8-926c-c89a6f71be56', 'Relationships': [{'Type': 'CHILD', 'Ids': ['30f4e433-5a82-48b5-a013-7827673debcf', 'e171bd40-c746-4322-8bee-a690bafe7d68', '7e47a4e2-2d97-491e-b725-36469c5bffa7']}]} 0.012247569859027863\n", "{'BlockType': 'LINE', 'Confidence': 99.7659912109375, 'Text': 'Entered By', 'Geometry': {'BoundingBox': {'Width': 0.09926580637693405, 'Height': 0.011874523013830185, 'Left': 0.8417482972145081, 'Top': 0.29890021681785583}, 'Polygon': [{'X': 0.8417482972145081, 'Y': 0.29890021681785583}, {'X': 0.9410141110420227, 'Y': 0.29890021681785583}, {'X': 0.9410141110420227, 'Y': 0.3107747435569763}, {'X': 0.8417482972145081, 'Y': 0.3107747435569763}]}, 'Id': '85c8be12-9043-4fdc-b6d1-366d25602b6e', 'Relationships': [{'Type': 'CHILD', 'Ids': ['739e29c8-1bb7-4b16-9320-1a3a6a56c743', '5c9da1f3-a446-43ff-999e-03759a5f665d']}]} 0.011874523013830185\n", "{'BlockType': 'LINE', 'Confidence': 98.2580337524414, 'Text': '2/0000108795 2004660474', 'Geometry': {'BoundingBox': {'Width': 0.22710484266281128, 'Height': 0.01077586505562067, 'Left': 0.08383753150701523, 'Top': 0.30723196268081665}, 'Polygon': [{'X': 0.08383753150701523, 'Y': 0.30723196268081665}, {'X': 0.3109423816204071, 'Y': 0.30723196268081665}, {'X': 0.3109423816204071, 'Y': 0.31800782680511475}, {'X': 0.08383753150701523, 'Y': 0.31800782680511475}]}, 'Id': '59fa96b6-d6b8-4c29-9ef9-a519aa6233bb', 'Relationships': [{'Type': 'CHILD', 'Ids': ['2a5d73ab-aef5-4a75-bcbc-c1b4fc6e4956', '37e79170-a15b-4acb-b35b-79709772db6e']}]} 0.01077586505562067\n", "{'BlockType': 'LINE', 'Confidence': 99.5107650756836, 'Text': '<PERSON><PERSON>', 'Geometry': {'BoundingBox': {'Width': 0.17496022582054138, 'Height': 0.01153002679347992, 'Left': 0.4401853084564209, 'Top': 0.3097072243690491}, 'Polygon': [{'X': 0.4401853084564209, 'Y': 0.3097072243690491}, {'X': 0.6151455640792847, 'Y': 0.3097072243690491}, {'X': 0.6151455640792847, 'Y': 0.3212372362613678}, {'X': 0.4401853084564209, 'Y': 0.3212372362613678}]}, 'Id': '25ec9fc8-d268-4bbb-8d75-5bbc48a357f3', 'Relationships': [{'Type': 'CHILD', 'Ids': ['196a03dc-3e02-4495-a174-ff3542e0c437', '13f70c6f-3c66-4531-996e-cdc6dd67f084', '2a7708f2-3267-420d-96e3-7f58f02dbdbb']}]} 0.01153002679347992\n", "{'BlockType': 'LINE', 'Confidence': 98.41480255126953, 'Text': 'DNEVES', 'Geometry': {'BoundingBox': {'Width': 0.05822181701660156, 'Height': 0.008954912424087524, 'Left': 0.8435723781585693, 'Top': 0.31318020820617676}, 'Polygon': [{'X': 0.8435723781585693, 'Y': 0.31318020820617676}, {'X': 0.9017941951751709, 'Y': 0.31318020820617676}, {'X': 0.9017941951751709, 'Y': 0.3221351206302643}, {'X': 0.8435723781585693, 'Y': 0.3221351206302643}]}, 'Id': '2c1604c8-7161-4b80-8c41-a01dab3938b3', 'Relationships': [{'Type': 'CHILD', 'Ids': ['d935ef06-3523-4047-a6a0-935244c6763b']}]} 0.008954912424087524\n", "{'BlockType': 'LINE', 'Confidence': 98.63285064697266, 'Text': 'Order No 64093/00', 'Geometry': {'BoundingBox': {'Width': 0.1676509827375412, 'Height': 0.01076501701027155, 'Left': 0.07258747518062592, 'Top': 0.3221951723098755}, 'Polygon': [{'X': 0.07258747518062592, 'Y': 0.3221951723098755}, {'X': 0.24023845791816711, 'Y': 0.3221951723098755}, {'X': 0.24023845791816711, 'Y': 0.33296018838882446}, {'X': 0.07258747518062592, 'Y': 0.33296018838882446}]}, 'Id': '25d7bd15-4529-4f90-b8f7-6ee404b130c4', 'Relationships': [{'Type': 'CHILD', 'Ids': ['2f1cb7b4-b3fa-4db0-a401-dd2806baa91f', '5c8471a0-8919-4888-a371-2a0ce3e984d8', 'ec6f16a5-e144-4b82-97f9-76502e4e39a6']}]} 0.01076501701027155\n", "{'BlockType': 'LINE', 'Confidence': 99.61636352539062, 'Text': 'Ship Via', 'Geometry': {'BoundingBox': {'Width': 0.0790785402059555, 'Height': 0.011360317468643188, 'Left': 0.07317303121089935, 'Top': 0.33520346879959106}, 'Polygon': [{'X': 0.07317303121089935, 'Y': 0.33520346879959106}, {'X': 0.15225157141685486, 'Y': 0.33520346879959106}, {'X': 0.15225157141685486, 'Y': 0.34656378626823425}, {'X': 0.07317303121089935, 'Y': 0.34656378626823425}]}, 'Id': '20c3772e-a19c-49a0-9695-5eb108c9c980', 'Relationships': [{'Type': 'CHILD', 'Ids': ['0e977515-7ac7-4c92-9b40-e4970b3b0704', 'f3ee2e1e-6667-49f8-bb4c-7d0d0bf12813']}]} 0.011360317468643188\n", "{'BlockType': 'LINE', 'Confidence': 99.61685180664062, 'Text': 'Terms', 'Geometry': {'BoundingBox': {'Width': 0.049743831157684326, 'Height': 0.008871465921401978, 'Left': 0.39987310767173767, 'Top': 0.33849483728408813}, 'Polygon': [{'X': 0.39987310767173767, 'Y': 0.33849483728408813}, {'X': 0.449616938829422, 'Y': 0.33849483728408813}, {'X': 0.449616938829422, 'Y': 0.3473663032054901}, {'X': 0.39987310767173767, 'Y': 0.3473663032054901}]}, 'Id': 'd724463e-5a0c-4c33-8fcb-c6f9fd993806', 'Relationships': [{'Type': 'CHILD', 'Ids': ['9eeede46-612a-402a-b83a-94f273cd80cd']}]} 0.008871465921401978\n", "{'BlockType': 'LINE', 'Confidence': 99.53004455566406, 'Text': 'NET 30 DAYS', 'Geometry': {'BoundingBox': {'Width': 0.10891108959913254, 'Height': 0.009652793407440186, 'Left': 0.5465112924575806, 'Top': 0.33873945474624634}, 'Polygon': [{'X': 0.5465112924575806, 'Y': 0.33873945474624634}, {'X': 0.6554223299026489, 'Y': 0.33873945474624634}, {'X': 0.6554223299026489, 'Y': 0.3483922481536865}, {'X': 0.5465112924575806, 'Y': 0.3483922481536865}]}, 'Id': 'd19a4ffb-8798-47f5-a91a-d810e42bfd71', 'Relationships': [{'Type': 'CHILD', 'Ids': ['0d5dadf2-7bde-45e9-a925-98a905e187b0', 'c91238a3-d7fd-4bdb-b9fb-4cc84eecd2dd', '2b4a0d26-4cb4-47eb-bf7d-44b6a77867f2']}]} 0.009652793407440186\n", "{'BlockType': 'LINE', 'Confidence': 99.7498550415039, 'Text': 'Ref#', 'Geometry': {'BoundingBox': {'Width': 0.03676164150238037, 'Height': 0.009057015180587769, 'Left': 0.****************, 'Top': 0.****************}, 'Polygon': [{'X': 0.****************, 'Y': 0.****************}, {'X': 0.****************, 'Y': 0.****************}, {'X': 0.****************, 'Y': 0.*****************}, {'X': 0.****************, 'Y': 0.*****************}]}, 'Id': 'da9279d9-2785-42e0-ba80-1240f374f2c7', 'Relationships': [{'Type': 'CHILD', 'Ids': ['bbbc4e22-b317-4b3e-87f9-33385858e5cd']}]} 0.009057015180587769\n", "{'BlockType': 'LINE', 'Confidence': 99.*************, 'Text': 'Pay Type ACCOUNTS RECEIVABLE', 'Geometry': {'BoundingBox': {'Width': 0.****************, 'Height': 0.010792965069413185, 'Left': 0.*****************, 'Top': 0.****************}, 'Polygon': [{'X': 0.*****************, 'Y': 0.****************}, {'X': 0.*****************, 'Y': 0.****************}, {'X': 0.*****************, 'Y': 0.*****************}, {'X': 0.*****************, 'Y': 0.*****************}]}, 'Id': '8d77ba13-25c9-479c-91f8-7705a6bf68c9', 'Relationships': [{'Type': 'CHILD', 'Ids': ['2b8e46cd-11a9-4515-ac32-c7bb57586e07', 'f3bebb45-dc7d-4e35-86be-e60d3eae1919', '7551fd37-21cd-4793-a3e3-32e2309a2653', '12a2a9f8-28e7-4fd3-ade9-8afbf50a036b']}]} 0.010792965069413185\n", "{'BlockType': 'LINE', 'Confidence': 80.**************, 'Text': 'Seq Item Number/Description', 'Geometry': {'BoundingBox': {'Width': 0.*****************, 'Height': 0.011650259606540203, 'Left': 0.*****************, 'Top': 0.*****************}, 'Polygon': [{'X': 0.*****************, 'Y': 0.*****************}, {'X': 0.3431723415851593, 'Y': 0.*****************}, {'X': 0.3431723415851593, 'Y': 0.38413098454475403}, {'X': 0.*****************, 'Y': 0.38413098454475403}]}, 'Id': '57b216c2-199e-4d5d-9ff5-b643d3528a93', 'Relationships': [{'Type': 'CHILD', 'Ids': ['f551b8cf-2e27-415d-a00e-1ad265be8a00', '9416b9ba-25f8-4b39-a944-24b2fd507a49', '388d9e17-1870-4c6a-b3c1-6b0151daf397']}]} 0.011650259606540203\n", "{'BlockType': 'LINE', 'Confidence': 98.99694061279297, 'Text': 'U/M Ordered', 'Geometry': {'BoundingBox': {'Width': 0.11274904012680054, 'Height': 0.009336262941360474, 'Left': 0.3904222548007965, 'Top': 0.37457776069641113}, 'Polygon': [{'X': 0.3904222548007965, 'Y': 0.37457776069641113}, {'X': 0.5031712651252747, 'Y': 0.37457776069641113}, {'X': 0.5031712651252747, 'Y': 0.3839140236377716}, {'X': 0.3904222548007965, 'Y': 0.3839140236377716}]}, 'Id': '89a46acd-315f-4034-8d9d-9976a156c21c', 'Relationships': [{'Type': 'CHILD', 'Ids': ['64340160-207f-403a-8a5e-7af96f17458a', 'f6df1bb9-a57e-42d2-a8e5-f3b4ebc5601a']}]} 0.009336262941360474\n", "{'BlockType': 'LINE', 'Confidence': 98.6435317993164, 'Text': 'Shipped', 'Geometry': {'BoundingBox': {'Width': 0.07111680507659912, 'Height': 0.011734336614608765, 'Left': 0.5256155729293823, 'Top': 0.37490934133529663}, 'Polygon': [{'X': 0.5256155729293823, 'Y': 0.37490934133529663}, {'X': 0.5967323780059814, 'Y': 0.37490934133529663}, {'X': 0.5967323780059814, 'Y': 0.3866436779499054}, {'X': 0.5256155729293823, 'Y': 0.3866436779499054}]}, 'Id': 'd7815801-cc41-4c11-87f9-9434af0861bf', 'Relationships': [{'Type': 'CHILD', 'Ids': ['ba47c6bc-3410-436f-9876-96f1a9e05037']}]} 0.011734336614608765\n", "{'BlockType': 'LINE', 'Confidence': 97.08364868164062, 'Text': 'B/O', 'Geometry': {'BoundingBox': {'Width': 0.03089362382888794, 'Height': 0.009142786264419556, 'Left': 0.6512048244476318, 'Top': 0.3761776387691498}, 'Polygon': [{'X': 0.6512048244476318, 'Y': 0.3761776387691498}, {'X': 0.6820984482765198, 'Y': 0.3761776387691498}, {'X': 0.6820984482765198, 'Y': 0.38532042503356934}, {'X': 0.6512048244476318, 'Y': 0.38532042503356934}]}, 'Id': '8bf432e1-2fc8-4ae3-ab78-611c4505d73f', 'Relationships': [{'Type': 'CHILD', 'Ids': ['6edfbe7a-1ce6-4403-a8de-5a8f29e0f0c7']}]} 0.009142786264419556\n", "{'BlockType': 'LINE', 'Confidence': 99.31029510498047, 'Text': 'Sell Price Total', 'Geometry': {'BoundingBox': {'Width': 0.1601620763540268, 'Height': 0.009626384824514389, 'Left': 0.7188917994499207, 'Top': 0.3767111301422119}, 'Polygon': [{'X': 0.7188917994499207, 'Y': 0.3767111301422119}, {'X': 0.8790538907051086, 'Y': 0.3767111301422119}, {'X': 0.8790538907051086, 'Y': 0.3863375186920166}, {'X': 0.7188917994499207, 'Y': 0.3863375186920166}]}, 'Id': 'd122bfde-81bb-47c6-8d26-c41e190be2b3', 'Relationships': [{'Type': 'CHILD', 'Ids': ['d824647d-2682-4689-994e-38875ad114b3', 'df811b7d-8145-4665-8b87-3d31162a19cd', '60a0f784-cfe7-4761-b2f3-3c146abbd21d']}]} 0.009626384824514389\n", "{'BlockType': 'LINE', 'Confidence': 99.49791717529297, 'Text': 'Tx', 'Geometry': {'BoundingBox': {'Width': 0.0211331844329834, 'Height': 0.00878894329071045, 'Left': 0.9045231938362122, 'Top': 0.37782514095306396}, 'Polygon': [{'X': 0.9045231938362122, 'Y': 0.37782514095306396}, {'X': 0.9256563782691956, 'Y': 0.37782514095306396}, {'X': 0.9256563782691956, 'Y': 0.3866140842437744}, {'X': 0.9045231938362122, 'Y': 0.3866140842437744}]}, 'Id': '38f717e5-7ac6-4a68-919c-c05816aae86f', 'Relationships': [{'Type': 'CHILD', 'Ids': ['9d8fad8f-f7de-4dbd-a75e-644d6ed8c5e5']}]} 0.00878894329071045\n", "{'BlockType': 'LINE', 'Confidence': 97.83765411376953, 'Text': 'FOB Description: Prepay and Add', 'Geometry': {'BoundingBox': {'Width': 0.31570690870285034, 'Height': 0.012111648917198181, 'Left': 0.04947966709733009, 'Top': 0.3851239085197449}, 'Polygon': [{'X': 0.04947966709733009, 'Y': 0.3851239085197449}, {'X': 0.36518657207489014, 'Y': 0.3851239085197449}, {'X': 0.36518657207489014, 'Y': 0.39723554253578186}, {'X': 0.04947966709733009, 'Y': 0.39723554253578186}]}, 'Id': '2fedbbce-2007-47a1-8736-38842d120992', 'Relationships': [{'Type': 'CHILD', 'Ids': ['7578fda3-58f4-417a-8a1f-d5a574082cc8', 'd40a6bff-440c-49da-93eb-1845bc724715', '5b67c979-7491-4b20-b985-98329b8d77fc', '942642f2-f51c-4b32-bb5a-a6daf1f568ac', 'da439023-53a1-446e-994a-73f54587c011']}]} 0.012111648917198181\n", "{'BlockType': 'LINE', 'Confidence': 98.5836181640625, 'Text': 'HOME-999', 'Geometry': {'BoundingBox': {'Width': 0.07789841294288635, 'Height': 0.009849607944488525, 'Left': 0.11814555525779724, 'Top': 0.40012529492378235}, 'Polygon': [{'X': 0.11814555525779724, 'Y': 0.40012529492378235}, {'X': 0.1960439682006836, 'Y': 0.40012529492378235}, {'X': 0.1960439682006836, 'Y': 0.4099749028682709}, {'X': 0.11814555525779724, 'Y': 0.4099749028682709}]}, 'Id': '3477be03-e708-4f8f-83a9-72e62e220068', 'Relationships': [{'Type': 'CHILD', 'Ids': ['6441d3ef-5e79-4255-9ba9-d4e07b9ed099']}]} 0.009849607944488525\n", "{'BlockType': 'LINE', 'Confidence': 99.82451629638672, 'Text': 'EA', 'Geometry': {'BoundingBox': {'Width': 0.020300954580307007, 'Height': 0.008760124444961548, 'Left': 0.39437955617904663, 'Top': 0.40262606739997864}, 'Polygon': [{'X': 0.39437955617904663, 'Y': 0.40262606739997864}, {'X': 0.41468051075935364, 'Y': 0.40262606739997864}, {'X': 0.41468051075935364, 'Y': 0.4113861918449402}, {'X': 0.39437955617904663, 'Y': 0.4113861918449402}]}, 'Id': '1ffd4976-d35e-4ead-9839-e36c980920b2', 'Relationships': [{'Type': 'CHILD', 'Ids': ['52ef89d5-1b08-4a01-b41a-e125c59231e0']}]} 0.008760124444961548\n", "{'BlockType': 'LINE', 'Confidence': 91.55230712890625, 'Text': '1', 'Geometry': {'BoundingBox': {'Width': 0.009134620428085327, 'Height': 0.008919388055801392, 'Left': 0.49278783798217773, 'Top': 0.4027692973613739}, 'Polygon': [{'X': 0.49278783798217773, 'Y': 0.4027692973613739}, {'X': 0.5019224882125854, 'Y': 0.4027692973613739}, {'X': 0.5019224882125854, 'Y': 0.4116886854171753}, {'X': 0.49278783798217773, 'Y': 0.4116886854171753}]}, 'Id': '9a3fd73e-65de-49bb-8a04-4110e56287ef', 'Relationships': [{'Type': 'CHILD', 'Ids': ['efeb28c7-e87c-4c41-b8cc-7dafa51c4a24']}]} 0.008919388055801392\n", "{'BlockType': 'LINE', 'Confidence': 99.83745574951172, 'Text': '0', 'Geometry': {'BoundingBox': {'Width': 0.00820004940032959, 'Height': 0.008613288402557373, 'Left': 0.5827668309211731, 'Top': 0.4036628305912018}, 'Polygon': [{'X': 0.5827668309211731, 'Y': 0.4036628305912018}, {'X': 0.5909668803215027, 'Y': 0.4036628305912018}, {'X': 0.5909668803215027, 'Y': 0.41227611899375916}, {'X': 0.5827668309211731, 'Y': 0.41227611899375916}]}, 'Id': 'f7b4b048-a094-4748-98b2-60e8cf706ffa', 'Relationships': [{'Type': 'CHILD', 'Ids': ['243c85c8-5a94-480c-9cdd-62c7c427d63b']}]} 0.008613288402557373\n", "{'BlockType': 'LINE', 'Confidence': 97.9828872680664, 'Text': '1', 'Geometry': {'BoundingBox': {'Width': 0.009109199047088623, 'Height': 0.0086936354637146, 'Left': 0.6705948710441589, 'Top': 0.40445470809936523}, 'Polygon': [{'X': 0.6705948710441589, 'Y': 0.40445470809936523}, {'X': 0.6797040700912476, 'Y': 0.40445470809936523}, {'X': 0.6797040700912476, 'Y': 0.41314834356307983}, {'X': 0.6705948710441589, 'Y': 0.41314834356307983}]}, 'Id': '0a7e5654-251c-4b94-85f5-c2a94a0d0635', 'Relationships': [{'Type': 'CHILD', 'Ids': ['ab18d6c8-6019-407b-bbb1-fc1899535fa4']}]} 0.0086936354637146\n", "{'BlockType': 'LINE', 'Confidence': 84.48774719238281, 'Text': '11.51000 EA', 'Geometry': {'BoundingBox': {'Width': 0.10207217186689377, 'Height': 0.009635061025619507, 'Left': 0.745552122592926, 'Top': 0.4044962525367737}, 'Polygon': [{'X': 0.745552122592926, 'Y': 0.4044962525367737}, {'X': 0.8476243019104004, 'Y': 0.4044962525367737}, {'X': 0.8476243019104004, 'Y': 0.4141313135623932}, {'X': 0.745552122592926, 'Y': 0.4141313135623932}]}, 'Id': 'd6362cba-045e-4605-9bf9-27e7ea11d4bb', 'Relationships': [{'Type': 'CHILD', 'Ids': ['888f837c-4220-465d-af12-356aef459943', '5f28e871-8d40-4cb5-a34d-2492bc84a6e5']}]} 0.009635061025619507\n", "{'BlockType': 'LINE', 'Confidence': 99.20401000976562, 'Text': '18 in. White Heavy Duty Hook Ra', 'Geometry': {'BoundingBox': {'Width': 0.3052891790866852, 'Height': 0.01310095377266407, 'Left': 0.06670156121253967, 'Top': 0.41547420620918274}, 'Polygon': [{'X': 0.06670156121253967, 'Y': 0.41547420620918274}, {'X': 0.37199074029922485, 'Y': 0.41547420620918274}, {'X': 0.37199074029922485, 'Y': 0.42857515811920166}, {'X': 0.06670156121253967, 'Y': 0.42857515811920166}]}, 'Id': '3fb50d62-50b6-403e-b5c4-1f7af5736276', 'Relationships': [{'Type': 'CHILD', 'Ids': ['112aaf03-c42e-4b76-9f08-0e8fd3e8edaa', '8200153c-c95a-4e9a-a52a-2d9d4bff35a7', '4c64f6d9-893a-41ac-99ae-d9c9cb18f6fa', '20078de6-2053-4525-8b8e-963053dd61a6', 'b82ffcce-dee1-4107-9277-6d7db6c82ec6', 'fed22fa1-987a-4fed-87ff-ebe7e2ea52d7', '64cd9046-e486-493e-bf95-af511543be1f']}]} 0.01310095377266407\n", "{'BlockType': 'LINE', 'Confidence': 98.33174133300781, 'Text': '.00', 'Geometry': {'BoundingBox': {'Width': 0.026770949363708496, 'Height': 0.010097384452819824, 'Left': 0.8463836908340454, 'Top': 0.4208473265171051}, 'Polygon': [{'X': 0.8463836908340454, 'Y': 0.4208473265171051}, {'X': 0.8731546401977539, 'Y': 0.4208473265171051}, {'X': 0.8731546401977539, 'Y': 0.4309447109699249}, {'X': 0.8463836908340454, 'Y': 0.4309447109699249}]}, 'Id': 'f94987f5-10eb-45a3-b9b6-f0fd73d19e16', 'Relationships': [{'Type': 'CHILD', 'Ids': ['63675b5c-3123-4205-afa4-7b0a15f1b7a7']}]} 0.010097384452819824\n", "{'BlockType': 'LINE', 'Confidence': 99.80078125, 'Text': '129847', 'Geometry': {'BoundingBox': {'Width': 0.05792144685983658, 'Height': 0.010613799095153809, 'Left': 0.0662701353430748, 'Top': 0.4313230514526367}, 'Polygon': [{'X': 0.0662701353430748, 'Y': 0.4313230514526367}, {'X': 0.12419158220291138, 'Y': 0.4313230514526367}, {'X': 0.12419158220291138, 'Y': 0.4419368505477905}, {'X': 0.0662701353430748, 'Y': 0.4419368505477905}]}, 'Id': '9009871d-86b0-445e-a5d6-e9c2631663d3', 'Relationships': [{'Type': 'CHILD', 'Ids': ['233b7c14-e08f-4adb-8a9c-3c51c20b0630']}]} 0.010613799095153809\n", "{'BlockType': 'LINE', 'Confidence': 99.45433044433594, 'Text': '2', 'Geometry': {'BoundingBox': {'Width': 0.009028282016515732, 'Height': 0.009277135133743286, 'Left': 0.04899875074625015, 'Top': 0.44719913601875305}, 'Polygon': [{'X': 0.04899875074625015, 'Y': 0.44719913601875305}, {'X': 0.058027032762765884, 'Y': 0.44719913601875305}, {'X': 0.058027032762765884, 'Y': 0.45647627115249634}, {'X': 0.04899875074625015, 'Y': 0.45647627115249634}]}, 'Id': 'afd9352a-0381-4e35-aa42-09fc34a2efaa', 'Relationships': [{'Type': 'CHILD', 'Ids': ['afb9f506-580d-469a-bb9e-37f686248854']}]} 0.009277135133743286\n", "{'BlockType': 'LINE', 'Confidence': 98.96736907958984, 'Text': 'WOLF-999', 'Geometry': {'BoundingBox': {'Width': 0.07865457981824875, 'Height': 0.010077595710754395, 'Left': 0.11683479696512222, 'Top': 0.44762662053108215}, 'Polygon': [{'X': 0.11683479696512222, 'Y': 0.44762662053108215}, {'X': 0.19548937678337097, 'Y': 0.44762662053108215}, {'X': 0.19548937678337097, 'Y': 0.45770421624183655}, {'X': 0.11683479696512222, 'Y': 0.45770421624183655}]}, 'Id': '381248b4-8cf6-4067-9095-3754be323a1b', 'Relationships': [{'Type': 'CHILD', 'Ids': ['acc92c3c-3ffc-494c-9861-0f1a50ea1168']}]} 0.010077595710754395\n", "{'BlockType': 'LINE', 'Confidence': 99.7710952758789, 'Text': 'EA', 'Geometry': {'BoundingBox': {'Width': 0.020082980394363403, 'Height': 0.009228527545928955, 'Left': 0.3939174711704254, 'Top': 0.44997453689575195}, 'Polygon': [{'X': 0.3939174711704254, 'Y': 0.44997453689575195}, {'X': 0.4140004515647888, 'Y': 0.44997453689575195}, {'X': 0.4140004515647888, 'Y': 0.4592030644416809}, {'X': 0.3939174711704254, 'Y': 0.4592030644416809}]}, 'Id': '1ae2cb40-f20f-49ce-b183-e64d6935da7d', 'Relationships': [{'Type': 'CHILD', 'Ids': ['76b0428d-5b3b-4609-9d29-32c9e19a587b']}]} 0.009228527545928955\n", "{'BlockType': 'LINE', 'Confidence': 88.6017837524414, 'Text': '1', 'Geometry': {'BoundingBox': {'Width': 0.008718103170394897, 'Height': 0.009465724229812622, 'Left': 0.4921165704727173, 'Top': 0.4502147138118744}, 'Polygon': [{'X': 0.4921165704727173, 'Y': 0.4502147138118744}, {'X': 0.5008347034454346, 'Y': 0.4502147138118744}, {'X': 0.5008347034454346, 'Y': 0.459680438041687}, {'X': 0.4921165704727173, 'Y': 0.459680438041687}]}, 'Id': 'c2b908f0-2faa-438e-ba81-6f86dfd579ac', 'Relationships': [{'Type': 'CHILD', 'Ids': ['6fa88435-ae0f-46c5-bf9b-8bb632153cb3']}]} 0.009465724229812622\n", "{'BlockType': 'LINE', 'Confidence': 99.3584976196289, 'Text': '0', 'Geometry': {'BoundingBox': {'Width': 0.008306741714477539, 'Height': 0.00885346531867981, 'Left': 0.5819917321205139, 'Top': 0.45119524002075195}, 'Polygon': [{'X': 0.5819917321205139, 'Y': 0.45119524002075195}, {'X': 0.5902984738349915, 'Y': 0.45119524002075195}, {'X': 0.5902984738349915, 'Y': 0.46004870533943176}, {'X': 0.5819917321205139, 'Y': 0.46004870533943176}]}, 'Id': 'd9a0b0a8-47fd-40d3-985b-8d770aab92a2', 'Relationships': [{'Type': 'CHILD', 'Ids': ['0461a94d-50e7-4388-977d-93cd3f66eec7']}]} 0.00885346531867981\n", "{'BlockType': 'LINE', 'Confidence': 85.51598358154297, 'Text': '1', 'Geometry': {'BoundingBox': {'Width': 0.008830666542053223, 'Height': 0.008894562721252441, 'Left': 0.6699556708335876, 'Top': 0.4519995450973511}, 'Polygon': [{'X': 0.6699556708335876, 'Y': 0.4519995450973511}, {'X': 0.6787863373756409, 'Y': 0.4519995450973511}, {'X': 0.6787863373756409, 'Y': 0.4608941078186035}, {'X': 0.6699556708335876, 'Y': 0.4608941078186035}]}, 'Id': 'a5ca5c4e-9d93-450b-8032-e27dd64e1d6d', 'Relationships': [{'Type': 'CHILD', 'Ids': ['f8b45591-0d6c-4b1c-ad40-439e8044c798']}]} 0.008894562721252441\n", "{'BlockType': 'LINE', 'Confidence': 97.47160339355469, 'Text': '201.68000 EA', 'Geometry': {'BoundingBox': {'Width': 0.11868789047002792, 'Height': 0.010095124132931232, 'Left': 0.7292703986167908, 'Top': 0.45203784108161926}, 'Polygon': [{'X': 0.7292703986167908, 'Y': 0.45203784108161926}, {'X': 0.8479583263397217, 'Y': 0.45203784108161926}, {'X': 0.8479583263397217, 'Y': 0.4621329605579376}, {'X': 0.7292703986167908, 'Y': 0.4621329605579376}]}, 'Id': '777a81e3-9d31-4edd-a625-132c177e4797', 'Relationships': [{'Type': 'CHILD', 'Ids': ['418ea283-f7da-4828-a99b-2eaf4a553228', '2c2f588e-a118-4314-94df-bac6deb7f0c1']}]} 0.010095124132931232\n", "{'BlockType': 'LINE', 'Confidence': 97.037841796875, 'Text': '<PERSON><PERSON> System - 7 Apron', 'Geometry': {'BoundingBox': {'Width': 0.24546726047992706, 'Height': 0.012305973097682, 'Left': 0.06623001396656036, 'Top': 0.46378734707832336}, 'Polygon': [{'X': 0.06623001396656036, 'Y': 0.46378734707832336}, {'X': 0.3116972744464874, 'Y': 0.46378734707832336}, {'X': 0.3116972744464874, 'Y': 0.4760933220386505}, {'X': 0.06623001396656036, 'Y': 0.4760933220386505}]}, 'Id': '2a95211e-f5a4-471d-b0d6-f024b94b199e', 'Relationships': [{'Type': 'CHILD', 'Ids': ['d2cde3b2-6d25-4287-b406-91269ce872f3', '22e73d30-0991-4d09-9f0d-0562464c0a6a', 'fc36a2b6-ffa0-49cb-81c3-8d3a7383f139', 'a94b7f52-46e4-4d56-a3b9-ba262c2de7c9', '16b9c056-8553-46b4-ad02-63c556f93448', '6ed5caa4-cc1e-45aa-80d1-0d8f5cecf979']}]} 0.012305973097682\n", "{'BlockType': 'LINE', 'Confidence': 98.0019302368164, 'Text': '.00', 'Geometry': {'BoundingBox': {'Width': 0.026824355125427246, 'Height': 0.009680718183517456, 'Left': 0.8460143804550171, 'Top': 0.46877509355545044}, 'Polygon': [{'X': 0.8460143804550171, 'Y': 0.46877509355545044}, {'X': 0.8728387355804443, 'Y': 0.46877509355545044}, {'X': 0.8728387355804443, 'Y': 0.4784558117389679}, {'X': 0.8460143804550171, 'Y': 0.4784558117389679}]}, 'Id': 'c4f9095b-1e5f-4de9-bab3-a57025575d70', 'Relationships': [{'Type': 'CHILD', 'Ids': ['986465bb-8cde-4f14-981d-8f97606d68d7']}]} 0.009680718183517456\n", "{'BlockType': 'LINE', 'Confidence': 99.59545135498047, 'Text': '16427', 'Geometry': {'BoundingBox': {'Width': 0.04876653850078583, 'Height': 0.009972691535949707, 'Left': 0.06567079573869705, 'Top': 0.47896328568458557}, 'Polygon': [{'X': 0.06567079573869705, 'Y': 0.47896328568458557}, {'X': 0.11443733423948288, 'Y': 0.47896328568458557}, {'X': 0.11443733423948288, 'Y': 0.4889359772205353}, {'X': 0.06567079573869705, 'Y': 0.4889359772205353}]}, 'Id': '777fe162-8b45-4514-8de2-555df0a23dbc', 'Relationships': [{'Type': 'CHILD', 'Ids': ['54f292b1-1242-4e33-be24-1d2b93fe235e']}]} 0.009972691535949707\n", "{'BlockType': 'LINE', 'Confidence': 99.67913055419922, 'Text': '3', 'Geometry': {'BoundingBox': {'Width': 0.008067227900028229, 'Height': 0.00851091742515564, 'Left': 0.04864872246980667, 'Top': 0.49491825699806213}, 'Polygon': [{'X': 0.04864872246980667, 'Y': 0.49491825699806213}, {'X': 0.0567159503698349, 'Y': 0.49491825699806213}, {'X': 0.0567159503698349, 'Y': 0.5034291744232178}, {'X': 0.04864872246980667, 'Y': 0.5034291744232178}]}, 'Id': 'fd1f0e6c-e69d-42dc-bb60-b41d80993b03', 'Relationships': [{'Type': 'CHILD', 'Ids': ['20884021-81c1-4f64-a6ab-93ec1ac318a8']}]} 0.00851091742515564\n", "{'BlockType': 'LINE', 'Confidence': 99.26130676269531, 'Text': 'LGSE-999', 'Geometry': {'BoundingBox': {'Width': 0.07664863020181656, 'Height': 0.009528875350952148, 'Left': 0.1180155947804451, 'Top': 0.4951528310775757}, 'Polygon': [{'X': 0.1180155947804451, 'Y': 0.4951528310775757}, {'X': 0.19466422498226166, 'Y': 0.4951528310775757}, {'X': 0.19466422498226166, 'Y': 0.5046817064285278}, {'X': 0.1180155947804451, 'Y': 0.5046817064285278}]}, 'Id': '19eaf1c5-5105-430f-a22a-730f0a489f6c', 'Relationships': [{'Type': 'CHILD', 'Ids': ['26cebffe-1764-4e01-8f19-89e8c615929e']}]} 0.009528875350952148\n", "{'BlockType': 'LINE', 'Confidence': 99.7308578491211, 'Text': 'EA', 'Geometry': {'BoundingBox': {'Width': 0.020398199558258057, 'Height': 0.008798718452453613, 'Left': 0.39342260360717773, 'Top': 0.49754029512405396}, 'Polygon': [{'X': 0.39342260360717773, 'Y': 0.49754029512405396}, {'X': 0.4138208031654358, 'Y': 0.49754029512405396}, {'X': 0.4138208031654358, 'Y': 0.5063390135765076}, {'X': 0.39342260360717773, 'Y': 0.5063390135765076}]}, 'Id': '62bddec2-b80a-4881-bda1-daa16fb9049a', 'Relationships': [{'Type': 'CHILD', 'Ids': ['b1b8fe80-e903-4f9d-b629-7e60ca218163']}]} 0.008798718452453613\n", "{'BlockType': 'LINE', 'Confidence': 98.01951599121094, 'Text': '1', 'Geometry': {'BoundingBox': {'Width': 0.009187579154968262, 'Height': 0.008977949619293213, 'Left': 0.49128779768943787, 'Top': 0.49795496463775635}, 'Polygon': [{'X': 0.49128779768943787, 'Y': 0.49795496463775635}, {'X': 0.5004754066467285, 'Y': 0.49795496463775635}, {'X': 0.5004754066467285, 'Y': 0.5069329142570496}, {'X': 0.49128779768943787, 'Y': 0.5069329142570496}]}, 'Id': '896e77b1-763c-49f7-b87f-0f789d1c2e88', 'Relationships': [{'Type': 'CHILD', 'Ids': ['bd0a7dc5-20d6-498d-897f-eb3a1671e6ba']}]} 0.008977949619293213\n", "{'BlockType': 'LINE', 'Confidence': 99.6795425415039, 'Text': '0', 'Geometry': {'BoundingBox': {'Width': 0.007967114448547363, 'Height': 0.008382648229598999, 'Left': 0.5813584923744202, 'Top': 0.49878624081611633}, 'Polygon': [{'X': 0.5813584923744202, 'Y': 0.49878624081611633}, {'X': 0.5893256068229675, 'Y': 0.49878624081611633}, {'X': 0.5893256068229675, 'Y': 0.5071688890457153}, {'X': 0.5813584923744202, 'Y': 0.5071688890457153}]}, 'Id': '950cec71-cbf1-46f4-8261-f312fc327c91', 'Relationships': [{'Type': 'CHILD', 'Ids': ['ef4074f4-c491-43f5-86a7-6e3fba976cf3']}]} 0.008382648229598999\n", "{'BlockType': 'LINE', 'Confidence': 89.53162384033203, 'Text': '1', 'Geometry': {'BoundingBox': {'Width': 0.009180009365081787, 'Height': 0.008125752210617065, 'Left': 0.6692103147506714, 'Top': 0.4998123347759247}, 'Polygon': [{'X': 0.6692103147506714, 'Y': 0.4998123347759247}, {'X': 0.6783903241157532, 'Y': 0.4998123347759247}, {'X': 0.6783903241157532, 'Y': 0.5079380869865417}, {'X': 0.6692103147506714, 'Y': 0.5079380869865417}]}, 'Id': '78b6f805-fb7e-4f1c-a4f0-685958dee017', 'Relationships': [{'Type': 'CHILD', 'Ids': ['d48f6377-693d-45a6-8fa3-e56062bf5ef9']}]} 0.008125752210617065\n", "{'BlockType': 'LINE', 'Confidence': 96.8361587524414, 'Text': '18.78000 EA', 'Geometry': {'BoundingBox': {'Width': 0.10461191087961197, 'Height': 0.009876255877315998, 'Left': 0.7420734167098999, 'Top': 0.4994523525238037}, 'Polygon': [{'X': 0.7420734167098999, 'Y': 0.4994523525238037}, {'X': 0.8466852903366089, 'Y': 0.4994523525238037}, {'X': 0.8466852903366089, 'Y': 0.5093286037445068}, {'X': 0.7420734167098999, 'Y': 0.5093286037445068}]}, 'Id': '900027c9-9f63-4ce1-bff7-cac5a8e02047', 'Relationships': [{'Type': 'CHILD', 'Ids': ['ea3bbf5b-b7eb-43bb-be83-5c482472ad29', '14e68b56-205b-4483-a4dd-a99de8396dfd']}]} 0.009876255877315998\n", "{'BlockType': 'LINE', 'Confidence': 99.52444458007812, 'Text': 'Magnetic Dry-Erase Board,', 'Geometry': {'BoundingBox': {'Width': 0.24330484867095947, 'Height': 0.012365372851490974, 'Left': 0.0641254261136055, 'Top': 0.5103409290313721}, 'Polygon': [{'X': 0.0641254261136055, 'Y': 0.5103409290313721}, {'X': 0.3074302673339844, 'Y': 0.5103409290313721}, {'X': 0.3074302673339844, 'Y': 0.5227062702178955}, {'X': 0.0641254261136055, 'Y': 0.5227062702178955}]}, 'Id': '91d0cb71-af1d-4021-8392-bea1da2f7ee4', 'Relationships': [{'Type': 'CHILD', 'Ids': ['6b296639-f8ca-40a7-8708-57c4a971c790', 'a3a060d9-18f7-471b-a65a-c9250dc873ed', 'a25f1426-a4ef-4ce2-b4f1-7ca7d35e7c4b']}]} 0.012365372851490974\n", "{'BlockType': 'LINE', 'Confidence': 96.33416748046875, 'Text': '.00', 'Geometry': {'BoundingBox': {'Width': 0.026713669300079346, 'Height': 0.01044696569442749, 'Left': 0.8456724882125854, 'Top': 0.5155492424964905}, 'Polygon': [{'X': 0.8456724882125854, 'Y': 0.5155492424964905}, {'X': 0.8723861575126648, 'Y': 0.5155492424964905}, {'X': 0.8723861575126648, 'Y': 0.525996208190918}, {'X': 0.8456724882125854, 'Y': 0.525996208190918}]}, 'Id': '56ff5b51-c550-4c1c-b1d7-df188311003e', 'Relationships': [{'Type': 'CHILD', 'Ids': ['6fac9fa7-b2fa-46cc-9a95-518c11d65cf3']}]} 0.01044696569442749\n", "{'BlockType': 'LINE', 'Confidence': 99.7085952758789, 'Text': '981164', 'Geometry': {'BoundingBox': {'Width': 0.05759917199611664, 'Height': 0.010079383850097656, 'Left': 0.06552312523126602, 'Top': 0.5259988903999329}, 'Polygon': [{'X': 0.06552312523126602, 'Y': 0.5259988903999329}, {'X': 0.12312229722738266, 'Y': 0.5259988903999329}, {'X': 0.12312229722738266, 'Y': 0.5360782742500305}, {'X': 0.06552312523126602, 'Y': 0.5360782742500305}]}, 'Id': '70471186-f79e-4573-b3e7-45ff9aa62ead', 'Relationships': [{'Type': 'CHILD', 'Ids': ['bab25f92-6a8b-48fa-84ad-0f4de2b560af']}]} 0.010079383850097656\n", "{'BlockType': 'LINE', 'Confidence': 99.25467681884766, 'Text': '4', 'Geometry': {'BoundingBox': {'Width': 0.00884561613202095, 'Height': 0.00921410322189331, 'Left': 0.04812128096818924, 'Top': 0.5411694049835205}, 'Polygon': [{'X': 0.04812128096818924, 'Y': 0.5411694049835205}, {'X': 0.05696689710021019, 'Y': 0.5411694049835205}, {'X': 0.05696689710021019, 'Y': 0.5503835082054138}, {'X': 0.04812128096818924, 'Y': 0.5503835082054138}]}, 'Id': '7e5ff194-cfd4-42aa-87d7-b8c5b6163aa3', 'Relationships': [{'Type': 'CHILD', 'Ids': ['e6c6fbc4-3480-4051-af4e-194a8cd2db5e']}]} 0.00921410322189331\n", "{'BlockType': 'LINE', 'Confidence': 96.3696060180664, 'Text': 'CESS-90940-00', 'Geometry': {'BoundingBox': {'Width': 0.12720996141433716, 'Height': 0.009906530380249023, 'Left': 0.11669771373271942, 'Top': 0.541917622089386}, 'Polygon': [{'X': 0.11669771373271942, 'Y': 0.541917622089386}, {'X': 0.24390767514705658, 'Y': 0.541917622089386}, {'X': 0.24390767514705658, 'Y': 0.551824152469635}, {'X': 0.11669771373271942, 'Y': 0.551824152469635}]}, 'Id': '8123e94e-369e-4869-9b0c-cd6a2486a7ed', 'Relationships': [{'Type': 'CHILD', 'Ids': ['964b01bd-23ac-4f13-9402-47aae6fdd2bb']}]} 0.009906530380249023\n", "{'BlockType': 'LINE', 'Confidence': 99.86363983154297, 'Text': 'EA', 'Geometry': {'BoundingBox': {'Width': 0.020255565643310547, 'Height': 0.00900965929031372, 'Left': 0.39333710074424744, 'Top': 0.5441679358482361}, 'Polygon': [{'X': 0.39333710074424744, 'Y': 0.5441679358482361}, {'X': 0.413592666387558, 'Y': 0.5441679358482361}, {'X': 0.413592666387558, 'Y': 0.5531775951385498}, {'X': 0.39333710074424744, 'Y': 0.5531775951385498}]}, 'Id': 'b9acaa6c-ce03-412a-b775-f0299578027c', 'Relationships': [{'Type': 'CHILD', 'Ids': ['06c81238-9b06-4999-8baf-27ce8a02e776']}]} 0.00900965929031372\n", "{'BlockType': 'LINE', 'Confidence': 88.18289947509766, 'Text': 'I', 'Geometry': {'BoundingBox': {'Width': 0.008205592632293701, 'Height': 0.00901883840560913, 'Left': 0.4915327727794647, 'Top': 0.5447347164154053}, 'Polygon': [{'X': 0.4915327727794647, 'Y': 0.5447347164154053}, {'X': 0.4997383654117584, 'Y': 0.5447347164154053}, {'X': 0.4997383654117584, 'Y': 0.5537535548210144}, {'X': 0.4915327727794647, 'Y': 0.5537535548210144}]}, 'Id': '120838df-09e1-40fa-b9d7-63cae0c0b972', 'Relationships': [{'Type': 'CHILD', 'Ids': ['d89f0e49-f4d4-4737-9fbf-8c388f6f3047']}]} 0.00901883840560913\n", "{'BlockType': 'LINE', 'Confidence': 99.74400329589844, 'Text': '0', 'Geometry': {'BoundingBox': {'Width': 0.008472025394439697, 'Height': 0.008736789226531982, 'Left': 0.6696189045906067, 'Top': 0.5459505319595337}, 'Polygon': [{'X': 0.6696189045906067, 'Y': 0.5459505319595337}, {'X': 0.6780909299850464, 'Y': 0.5459505319595337}, {'X': 0.6780909299850464, 'Y': 0.5546873211860657}, {'X': 0.6696189045906067, 'Y': 0.5546873211860657}]}, 'Id': '9e69fd8f-9f57-48fd-bd53-562bd8680701', 'Relationships': [{'Type': 'CHILD', 'Ids': ['fb9bdff9-62cc-4d3a-8352-911ad1a2cd7c']}]} 0.008736789226531982\n", "{'BlockType': 'LINE', 'Confidence': 87.81585693359375, 'Text': '84.07000 EA', 'Geometry': {'BoundingBox': {'Width': 0.10442709177732468, 'Height': 0.010106571950018406, 'Left': 0.7420849204063416, 'Top': 0.5462911128997803}, 'Polygon': [{'X': 0.7420849204063416, 'Y': 0.5462911128997803}, {'X': 0.8465120196342468, 'Y': 0.5462911128997803}, {'X': 0.8465120196342468, 'Y': 0.5563976764678955}, {'X': 0.7420849204063416, 'Y': 0.5563976764678955}]}, 'Id': 'b2b41ab0-4407-4ca6-988c-ce3e7fa331f4', 'Relationships': [{'Type': 'CHILD', 'Ids': ['cdab56a4-e005-485e-9da2-352762749001', '943525be-603d-4ef0-a89d-cf77fe52fcbe']}]} 0.010106571950018406\n", "{'BlockType': 'LINE', 'Confidence': 95.93769836425781, 'Text': 'VERTICAL WALL MT,ADJ PEGS, SOLID', 'Geometry': {'BoundingBox': {'Width': 0.3066609799861908, 'Height': 0.012158136814832687, 'Left': 0.06403088569641113, 'Top': 0.5574459433555603}, 'Polygon': [{'X': 0.06403088569641113, 'Y': 0.5574459433555603}, {'X': 0.37069186568260193, 'Y': 0.5574459433555603}, {'X': 0.37069186568260193, 'Y': 0.5696040987968445}, {'X': 0.06403088569641113, 'Y': 0.5696040987968445}]}, 'Id': 'dc9ed9f1-10d6-48f2-96e0-13159ba439ec', 'Relationships': [{'Type': 'CHILD', 'Ids': ['71777478-283f-46f3-9665-9b017300add8', '0d54644a-f160-421b-b742-e703bb543128', 'f9821da8-3c36-4151-8c37-1014157923c0', '803b73f7-cd85-47c4-8b06-6f2f518df27b', 'a184d394-217d-49a7-9eff-82a8d3e618c7']}]} 0.012158136814832687\n", "{'BlockType': 'LINE', 'Confidence': 99.55607604980469, 'Text': '84.07', 'Geometry': {'BoundingBox': {'Width': 0.047751665115356445, 'Height': 0.009956061840057373, 'Left': 0.8238878846168518, 'Top': 0.5623326897621155}, 'Polygon': [{'X': 0.8238878846168518, 'Y': 0.5623326897621155}, {'X': 0.8716395497322083, 'Y': 0.5623326897621155}, {'X': 0.8716395497322083, 'Y': 0.5722887516021729}, {'X': 0.8238878846168518, 'Y': 0.5722887516021729}]}, 'Id': '02420b2d-1263-4475-a03d-b93e3787f6b4', 'Relationships': [{'Type': 'CHILD', 'Ids': ['dba735f5-1f08-4388-bf09-ddae27749833']}]} 0.009956061840057373\n", "{'BlockType': 'LINE', 'Confidence': 99.5295639038086, 'Text': 'OAK CLR FINISH, INCLUDES HDWR', 'Geometry': {'BoundingBox': {'Width': 0.2770812213420868, 'Height': 0.011638945899903774, 'Left': 0.06418497115373611, 'Top': 0.5731295347213745}, 'Polygon': [{'X': 0.06418497115373611, 'Y': 0.5731295347213745}, {'X': 0.3412661850452423, 'Y': 0.5731295347213745}, {'X': 0.3412661850452423, 'Y': 0.5847684741020203}, {'X': 0.06418497115373611, 'Y': 0.5847684741020203}]}, 'Id': 'e534ef97-74f6-49f1-aeba-f41d5e36d6c0', 'Relationships': [{'Type': 'CHILD', 'Ids': ['fe12f93e-3c20-4fb3-9ff9-78121a2d348e', '29b9ecd3-f45d-4091-9996-6c7515ae0a57', 'fa1aa7e2-a22c-4a81-b9ef-858071229161', '43bde109-3db6-493e-9fd9-216779b182d6', 'a68e501f-6233-43ae-8790-a862f8ac285b']}]} 0.011638945899903774\n", "{'BlockType': 'LINE', 'Confidence': 99.54679107666016, 'Text': '5', 'Geometry': {'BoundingBox': {'Width': 0.008480239659547806, 'Height': 0.008777856826782227, 'Left': 0.04800964519381523, 'Top': 0.5884737968444824}, 'Polygon': [{'X': 0.04800964519381523, 'Y': 0.5884737968444824}, {'X': 0.05648988485336304, 'Y': 0.5884737968444824}, {'X': 0.05648988485336304, 'Y': 0.5972516536712646}, {'X': 0.04800964519381523, 'Y': 0.5972516536712646}]}, 'Id': '5787a9d8-e118-4303-a87e-974368ca87e7', 'Relationships': [{'Type': 'CHILD', 'Ids': ['0d10a182-2d7c-4f9d-91c1-ab9307752c00']}]} 0.008777856826782227\n", "{'BlockType': 'LINE', 'Confidence': 98.05520629882812, 'Text': 'BARY-999', 'Geometry': {'BoundingBox': {'Width': 0.075166255235672, 'Height': 0.009720385074615479, 'Left': 0.11685747653245926, 'Top': 0.5885488390922546}, 'Polygon': [{'X': 0.11685747653245926, 'Y': 0.5885488390922546}, {'X': 0.19202372431755066, 'Y': 0.5885488390922546}, {'X': 0.19202372431755066, 'Y': 0.5982692241668701}, {'X': 0.11685747653245926, 'Y': 0.5982692241668701}]}, 'Id': '00b2db5f-4d79-4a1e-be53-3f3266cfd6f1', 'Relationships': [{'Type': 'CHILD', 'Ids': ['398cca43-3cbe-4a5e-bf9c-97c3a6134dd4']}]} 0.009720385074615479\n", "{'BlockType': 'LINE', 'Confidence': 99.7975082397461, 'Text': 'EA', 'Geometry': {'BoundingBox': {'Width': 0.0195140540599823, 'Height': 0.009035110473632812, 'Left': 0.392831951379776, 'Top': 0.5908171534538269}, 'Polygon': [{'X': 0.392831951379776, 'Y': 0.5908171534538269}, {'X': 0.4123460054397583, 'Y': 0.5908171534538269}, {'X': 0.4123460054397583, 'Y': 0.5998522639274597}, {'X': 0.392831951379776, 'Y': 0.5998522639274597}]}, 'Id': '53747d66-9155-4808-809c-01334f0e03ce', 'Relationships': [{'Type': 'CHILD', 'Ids': ['3759ba67-7417-4be6-9f43-28c42fbb3082']}]} 0.009035110473632812\n", "{'BlockType': 'LINE', 'Confidence': 93.91143035888672, 'Text': '1', 'Geometry': {'BoundingBox': {'Width': 0.008965879678726196, 'Height': 0.008608043193817139, 'Left': 0.49083518981933594, 'Top': 0.5919180512428284}, 'Polygon': [{'X': 0.49083518981933594, 'Y': 0.5919180512428284}, {'X': 0.49980106949806213, 'Y': 0.5919180512428284}, {'X': 0.49980106949806213, 'Y': 0.6005260944366455}, {'X': 0.49083518981933594, 'Y': 0.6005260944366455}]}, 'Id': '9b1cac54-89bf-4372-982b-aa5099375976', 'Relationships': [{'Type': 'CHILD', 'Ids': ['9ef29f73-d6d4-4f69-b090-ac89075697b1']}]} 0.008608043193817139\n", "{'BlockType': 'LINE', 'Confidence': 64.75106811523438, 'Text': '0', 'Geometry': {'BoundingBox': {'Width': 0.007858335971832275, 'Height': 0.008568823337554932, 'Left': 0.580552875995636, 'Top': 0.592190682888031}, 'Polygon': [{'X': 0.580552875995636, 'Y': 0.592190682888031}, {'X': 0.5884112119674683, 'Y': 0.592190682888031}, {'X': 0.5884112119674683, 'Y': 0.6007595062255859}, {'X': 0.580552875995636, 'Y': 0.6007595062255859}]}, 'Id': '8ae166f3-607e-4ff7-88ff-8b091f6ea7d7', 'Relationships': [{'Type': 'CHILD', 'Ids': ['ae137193-b321-4c65-886b-2b0e69b93d98']}]} 0.008568823337554932\n", "{'BlockType': 'LINE', 'Confidence': 99.73619842529297, 'Text': '1', 'Geometry': {'BoundingBox': {'Width': 0.008664131164550781, 'Height': 0.008552908897399902, 'Left': 0.6688703894615173, 'Top': 0.593028724193573}, 'Polygon': [{'X': 0.6688703894615173, 'Y': 0.593028724193573}, {'X': 0.6775345206260681, 'Y': 0.593028724193573}, {'X': 0.6775345206260681, 'Y': 0.6015816330909729}, {'X': 0.6688703894615173, 'Y': 0.6015816330909729}]}, 'Id': 'f55de465-57b5-4053-b291-b27ac411e425', 'Relationships': [{'Type': 'CHILD', 'Ids': ['fbe6f917-808c-46fd-b35a-c1150e6e3fc6']}]} 0.008552908897399902\n", "{'BlockType': 'LINE', 'Confidence': 96.7802734375, 'Text': '362.72000 EA', 'Geometry': {'BoundingBox': {'Width': 0.11703432351350784, 'Height': 0.009900339879095554, 'Left': 0.728845477104187, 'Top': 0.5930556058883667}, 'Polygon': [{'X': 0.728845477104187, 'Y': 0.5930556058883667}, {'X': 0.845879852771759, 'Y': 0.5930556058883667}, {'X': 0.845879852771759, 'Y': 0.6029559373855591}, {'X': 0.728845477104187, 'Y': 0.6029559373855591}]}, 'Id': 'a9c1e8b9-5ffc-4fd3-a93b-bceaf86b8c47', 'Relationships': [{'Type': 'CHILD', 'Ids': ['696afa20-0d11-4d58-abde-e4bcce902a7f', '25c6b06d-6c69-49c0-b3c5-2cda3889d040']}]} 0.009900339879095554\n", "{'BlockType': 'LINE', 'Confidence': 99.53331756591797, 'Text': '<PERSON> <PERSON><PERSON> Rack, Folding Left', 'Geometry': {'BoundingBox': {'Width': 0.2853317856788635, 'Height': 0.013451894745230675, 'Left': 0.06417972594499588, 'Top': 0.6036310195922852}, 'Polygon': [{'X': 0.06417972594499588, 'Y': 0.6036310195922852}, {'X': 0.3495115041732788, 'Y': 0.6036310195922852}, {'X': 0.3495115041732788, 'Y': 0.6170828938484192}, {'X': 0.06417972594499588, 'Y': 0.6170828938484192}]}, 'Id': '9aeaabe3-fc56-44bf-be82-8166b6e33c9f', 'Relationships': [{'Type': 'CHILD', 'Ids': ['35477e9a-430d-4814-ba3a-2e940baed2ef', '9478959a-0d89-479f-9838-95abb8e42c98', '7f9a89f1-efbe-4c08-b141-f06bf1f80694', 'cd105930-05b1-4de2-a1c5-d2203471d7e4', '0f5fc9c1-067b-498e-9bf7-178dcfaefa33']}]} 0.013451894745230675\n", "{'BlockType': 'LINE', 'Confidence': 96.85115051269531, 'Text': '.00', 'Geometry': {'BoundingBox': {'Width': 0.027882277965545654, 'Height': 0.009966909885406494, 'Left': 0.8434849977493286, 'Top': 0.6095300316810608}, 'Polygon': [{'X': 0.8434849977493286, 'Y': 0.6095300316810608}, {'X': 0.8713672757148743, 'Y': 0.6095300316810608}, {'X': 0.8713672757148743, 'Y': 0.6194969415664673}, {'X': 0.8434849977493286, 'Y': 0.6194969415664673}]}, 'Id': '164182df-bf1f-4cb2-840c-19ccb6c0b037', 'Relationships': [{'Type': 'CHILD', 'Ids': ['56175396-31a8-4c61-b8ba-970333258a0c']}]} 0.009966909885406494\n", "{'BlockType': 'LINE', 'Confidence': 99.32791900634766, 'Text': '66685', 'Geometry': {'BoundingBox': {'Width': 0.04843297600746155, 'Height': 0.00984799861907959, 'Left': 0.0647197961807251, 'Top': 0.6196815371513367}, 'Polygon': [{'X': 0.0647197961807251, 'Y': 0.6196815371513367}, {'X': 0.11315277218818665, 'Y': 0.6196815371513367}, {'X': 0.11315277218818665, 'Y': 0.6295295357704163}, {'X': 0.0647197961807251, 'Y': 0.6295295357704163}]}, 'Id': '98960465-ade1-4c5f-ab23-dfb5acdc7c86', 'Relationships': [{'Type': 'CHILD', 'Ids': ['386c101b-6537-4767-a64b-48933a2e3cdc']}]} 0.00984799861907959\n", "{'BlockType': 'LINE', 'Confidence': 98.5881118774414, 'Text': 'Freight Charges', 'Geometry': {'BoundingBox': {'Width': 0.14763452112674713, 'Height': 0.01257980614900589, 'Left': 0.0439467690885067, 'Top': 0.6355158090591431}, 'Polygon': [{'X': 0.0439467690885067, 'Y': 0.6355158090591431}, {'X': 0.19158127903938293, 'Y': 0.6355158090591431}, {'X': 0.19158127903938293, 'Y': 0.6480956077575684}, {'X': 0.0439467690885067, 'Y': 0.6480956077575684}]}, 'Id': '0350478b-3275-4506-9f76-6f8bf5f02e93', 'Relationships': [{'Type': 'CHILD', 'Ids': ['ef24023c-499c-48ad-8659-4c21ecd70556', 'f7a0fa0b-48b5-4ec2-b30d-7b7c0016408a']}]} 0.01257980614900589\n", "{'BlockType': 'LINE', 'Confidence': 99.76875305175781, 'Text': 'ESTIMATED', 'Geometry': {'BoundingBox': {'Width': 0.08952996134757996, 'Height': 0.009509265422821045, 'Left': 0.3498607277870178, 'Top': 0.6385302543640137}, 'Polygon': [{'X': 0.3498607277870178, 'Y': 0.6385302543640137}, {'X': 0.4393906891345978, 'Y': 0.6385302543640137}, {'X': 0.4393906891345978, 'Y': 0.6480395197868347}, {'X': 0.3498607277870178, 'Y': 0.6480395197868347}]}, 'Id': '7aa0f081-fd2e-4f10-82aa-95fee890b15a', 'Relationships': [{'Type': 'CHILD', 'Ids': ['21c1d883-c7d4-4764-88cf-e3abfd8f53aa']}]} 0.009509265422821045\n", "{'BlockType': 'LINE', 'Confidence': 97.96550750732422, 'Text': '137.17', 'Geometry': {'BoundingBox': {'Width': 0.057327985763549805, 'Height': 0.009956061840057373, 'Left': 0.8036206960678101, 'Top': 0.6411967277526855}, 'Polygon': [{'X': 0.8036206960678101, 'Y': 0.6411967277526855}, {'X': 0.8609486818313599, 'Y': 0.6411967277526855}, {'X': 0.8609486818313599, 'Y': 0.6511527895927429}, {'X': 0.8036206960678101, 'Y': 0.6511527895927429}]}, 'Id': 'e37748ed-7fe0-4916-8a50-395c80ec0628', 'Relationships': [{'Type': 'CHILD', 'Ids': ['0eb1f33e-d1c1-4909-83fb-eff120b5ff3e']}]} 0.009956061840057373\n", "{'BlockType': 'LINE', 'Confidence': 91.10680389404297, 'Text': 'CONTACT FOR DELIVERY:', 'Geometry': {'BoundingBox': {'Width': 0.20184293389320374, 'Height': 0.010622705332934856, 'Left': 0.043380480259656906, 'Top': 0.6520165205001831}, 'Polygon': [{'X': 0.043380480259656906, 'Y': 0.6520165205001831}, {'X': 0.24522340297698975, 'Y': 0.6520165205001831}, {'X': 0.24522340297698975, 'Y': 0.6626392006874084}, {'X': 0.043380480259656906, 'Y': 0.6626392006874084}]}, 'Id': '2a9d0bb1-15c8-4f83-9158-c4b458a56152', 'Relationships': [{'Type': 'CHILD', 'Ids': ['f9519250-afab-44a4-b877-c45e39e02904', 'fec7e16f-3d05-41e8-8356-ec031e7e48cb', '0ef13ebd-5864-4b98-8576-ea4f67b03fd6']}]} 0.010622705332934856\n", "{'BlockType': 'LINE', 'Confidence': 99.50658416748047, 'Text': 'DAWN MCGOWAN', 'Geometry': {'BoundingBox': {'Width': 0.11879725009202957, 'Height': 0.009919645264744759, 'Left': 0.04373892769217491, 'Top': 0.6679951548576355}, 'Polygon': [{'X': 0.04373892769217491, 'Y': 0.6679951548576355}, {'X': 0.16253617405891418, 'Y': 0.6679951548576355}, {'X': 0.16253617405891418, 'Y': 0.6779147982597351}, {'X': 0.04373892769217491, 'Y': 0.6779147982597351}]}, 'Id': 'e55cdb10-2dc9-4187-abd0-25a61f7f4cf6', 'Relationships': [{'Type': 'CHILD', 'Ids': ['2368155f-a328-4491-9574-50280c76cacb', '977c12a5-b7b4-474a-8770-de721594af48']}]} 0.009919645264744759\n", "{'BlockType': 'LINE', 'Confidence': 99.32040405273438, 'Text': '443-287-9038', 'Geometry': {'BoundingBox': {'Width': 0.11827480792999268, 'Height': 0.010201811790466309, 'Left': 0.04433920979499817, 'Top': 0.6834139823913574}, 'Polygon': [{'X': 0.04433920979499817, 'Y': 0.6834139823913574}, {'X': 0.16261401772499084, 'Y': 0.6834139823913574}, {'X': 0.16261401772499084, 'Y': 0.6936157941818237}, {'X': 0.04433920979499817, 'Y': 0.6936157941818237}]}, 'Id': 'e53d0c02-c792-4df2-850d-2d6567fbf555', 'Relationships': [{'Type': 'CHILD', 'Ids': ['047f154b-4bf9-47db-a58b-1ae7e9f5eec7']}]} 0.010201811790466309\n", "{'BlockType': 'LINE', 'Confidence': 98.6249008178711, 'Text': 'INVOICE DUE: 07/04/20', 'Geometry': {'BoundingBox': {'Width': 0.20717620849609375, 'Height': 0.01115397922694683, 'Left': 0.400969535112381, 'Top': 0.701345682144165}, 'Polygon': [{'X': 0.400969535112381, 'Y': 0.701345682144165}, {'X': 0.6081457734107971, 'Y': 0.701345682144165}, {'X': 0.6081457734107971, 'Y': 0.7124996781349182}, {'X': 0.400969535112381, 'Y': 0.7124996781349182}]}, 'Id': 'f2bc129c-5c1a-4cb0-bc77-17c1073ced89', 'Relationships': [{'Type': 'CHILD', 'Ids': ['c87e9d5e-c65c-4240-9af9-4498f75c1e33', '6f1b93f4-e51a-48b1-885f-98c044ba2e28', 'f5c08f84-a995-437b-ae97-cd9e99037638']}]} 0.01115397922694683\n", "{'BlockType': 'LINE', 'Confidence': 99.59651184082031, 'Text': 'Thank you for your order.', 'Geometry': {'BoundingBox': {'Width': 0.2445940375328064, 'Height': 0.012499678879976273, 'Left': 0.02408781461417675, 'Top': 0.8353400826454163}, 'Polygon': [{'X': 0.02408781461417675, 'Y': 0.8353400826454163}, {'X': 0.2686818540096283, 'Y': 0.8353400826454163}, {'X': 0.2686818540096283, 'Y': 0.8478397727012634}, {'X': 0.02408781461417675, 'Y': 0.8478397727012634}]}, 'Id': '77b14483-dfdc-4a8f-8441-8dd7d5ed2ac2', 'Relationships': [{'Type': 'CHILD', 'Ids': ['76a02a15-ecb2-4e54-80c4-f9fdcfa9e642', '8013abb3-d089-4a1b-8248-39992a51da3d', 'd1fd5791-927a-4119-9ad8-6950ed301465', 'c13927dc-1a24-4da1-99f3-5d29c4c0d750', '0f80d01d-a0d4-4f60-a5b1-dda4f9bf9c99']}]} 0.012499678879976273\n", "{'BlockType': 'LINE', 'Confidence': 99.73908996582031, 'Text': 'Remit to:', 'Geometry': {'BoundingBox': {'Width': 0.08804970979690552, 'Height': 0.010216771624982357, 'Left': 0.023754730820655823, 'Top': 0.8606145977973938}, 'Polygon': [{'X': 0.023754730820655823, 'Y': 0.8606145977973938}, {'X': 0.11180444061756134, 'Y': 0.8606145977973938}, {'X': 0.11180444061756134, 'Y': 0.8708313703536987}, {'X': 0.023754730820655823, 'Y': 0.8708313703536987}]}, 'Id': 'fc58d1d3-3a20-4740-b966-175ce2531477', 'Relationships': [{'Type': 'CHILD', 'Ids': ['00b84d85-90f2-4eb7-b90d-b71d9d03dd32', 'c5dc3b35-32a8-4cdd-838d-bd761d19c47f']}]} 0.010216771624982357\n", "{'BlockType': 'LINE', 'Confidence': 93.27802276611328, 'Text': 'SUBTOTAL:', 'Geometry': {'BoundingBox': {'Width': 0.0863199234008789, 'Height': 0.010100126266479492, 'Left': 0.5345553755760193, 'Top': 0.8676968812942505}, 'Polygon': [{'X': 0.5345553755760193, 'Y': 0.8676968812942505}, {'X': 0.6208752989768982, 'Y': 0.8676968812942505}, {'X': 0.6208752989768982, 'Y': 0.87779700756073}, {'X': 0.5345553755760193, 'Y': 0.87779700756073}]}, 'Id': 'fc7cc7c4-dbed-4c46-8a61-4e81f63678e2', 'Relationships': [{'Type': 'CHILD', 'Ids': ['36833997-8d5f-4f93-9700-bc8a01a0a46e']}]} 0.010100126266479492\n", "{'BlockType': 'LINE', 'Confidence': 99.28219604492188, 'Text': 'CME', 'Geometry': {'BoundingBox': {'Width': 0.030288228765130043, 'Height': 0.00867694616317749, 'Left': 0.02426537126302719, 'Top': 0.8739368319511414}, 'Polygon': [{'X': 0.02426537126302719, 'Y': 0.8739368319511414}, {'X': 0.05455360189080238, 'Y': 0.8739368319511414}, {'X': 0.05455360189080238, 'Y': 0.8826137781143188}, {'X': 0.02426537126302719, 'Y': 0.8826137781143188}]}, 'Id': '81d9007b-284e-4f7c-b8d4-fb048714c0c7', 'Relationships': [{'Type': 'CHILD', 'Ids': ['5da9e26b-0b15-4a9c-8c43-9c945bbae9ce']}]} 0.00867694616317749\n", "{'BlockType': 'LINE', 'Confidence': 98.6235122680664, 'Text': '221.24', 'Geometry': {'BoundingBox': {'Width': 0.058124005794525146, 'Height': 0.010640978813171387, 'Left': 0.8103728890419006, 'Top': 0.8690940737724304}, 'Polygon': [{'X': 0.8103728890419006, 'Y': 0.8690940737724304}, {'X': 0.8684968948364258, 'Y': 0.8690940737724304}, {'X': 0.8684968948364258, 'Y': 0.8797350525856018}, {'X': 0.8103728890419006, 'Y': 0.8797350525856018}]}, 'Id': '5833e77c-0b87-476f-a35e-bbb83d4dbde7', 'Relationships': [{'Type': 'CHILD', 'Ids': ['37a32e7b-9a3a-4bfc-839f-f9d6dd70cf77']}]} 0.010640978813171387\n", "{'BlockType': 'LINE', 'Confidence': 98.90738677978516, 'Text': 'P.O. Box 6887', 'Geometry': {'BoundingBox': {'Width': 0.12790873646736145, 'Height': 0.00963225681334734, 'Left': 0.024020498618483543, 'Top': 0.8867853283882141}, 'Polygon': [{'X': 0.024020498618483543, 'Y': 0.8867853283882141}, {'X': 0.15192922949790955, 'Y': 0.8867853283882141}, {'X': 0.15192922949790955, 'Y': 0.8964175581932068}, {'X': 0.024020498618483543, 'Y': 0.8964175581932068}]}, 'Id': 'f5849f11-e898-4ac9-9b62-5b540302dec7', 'Relationships': [{'Type': 'CHILD', 'Ids': ['8ee5a406-f5f0-48db-923f-50d22b108696', '6dfb19a7-f950-42ec-b767-25c22c536673', '382b540f-363d-4afb-94c2-6f2d4fade139']}]} 0.00963225681334734\n", "{'BlockType': 'LINE', 'Confidence': 99.34194946289062, 'Text': 'Warwick, RI 02887', 'Geometry': {'BoundingBox': {'Width': 0.16828323900699615, 'Height': 0.010691404342651367, 'Left': 0.023296047002077103, 'Top': 0.898752748966217}, 'Polygon': [{'X': 0.023296047002077103, 'Y': 0.898752748966217}, {'X': 0.19157928228378296, 'Y': 0.898752748966217}, {'X': 0.19157928228378296, 'Y': 0.9094441533088684}, {'X': 0.023296047002077103, 'Y': 0.9094441533088684}]}, 'Id': 'f4eed26f-59f5-47f5-9e39-7623b3a4f9a5', 'Relationships': [{'Type': 'CHILD', 'Ids': ['a150a55c-6b57-4e28-8530-ea1bc41552f8', '0c05791d-1dc8-4700-b03c-3e447ab12c42', '338a12ca-e15c-41db-9fd2-e5b39073ca93']}]} 0.010691404342651367\n", "{'BlockType': 'LINE', 'Confidence': 92.99307250976562, 'Text': 'Questions: 1-800-338-2372 -', 'Geometry': {'BoundingBox': {'Width': 0.24573281407356262, 'Height': 0.01069571077823639, 'Left': 0.024221936240792274, 'Top': 0.9113709926605225}, 'Polygon': [{'X': 0.024221936240792274, 'Y': 0.9113709926605225}, {'X': 0.26995474100112915, 'Y': 0.9113709926605225}, {'X': 0.26995474100112915, 'Y': 0.9220666885375977}, {'X': 0.024221936240792274, 'Y': 0.9220666885375977}]}, 'Id': '9d50b43e-4435-4dcd-8181-4f662cf5e869', 'Relationships': [{'Type': 'CHILD', 'Ids': ['41f75cbd-01c2-427b-89fb-364ad6e3f6e8', 'd780e919-7c37-40ed-8a33-6fa0165aac5f', 'f39f0d51-8689-4699-bc7d-3cc245f71718']}]} 0.01069571077823639\n", "{'BlockType': 'LINE', 'Confidence': 98.20015716552734, 'Text': 'TOTAL:', 'Geometry': {'BoundingBox': {'Width': 0.05713266134262085, 'Height': 0.009734392166137695, 'Left': 0.5635004043579102, 'Top': 0.9126775860786438}, 'Polygon': [{'X': 0.5635004043579102, 'Y': 0.9126775860786438}, {'X': 0.620633065700531, 'Y': 0.9126775860786438}, {'X': 0.620633065700531, 'Y': 0.9224119782447815}, {'X': 0.5635004043579102, 'Y': 0.9224119782447815}]}, 'Id': '9665d721-375d-415f-8704-98efa1e29e74', 'Relationships': [{'Type': 'CHILD', 'Ids': ['7d6a9f95-38d9-462c-b702-6a02ec496cee']}]} 0.009734392166137695\n", "{'BlockType': 'LINE', 'Confidence': 98.42185974121094, 'Text': '221.24', 'Geometry': {'BoundingBox': {'Width': 0.0569649338722229, 'Height': 0.010425150394439697, 'Left': 0.8100495934486389, 'Top': 0.9135648012161255}, 'Polygon': [{'X': 0.8100495934486389, 'Y': 0.9135648012161255}, {'X': 0.8670145273208618, 'Y': 0.9135648012161255}, {'X': 0.8670145273208618, 'Y': 0.9239899516105652}, {'X': 0.8100495934486389, 'Y': 0.9239899516105652}]}, 'Id': 'fe55a059-e4bc-4765-99a2-cce4b55b55fe', 'Relationships': [{'Type': 'CHILD', 'Ids': ['e94a15d6-e95d-4ec0-917c-fbf6a6861d55']}]} 0.010425150394439697\n", "{'BlockType': 'LINE', 'Confidence': 98.52609252929688, 'Text': 'or <EMAIL>', 'Geometry': {'BoundingBox': {'Width': 0.33333009481430054, 'Height': 0.012248635292053223, 'Left': 0.024224983528256416, 'Top': 0.9248546361923218}, 'Polygon': [{'X': 0.024224983528256416, 'Y': 0.9248546361923218}, {'X': 0.3575550615787506, 'Y': 0.9248546361923218}, {'X': 0.3575550615787506, 'Y': 0.937103271484375}, {'X': 0.024224983528256416, 'Y': 0.937103271484375}]}, 'Id': '3cb342ab-155b-4a9b-9e6b-efa9a6f125ce', 'Relationships': [{'Type': 'CHILD', 'Ids': ['043d1cdb-0d96-4fbf-b67a-b191c89c85ec', '2f3d4699-a1c3-41a6-b1bf-064ef5b9eaf9']}]} 0.012248635292053223\n", "{'BlockType': 'LINE', 'Confidence': 98.32035827636719, 'Text': 'DEPOSIT:', 'Geometry': {'BoundingBox': {'Width': 0.07656413316726685, 'Height': 0.009983420372009277, 'Left': 0.5420464277267456, 'Top': 0.9439269304275513}, 'Polygon': [{'X': 0.5420464277267456, 'Y': 0.9439269304275513}, {'X': 0.6186105608940125, 'Y': 0.9439269304275513}, {'X': 0.6186105608940125, 'Y': 0.9539103507995605}, {'X': 0.5420464277267456, 'Y': 0.9539103507995605}]}, 'Id': '8d53237f-23ba-4c01-b9d2-453074241eac', 'Relationships': [{'Type': 'CHILD', 'Ids': ['eea198b1-bdf1-422f-a77f-8df0c4282107']}]} 0.009983420372009277\n", "{'BlockType': 'LINE', 'Confidence': 98.6618881225586, 'Text': 'AMT DUE:', 'Geometry': {'BoundingBox': {'Width': 0.07657720148563385, 'Height': 0.009654291905462742, 'Left': 0.5416424870491028, 'Top': 0.9583922624588013}, 'Polygon': [{'X': 0.5416424870491028, 'Y': 0.9583922624588013}, {'X': 0.6182196736335754, 'Y': 0.9583922624588013}, {'X': 0.6182196736335754, 'Y': 0.9680465459823608}, {'X': 0.5416424870491028, 'Y': 0.9680465459823608}]}, 'Id': '89aa1f25-cacb-496b-b6d9-c89c00e604f2', 'Relationships': [{'Type': 'CHILD', 'Ids': ['4fbce261-1dc3-432c-b409-a9d069f6a606', '4fbfc22b-6b0e-4adc-8d75-ff4c9d63053a']}]} 0.009654291905462742\n", "{'BlockType': 'LINE', 'Confidence': 98.00476837158203, 'Text': '221.24', 'Geometry': {'BoundingBox': {'Width': 0.0565187931060791, 'Height': 0.010272324085235596, 'Left': 0.8088181614875793, 'Top': 0.9591375589370728}, 'Polygon': [{'X': 0.8088181614875793, 'Y': 0.9591375589370728}, {'X': 0.8653369545936584, 'Y': 0.9591375589370728}, {'X': 0.8653369545936584, 'Y': 0.9694098830223083}, {'X': 0.8088181614875793, 'Y': 0.9694098830223083}]}, 'Id': 'e8ca3f8a-b58d-4a6e-812a-f79ac7872dfd', 'Relationships': [{'Type': 'CHILD', 'Ids': ['4a35c2ba-7548-4da9-b6eb-cc14c2cef9c4']}]} 0.010272324085235596\n"]}], "source": ["import json\n", "import pyap\n", "\n", "\n", "f = open(json_path,) \n", "data = json.load(f)\n", "\n", "lines=[]\n", "for d in data[\"Blocks\"]:\n", "    if d[\"BlockType\"]==\"LINE\":\n", "        t=d[\"Text\"]\n", "        p=d[\"Geometry\"][\"Polygon\"]\n", "        h=d[\"Geometry\"][\"BoundingBox\"][\"Height\"]\n", "        print(d,h)\n", "        \n", "        "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#!pip uninstall address-net\n", "#y\n", "#!pip install address-parser\n", "!pip install pyap\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pyap\n", "test_address = \"\"\"\n", "    Lorem ipsum\n", "    225 <PERSON><PERSON> Freeway,\n", "    Suite 1500 Irving, Texas 75062\n", "    <PERSON><PERSON> sit amet\n", "    \"\"\"\n", "pyap.address()\n", "addresses = pyap.parse(test_address, country='US')\n", "for address in addresses:\n", "    # shows found address\n", "    print(address)\n", "    # shows address parts\n", "    print(address.as_dict())\n"]}, {"cell_type": "code", "execution_count": 101, "metadata": {}, "outputs": [], "source": ["#open cv to find countour\n", "\n", "import cv2 \n", "#import pytesseract \n", "  \n", "# Mention the installed location of Tesseract-OCR in your system \n", "#pytesseract.pytesseract.tesseract_cmd = 'System_path_to_tesseract.exe'\n", "  \n", "# Read image from which text needs to be extracted \n", "img = cv2.imread(\"/home/<USER>/invoice/inv-collection/jhuextract/83019d84-0b83-44b6-99b2-c6896eb4c8c3.jpg\") \n", "  \n", "# Preprocessing the image starts \n", "  \n", "# Convert the image to gray scale \n", "gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY) \n", "  \n", "# Performing OTSU threshold \n", "ret, thresh1 = cv2.threshold(gray, 0, 255, cv2.THRESH_OTSU | cv2.THRESH_BINARY_INV) \n", "  \n", "# Specify structure shape and kernel size.  \n", "# Kernel size increases or decreases the area  \n", "# of the rectangle to be detected. \n", "# A smaller value like (10, 10) will detect  \n", "# each word instead of a sentence. \n", "rect_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (300, 300)) \n", "  \n", "# Appplying dilation on the threshold image \n", "dilation = cv2.dilate(thresh1, rect_kernel, iterations = 1) \n", "  \n", "# Finding contours \n", "#contours, hierarchy = cv2.findContours(dilation, cv2.RETR_EXTERNAL,  \n", "#                                                 cv2.CHAIN_APPROX_NONE) \n", "  \n", "contours,hierarchy = cv2.findContours(thresh1,cv2.RETR_EXTERNAL,cv2.CHAIN_APPROX_SIMPLE )\n", "\n"]}, {"cell_type": "code", "execution_count": 102, "metadata": {}, "outputs": [], "source": ["for c in contours:\n", "    rect = cv2.boundingRect(c)\n", "    if rect[2] > 500 or rect[3] > 500 : continue\n", "\n", "    #print (cv2.contourArea(c))\n", "    x,y,w,h = rect\n", "    cv2.rectangle(img,(x-20,y-20),(x+w+20,y+h+20),(0,0,255),-1)\n", "    #cv2.fillPoly(img,[x,y,x+w,y+h],(0,0,255))"]}, {"cell_type": "code", "execution_count": 103, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 103, "metadata": {}, "output_type": "execute_result"}], "source": ["cv2.imwrite(\"../one2.jpg\",img)"]}, {"cell_type": "code", "execution_count": 107, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["done\n"]}], "source": ["#detecting polygon\n", "\n", "# Python code to detect an arrow (seven-sided shape) from an image. \n", "import numpy as np \n", "import cv2 \n", "\n", "# Reading image \n", "img2 = cv2.imread('../one2.jpg', cv2.IMREAD_COLOR) \n", "\n", "# Reading same image in another variable and \n", "# converting to gray scale. \n", "img = cv2.imread('../one2.jpg', cv2.IMREAD_GRAYSCALE) \n", "\n", "# Converting image to a binary image \n", "# (black and white only image). \n", "_,threshold = cv2.threshold(img, 110, 255, \n", "\t\t\t\t\t\t\tcv2.THRESH_BINARY) \n", "\n", "# Detecting shapes in image by selecting region \n", "# with same colors or intensity. \n", "contours,_=cv2.findContours(threshold, cv2.RETR_TREE, \n", "\t\t\t\t\t\t\tcv2.CHAIN_APPROX_SIMPLE) \n", "\n", "# Searching through every region selected to \n", "# find the required polygon. \n", "for cnt in contours : \n", "\tarea = cv2.contourArea(cnt) \n", "\n", "\t# Shortlisting the regions based on there area. \n", "\tif area > 400: \n", "\t\tapprox = cv2.approxPolyDP(cnt, \n", "\t\t\t\t\t\t\t\t0.009 * cv2.<PERSON><PERSON><PERSON><PERSON>(cnt, True), True) \n", "\n", "\t\t# Checking if the no. of sides of the selected region is 7. \n", "\t\tif(True): \n", "\t\t\tcv2.drawContours(img2, [approx], 0, (0, 0, 0), 5) \n", "\n", "# Showing the image along with outlined arrow. \n", "cv2.imwrite('../image2.jpg', img2) \n", "print(\"done\")\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.4"}}, "nbformat": 4, "nbformat_minor": 2}