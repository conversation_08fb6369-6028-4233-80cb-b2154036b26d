import math
import json
from scipy.spatial import distance as sp_distance
from collections import Counter 

def findLowestScore(items):
    least_score=0
    least_item=[]
    result=[]
    count=0
    
def checkDuplicate(item,list_):
    
    for i in list_:
        if item in i:
            return True
    return False

def createText(json_path):
        text_cordinates=[]
        matches=[]

        with open(json_path) as json_file:
            data=json.load(json_file)
            for blocks in data['Blocks']:
                temp_str=''
                #top_key= int(blocks['Geometry']['BoundingBox']['Top']*(height)/10) 
                #Left_key= int(blocks['Geometry']['BoundingBox']['Top']*(height)/10)
                    
                a=blocks["Geometry"]["Polygon"][0]['X']
                b=blocks["Geometry"]["Polygon"][0]['Y']

                c=blocks["Geometry"]["Polygon"][1]['X']
                d=blocks["Geometry"]["Polygon"][1]['Y']
                    
                if (blocks["BlockType"]=='LINE' and blocks['Geometry']['BoundingBox']['Top'])  :
                    text_cordinates.append((blocks["Text"],a,b,c,d))
                
        count=0  
        print("text cordinates ",len(text_cordinates))
        for item in text_cordinates:
            sub_list=text_cordinates[count+1:count+20]
            temp_matches=[]
            distance_counter=[]
            count2=0
            #print( "sublist", len(sub_list))
            for item2 in sub_list:
        
                a=(item[1],item[2])
                b=(item2[1],item2[2])
                dst = sp_distance.euclidean(a, b)
                myradians = math.atan2(b[1]-a[1], b[0]-a[0])
                degrees = math.degrees(myradians)
                degrees=int(degrees)
                #print(degrees,' -- ',item[0],' -- ',item2[0],' -- ',dst)
               
                if degrees>-4 and degrees<4 and dst<0.39:
                    temp_matches.append( (item[0],item2[0],degrees,dst) )
        
                

            temp_matches=findLowestScore(temp_matches) 
            if(temp_matches==None):
                temp_matches=[]
            
            if  len(temp_matches)==0:
                matches.append((item[0]))
            else:
                for i in temp_matches:
                    #print("item",i)
                    matches.append(i)
            count+=1
     
        header_list=[]
        count_=0
        for item in matches:
            #print(str(type))
            if str(type(item))=="<class 'str'>":
                if not checkDuplicate(item,matches[0:count_]):
                    header_list.append(item)
            elif str(type(item))=="<class 'tuple'>":
                header_list.append(item[0]+" "+item[1])
            count_=count_+1
        

        return header_list