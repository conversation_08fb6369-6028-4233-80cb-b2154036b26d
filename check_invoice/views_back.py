from django.shortcuts import render
from rest_framework.views import APIView
from django.shortcuts import render
from django.views.generic import View
from django.views.decorators.csrf import csrf_exempt
from django.http import JsonResponse
import json
from django.http import HttpResponse
import fitz
from AMS.settings import BASE_DIR
import uuid
import os 
import base64
import pdfplumber
import subprocess
import os.path
from os import path
import time
from AMS.extract_settings import *
from AMS.Invoice_identification_Settings import *
from spacy.matcher import Matcher
import datefinder
import boto3
from  check_invoice.AWSExtractUtil import *


#AWS Credentials
extract = boto3.client('textract','us-east-1')
client = boto3.client(service_name='textract',region_name='us-east-1',endpoint_url='https://textract.us-east-1.amazonaws.com',aws_access_key_id="********************",
                        aws_secret_access_key="6629LxPd+So34S4EJPxsxs0KOvCwt/AwrjZkAHwN")
 

# Create your views here.



#set of files that need to be deleted at the end of process 
del_files=[]



#checks if pdf is completly scanned 
# return values 
# no - if pdf contains text 
# scanned - if pdf is completly scanned 
def check_pdf_scanned_plain(file_name):
    # This algorithm calculates the percentage of document that is covered by (searchable) text

    page_num = 0
    text_perc = 0.0

    doc = fitz.open(file_name)

    for page in doc:
        page_num = page_num + 1

        page_area = abs(page.rect)
        text_area = 0.0
        for b in page.getTextBlocks():
            r = fitz.Rect(b[:4]) # rectangle where block text appears
            text_area = text_area + abs(r)
        text_perc = text_perc + (text_area / page_area)

    text_perc = text_perc / page_num

    # If the percentage of text is very low, the document is most likely a scanned PDF
    result=""
    if text_perc < 0.01:
        result="scanned"
        print("fully scanned PDF - no relevant text")
    else:
        result="no"
        print("not fully scanned PDF - text is present")
    return result 


#Extract Images from PDF and returns images :

def reader( file):
    with open(file, "rb") as image_file:
        img_test = image_file.read()
        bytes_test = bytearray(img_test)
    return bytes_test



def extract_images_pdf(file):
    path, filename = os.path.split(file)
    doc = fitz.open(file)
    filename=filename.split(".")[0]
    files=[]
    for i in range(len(doc)):
        for img in doc.getPageImageList(i):
            print(img)
            xref = img[0]
            pix = fitz.Pixmap(doc, xref)
            if pix.n < 5:       # this is GRAY or RGB
                pix.writePNG(path+"/p%s-%s.png" % (i, filename))
                files.append(path+"/p%s-%s.png" % (i, filename))
            else:               # CMYK: convert to RGB first
                pix1 = fitz.Pixmap(fitz.csRGB, pix)
                pix1.writePNG(path+"/p%s-%s.png" % (i, filename))
                pix1 = None
                files.append(path+"/p%s-%s.png" % (i, filename))
            pix = None

    del_files.extend(files)        
    return files    
        
print("extracted")


def extract_text_from_image_aws(files):
    
    data=[]
    
    
    #target_textname=imagename.split(".")[0]+"-c"
    print("No of Files For scanning ",len(files))
    
    for file in files:
        imagename=file
        target_json_file=imagename.split(".")[0]+"-c.json"
        del_files.append(target_json_file)

        data_str = reader(file)
        response = client.detect_document_text(Document={'Bytes': data_str})
        with open(target_json_file, 'w') as fp:
            json.dump(response, fp)
        #Extract data
        data.extend(createText(target_json_file) )
    
    return data

    

       
        




def extract_text_from_image(files):
    
    data=""
    print("No of Files For scanning ",len(files))
    for file in files:

        #Perform OCR :
        #using tesseract to extract text from it
        imagename=file
        target_imagename=imagename.split(".")[0]+"-c.png"
        target_textname=imagename.split(".")[0]+"-c"

        process = subprocess.Popen(['tesseract', imagename,target_textname],
                     stdout=subprocess.PIPE, 
                     stderr=subprocess.PIPE) 
        stdout, stderr = process.communicate()
        out_list=str(stderr).split("\n")

        if any("Error in boxClipToRectangle" in s for s in out_list):
            cmd="convert "+imagename+" -type Grayscale -negate -define morphology:compose=darken -morphology Thinning 'Rectangle:1x80+0+0<' -negate "+target_imagename
            stream = os.popen(cmd)
            output = stream.read()

             #perform ocr again 
            process = subprocess.Popen(['tesseract', target_imagename,target_textname],
                     stdout=subprocess.PIPE, 
                     stderr=subprocess.PIPE) 
            stdout, stderr = process.communicate()
        

    

         





        #converting images to border so that tesseract detects the borders 
        
        #cmd="convert "+imagename+" -type Grayscale -negate -define morphology:compose=darken -morphology Thinning 'Rectangle:1x80+0+0<' -negate "+target_imagename
        #stream = os.popen(cmd)


        #Adding File For Clean UP Process 
        del_files.append(target_imagename)
        del_files.append(target_textname+".txt")

       
        print("Image Name ",target_imagename)
        #time.sleep(60)
        

        #using tesseract to extract text from it
        
        print("output",stdout, stderr)
        print("path exists ",path.exists(target_textname+".txt"))
        if path.exists(target_textname+".txt"):
            f = open(target_textname+".txt", "r")
            data=data+f.read()
            #print(data)
    print("returning data length ",len(data))
    return data 


       











#Class Serves as View  

class CheckInvoice(APIView):

    def post(self,request):
        json_body = json.loads(request.body.decode("utf-8"))
        base64__ = json_body["data"]
        invoice_type = json_body["type"]
        base_path = BASE_DIR+'/extract_check/'

        unique_filename = str(uuid.uuid4())
        if invoice_type == "pdf":
            invoice_path = base_path+unique_filename+".pdf"
        else:
            invoice_path = base_path+unique_filename+".png"

        print("invoice_path", invoice_path)
        with open(os.path.expanduser(invoice_path), 'wb') as fout:
            fout.write(base64.b64decode(base64__))

                
        print("File Written "+invoice_path)
        fout.close()
        del_files.append(invoice_path)

        #convert image file into pdf file 
        extract_text=None
        files=[]
        if invoice_type=="pdf":
            #Check if its a scanned pdf 

             
            try:
                scanned=check_pdf_scanned_plain(invoice_path)
            except :
                error={"status":'error','error':'invalid type'}
                return HttpResponse(json.dumps(error,indent=4, sort_keys=True, default=str))
                
            
            if scanned=="scanned":
                #PDF is scanned Extract Image from PDF 
                files=extract_images_pdf(invoice_path)

                #extract text from images 
                print("extract text from image ")
                print(files)
                extract_text=extract_text_from_image_aws(files)




            else:
                #PDF is TEXT Extract text from pdf  
                with pdfplumber.open(invoice_path) as pdf:
                    page = pdf.pages[0]
                    extract_text = page.extract_text() 
                    print("-----------------------------------------")
                    print(extract_text)
        else:
            #invoice type is not pdf , extract the data from image :
            print("extract text from image ")
            arr=[invoice_path]
            extract_text=extract_text_from_image_aws(arr)
            
       
         
        
        
        print("----------------------------")
        print(extract_text) 
        print("---------------------------")

        #initialize the matcher :
        matcher = Matcher(nlp.vocab)

        total_weights=0
        total_score=0
        match_summary={}
        for field in  check_fields:
            print("---------------------------------------------")
            print("################",field)

            f_name=field["name"]
            f_type=field["type"]
            f_pattern_list=field["pattern"]
            f_weight=field["weight"]
            total_weights=total_weights+int(f_weight)
             
            match_list=[] 
            matcher = Matcher(nlp.vocab) 
            index = 0
            for pattern in f_pattern_list:
                matcher.add(f_name+str(index), None, pattern)
                index=index+1 

            lines=extract_text 
            if f_type=="string":

                for line in lines:
                    doc = nlp( line.strip() )
                    matches = matcher(doc)
                    for match_id, start, end in matches:
                        string_id = nlp.vocab.strings[match_id]  # Get string representation
                        span = doc[start:end]  # The matched span
                        if len(span.text)>0:
                            match_list.append(span.text)
                
                print("matches found ")
                print(match_list)
                if(len(match_list)>0):
                    total_score=total_score+int(f_weight)
                    match_summary[f_name]=match_list
                else:
                    match_summary[f_name]=None

            elif f_type=="date":
                match_list=[] 
                for line in lines:
                    doc = nlp( line.strip() )
                    matches = matcher(doc)
                    #date match usi
                    
                    date_match_count=0
                    
                    try:
                        date_match = datefinder.find_dates(line)  
                        date_match_count=len( list(date_match) ) 
                    except:
                        print("Error with datefinder ")
                            

                       
                   
                   

                    for match_id, start, end in matches:
                        string_id = nlp.vocab.strings[match_id]  # Get string representation
                        span = doc[start:end]  # The matched span
                        if len(span.text)>0 and date_match_count>0:
                            match_list.append(span.text)

                print("matches found ",f_name)
                print(match_list)
                if(len(match_list)>0):
                    total_score=total_score+int(f_weight)
                    match_summary[f_name]=match_list
                else:
                    match_summary[f_name]=None

        weight_percent=(total_score/total_weights)*100   
        print("total score ",total_score)
        print("total weight",total_weights)
        print("percentage ",(total_score/total_weights)*100) 
        print(match_summary)
        
        isINvoice=None
        if weight_percent>=40:
            isINvoice=True
        else:
            isINvoice=False

        #Clean Up Service :
       
        for file in del_files:
            if os.path.exists(file):
                os.remove(file)
            
        
        print("Removed all files ")

        res = {"percent": weight_percent,"is_invoice":isINvoice,'status':'success'}
        return HttpResponse(json.dumps(res,indent=4, sort_keys=True, default=str))

    
      






    def get(self, request):
        print(id(nlp))
        res = {"response": "ok"}
        return HttpResponse(res)

       
        

