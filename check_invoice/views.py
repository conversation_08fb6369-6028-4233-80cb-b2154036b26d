from django.shortcuts import render
from rest_framework.views import APIView
from django.shortcuts import render
from django.views.generic import View
from django.views.decorators.csrf import csrf_exempt
from django.http import JsonResponse
import json
from django.http import HttpResponse
import fitz
from AMS.settings import BASE_DIR
import uuid
import os 
import base64
import pdfplumber
import subprocess
import os.path
from os import path
import time
from AMS.extract_settings import *
from AMS.Invoice_identification_Settings import *
from spacy.matcher import Matcher
import datefinder
import boto3
from  check_invoice.AWSExtractUtil import *
#importing libraries for pdf to invoice
from pdf2image import convert_from_path, convert_from_bytes
from pdf2image.exceptions import (
    PDFInfoNotInstalledError,
    PDFPageCountError,
    PDFSyntaxError
)
from Invoice.ML.TFPredictions import *
from InvoiceSplit.utils import *


#AWS Credentials
#extract = boto3.client('textract','us-east-1')
#client = boto3.client(service_name='textract',region_name='us-east-1',endpoint_url='https://textract.us-east-1.amazonaws.com',)
 

# Create your views here.



#set of files that need to be deleted at the end of process 
del_files=[]



#checks if pdf is completly scanned 
# return values 
# no - if pdf contains text 
# scanned - if pdf is completly scanned 
def check_pdf_scanned_plain(file_name):
    # This algorithm calculates the percentage of document that is covered by (searchable) text

    page_num = 0
    text_perc = 0.0

    doc = fitz.open(file_name)

    for page in doc:
        page_num = page_num + 1

        page_area = abs(page.rect)
        text_area = 0.0
        for b in page.getTextBlocks():
            r = fitz.Rect(b[:4]) # rectangle where block text appears
            text_area = text_area + abs(r)
        text_perc = text_perc + (text_area / page_area)

    text_perc = text_perc / page_num

    # If the percentage of text is very low, the document is most likely a scanned PDF
    result=""
    if text_perc < 0.01:
        result="scanned"
        print("fully scanned PDF - no relevant text")
    else:
        result="no"
        print("not fully scanned PDF - text is present")
    return result 


#Extract Images from PDF and returns images :

def reader( file):
    with open(file, "rb") as image_file:
        img_test = image_file.read()
        bytes_test = bytearray(img_test)
    return bytes_test



def extract_images_pdf(file):
    path, filename = os.path.split(file)
    doc = fitz.open(file)
    filename=filename.split(".")[0]
    files=[]
    for i in range(len(doc)):
        for img in doc.getPageImageList(i):
            print(img)
            xref = img[0]
            pix = fitz.Pixmap(doc, xref)
            if pix.n < 5:       # this is GRAY or RGB
                pix.writePNG(path+"/p%s-%s.png" % (i, filename))
                files.append(path+"/p%s-%s.png" % (i, filename))
            else:               # CMYK: convert to RGB first
                pix1 = fitz.Pixmap(fitz.csRGB, pix)
                pix1.writePNG(path+"/p%s-%s.png" % (i, filename))
                pix1 = None
                files.append(path+"/p%s-%s.png" % (i, filename))
            pix = None

    del_files.extend(files)        
    return files    
        
print("extracted")



    

       
        





       











#Class Serves as View  

class CheckInvoice(APIView):

    def post(self,request):

        try:
            json_body = json.loads(request.body.decode("utf-8"))
            base64__ = json_body["data"]
            invoice_type = json_body["type"]
            base_path = BASE_DIR+'/extract_check/'
            request_id = log_to_db(request, 'check_invoice', 'check_invoice', None)

            unique_filename = str(uuid.uuid4())
            if invoice_type == "pdf":
                invoice_path = base_path+unique_filename+".pdf"
            else:
                invoice_path = base_path+unique_filename+".png"

            #raise Exception('Error with pdf parsing ')



            if not os.path.isdir(BASE_DIR+'/Invoice/pdf'):
                os.mkdir(BASE_DIR+'/Invoice/pdf')
                print("pdf folder created...")
            
        

            print("invoice_path", invoice_path)
            with open(os.path.expanduser(invoice_path), 'wb') as fout:
                fout.write(base64.b64decode(base64__))

                    
            print("File Written "+invoice_path)
            fout.close()
            del_files.append(invoice_path)

            #convert image file into pdf file 
            extract_text=None
            files=[]
            ############################################### Extract Invoice Start ################################ 
            images_list={}
            #json_list={}
            #folder_list=[]
            #mapping_list=[]

            images_count=0
            #convert the pdf into image 
            if invoice_type=="pdf":
                images = convert_from_path(invoice_path, size=(2000, None))
                #for im in images:
                #im.save(base_path+unique_filename+".png")
                
                for i in images:
                    i.save(base_path+unique_filename+"_"+str(images_count)+".png")
                    images_list[images_count]=base_path+unique_filename+"_"+str(images_count)+".png"
                    del_files.append(base_path+unique_filename+"_"+str(images_count)+".png")
                    images_count=images_count+1
            else:
                i = Image.open(invoice_path)
                i.save(base_path+unique_filename+"_"+str(images_count)+".png")
                images_list[images_count]=base_path+unique_filename+"_"+str(images_count)+".png"
                del_files.append(base_path+unique_filename+"_"+str(images_count)+".png")
                images_count=images_count+1

            image_keys=list(images_list.keys())
            image_keys.sort()


            page_length=len(image_keys)
            isInvoice=False
            percent=0
            if page_length>2 or invoice_type=="pdf" :
                isInvoice=True
                percent=80
            else:
                for key in image_keys:
                    I_res=None
                    inv_page_path=images_list[key]

                    if inv_page_path is not None:
                        I_res=predictInvoice(TFmodel2,inv_page_path)
                    print("key :",key, " Invoice :",I_res)
                    if I_res["NI"]<0.80:
                        isInvoice=True
                        percent=I_res["I"]
                    else:
                        percent=I_res["NI"]



            
        
            
                                
                






            ############################################### Extract Invoice END ################################ 
        
            
            #Clean Up Service :
        
        
            for file in del_files:
                print("removing ",file, " : ",os.path.exists(file))
                if os.path.exists(file):
                    os.remove(file)
            
            
            print("Removed all files ")

            res = {"percent": percent,"is_invoice":isInvoice,'status':'success'}
            update_status(request_id, 200)
            return HttpResponse(json.dumps(res,indent=4, sort_keys=True, default=str))

        except Exception as e:
            import traceback
            print(traceback.format_exc())
            error={}
            error["error"]="Invalid JSON/Corruption JSON values"
            print("Error ",str(e))
            update_status(request_id, 500, error_msg=str(e))
            return HttpResponse(json.dumps(str(e),indent=4, sort_keys=True, default=str),status=500,)

    
      






    def get(self, request):
        print(id(nlp))
        res = {"response": "ok"}
        return HttpResponse(res)

       
        

