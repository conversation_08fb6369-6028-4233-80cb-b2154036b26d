from AMS.extract_settings import db_logger

class CustomLogger:

    def __init__(self, prefix):
        self.prefix = prefix

    def print(self, message, user=None, add_to_db_logger=True, type='info'):
        message = str(message)
        print(self.prefix + ' - ' + message)
        if add_to_db_logger:
            if user is not None:
                if type == 'info':
                    db_logger.info(self.prefix + ' - ' + message, {'user':user,  'entity':self.prefix})
                else:
                    db_logger.exception(self.prefix + ' - ' + message, {'user':user,  'entity':self.prefix})