{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import os \n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\""]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import django\n", "django.setup()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from Invoice.DbConfig import *\n", "from fuzzywuzzy import fuzz\n", "from fuzzywuzzy import process\n", "import boto3\n", "import json\n", "import trp.trp2 as t2\n", "\n", "\n", "\n", "textract = boto3.client('textract')"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["def makecall(image_path,question,field_name):\n", "    field_list={}\n", "    with open(image_path, 'rb') as document:\n", "        imageBytes = bytearray(document.read())\n", "                            \n", "\n", "        # Call Textract AnalyzeDocument by passing a document from local disk\n", "        response2 = textract.analyze_document(\n", "            Document={'Bytes': imageBytes},\n", "            FeatureTypes=[\"QUERIES\"],\n", "            QueriesConfig={\n", "                \"Queries\": [{\n", "                    \"Text\": question,\n", "                    \"Alias\": field_name\n", "                }]\n", "            })\n", "\n", "        d = t2.TDocumentSchema().load(response2)\n", "        page = d.pages[0]\n", "\n", "        # get_query_answers returns a list of [query, alias, answer]\n", "        query_answers = d.get_query_answers(page=page) \n", "        if len(query_answers)>0:\n", "            if len(query_answers[0][2].strip())>0:\n", "                field_list[field_name]=query_answers[0][2]\n", "                print(question,\" \",query_answers[0][2])\n", "    \n", "    return field_list"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["def getQuery(config_name,json_result,image_list):\n", "    \n", "    field_results={}\n", "    res_list=getquery(config_name) \n", "    json_result =  {k.lower().strip(): v for k, v in json_result.items()}\n", "    \n", "    print(\"res_list \",res_list)\n", "    print(\"json \",json_result)\n", "    #for item in res_list:\n", "    \n", "    result={}\n", "    for item in res_list:\n", "        q_fname=item[\"fieldname\"]\n", "        q_question=item[\"query\"]\n", "        q_json= item[\"json\"]\n", "        \n", "        for index, key in enumerate(q_json):\n", "            if index==0:\n", "                if key in json_result:\n", "                    if fuzz.ratio(json_result[key],q_json[key])>93:\n", "                        result[\"c1\"]=True\n", "                    else:\n", "                        result[\"c1\"]=False\n", "\n", "            elif index==2:\n", "                if key in json_result:\n", "                    if fuzz.ratio(json_result[key],q_json[key])>93:\n", "                        result[\"c2\"]=True\n", "                    else:\n", "                        result[\"c2\"]=False\n", "\n", "        print(\"result\",result)\n", "        operator_result=None\n", "        if \"operator\" in res_list:\n", "\n", "            if \"c2\" in result:\n", "                print(\"in c2\")\n", "                operator_result=eval( result[\"c1\"]+\" \"+res_list[\"operator\"]+\" \"+result[\"c2\"] )\n", "                print(\"operator result \",operator_result)\n", "                if operator_result:\n", "                    for image in image_list:\n", "                        query_r=makecall(image,item[\"query\"],item[\"fieldname\"])\n", "                        print(\"query result \",query_r)\n", "                        if item[\"fieldname\"] in query_r:\n", "                            field_results[item[\"fieldname\"]]=query_r[item[\"fieldname\"]]\n", "                            break\n", "        else:\n", "            if \"c1\" in result:\n", "                print(\"in c1\")\n", "                for image in image_list:\n", "                    query_r=makecall(image,item[\"query\"],item[\"fieldname\"])\n", "                    print(\"query result \",query_r)\n", "                    if item[\"fieldname\"] in query_r:\n", "                        field_results[item[\"fieldname\"]]=query_r[item[\"fieldname\"]]\n", "                        break\n", "\n", "    \n", "    return field_results\n", "                    "]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["res_list  [{'fieldname': 'Q_INVOICE', 'query': 'what is Invoice number?', 'json': {'vendor_name': 'TDW WILLIAMSON CANADA ULC'}}]\n", "json  {'confidence_level': 66, 'vendor_name': 'TDW WILLIAMSON CANADA ULC'}\n", "result {'c1': True}\n", "in c1\n", "what is Invoice number?   JC1023602\n", "query result  {'Q_INVOICE': 'JC1023602'}\n"]}, {"data": {"text/plain": ["{'Q_INVOICE': 'JC1023602'}"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["json_res={\"confidence_level\": 66, \"vendor_name\": \"TDW WILLIAMSON CANADA ULC\" } \n", "img_list=[\"/home/<USER>/Pictures/TDWInvoice.png\"] \n", "r=getQuery(\"sou_config\",json_res,img_list) \n", "r"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.4"}}, "nbformat": 4, "nbformat_minor": 2}