# The first instruction is what image we want to base our container on
# We Use an official Python runtime as a parent image
FROM python:3.8

# The enviroment variable ensures that the python output is set straight
# to the terminal with out buffering it first
ENV PYTHONUNBUFFERED 1

# create root directory for our project in the container
#RUN mkdir -p /Invoice
#ENV AWS_SECRET_ACCESS_KEY=qGdEuYlAhLjBAZKHp+lFFCZaysE1P8mPrDAp13Zh
#ENV AWS_ACCESS_KEY_ID=********************
ENV AWS_SECRET_ACCESS_KEY=6629LxPd+So34S4EJPxsxs0KOvCwt/AwrjZkAHwN
ENV AWS_ACCESS_KEY_ID=********************
ENV AWS_DEFAULT_REGION=us-east-2
# Set the working directory to /clm_ai
####
RUN apt-get update

RUN mkdir -p /tensorflow/models
RUN apt-get install -y git python3-pip
RUN python3 -m pip install --upgrade pip
RUN pip install setuptools==69.5.1

RUN python3 -m pip install --upgrade Pillow
RUN apt-get install -y protobuf-compiler python3-lxml
RUN pip install jupyter
RUN pip install matplotlib
RUN git clone https://github.com/tensorflow/models.git /tensorflow/models

####

ADD . /invoice/
#COPY fonts/OCRA.ttf /usr/local/share/fonts/
VOLUME /invoice_volume
WORKDIR /invoice
# install our dependencies
# we use --system flag because we don't need an extra virtualenv
COPY Pipfile  /invoice/



RUN mv dvc_configs .dvc
#RUN pip install pip #--upgrade 
RUN pip install pipenv 
RUN pip uninstall pathspec -y
RUN pip install pathspec==0.9.0
RUN pip install dvc[s3]==1.10.2
RUN rm -rf .dvc/cache


RUN apt-get update && apt-get install -y fonts-liberation fonts-dejavu && rm -rf /var/lib/apt/lists/*

RUN dvc pull
# Copy the current directory contents into the container at /clm_ai

RUN pip install -r requirements.txt
#RUN pip install lexnlp==0.2.7
RUN pip install spacy==2.3.0

# Run apt-get Update
RUN apt-get update && apt-get upgrade -y
RUN apt install awscli -y
#RUN aws configure set aws_access_key_id ********************
#RUN aws configure set aws_secret_access_key qGdEuYlAhLjBAZKHp+lFFCZaysE1P8mPrDAp13Zh
RUN aws configure set aws_access_key_id ********************
RUN aws configure set aws_secret_access_key 6629LxPd+So34S4EJPxsxs0KOvCwt/AwrjZkAHwN

RUN python -m spacy download en_core_web_md


RUN apt-get install tesseract-ocr -y
RUN apt-get install libtesseract-dev -y
RUN apt-get install imagemagick -y
RUN apt-get install poppler-utils -y


#Tensorflow and Object Detection API 
#RUN apt-get update && yes | apt-get upgrade

#WORKDIR /tensorflow/models/research

RUN curl -OL https://github.com/protocolbuffers/protobuf/releases/download/v3.20.3/protoc-3.20.3-linux-x86_64.zip
RUN unzip -o protoc-3.20.3-linux-x86_64.zip -d /usr/local bin/protoc
RUN unzip -o protoc-3.20.3-linux-x86_64.zip -d /usr/local 'include/*'
RUN rm -f protoc-3.20.3-linux-x86_64.zip

#RUN protoc tensorflow/models/research/object_detection/protos/*.proto --python_out=.
WORKDIR /tensorflow/models/research
RUN ls -l
RUN protoc object_detection/protos/*.proto --python_out=.
RUN cp object_detection/packages/tf2/setup.py .
RUN python -m pip install /tensorflow/models/research/.
#RUN export PYTHONPATH=$PYTHONPATH:`pwd`:`pwd`/slim

###################
WORKDIR /invoice
#RUN apt-key adv --fetch-keys  http://developer.download.nvidia.com/compute/cuda/repos/ubuntu1604/x86_64/7fa2af80.pub
#RUN bash -c 'echo "deb https://developer.download.nvidia.com/compute/cuda/repos/ubuntu1604/x86_64 /" > /etc/apt/sources.list.d/cuda.list'
RUN apt update -y
#RUN apt install cuda-cudart-10-1 -y
#RUN git clone https://github.com/facebookresearch/detectron2.git
#RUN python -m pip install -e detectron2
RUN pip install 'git+https://github.com/facebookresearch/detectron2.git@a59f05630a8f205756064244bf5beb8661f96180'

#COUPLE OF PACKAGES ARE GETTING OVERRIDEN BY LIBRARIES
#PUT THOSE PACKAGES HERE POST INSTALLING ALL THE LIBRARIES
# #removing invalid version which is added by third library
RUN pip uninstall opencv-python-headless -y
RUN pip install django-simple-history==3.0.0
# importlib-metadata IS UPDATED BY INSTALLED LIBRARY AND DETECTRON2 REQUIRES THE OLD ONE.(REMOVING HERE AND ADDING THE OLDER VERSION IN REQUIREMENT OF POST INSTALLATION)
RUN pip uninstall importlib-metadata -y
#RUN python manage.py collectstatic 
RUN pip uninstall numpy -y
RUN pip install -r requirements_post_installation.txt

RUN python manage.py collectstatic --noinput
RUN python setVersion.py
RUN pip uninstall PyJWT -y
RUN pip install PyJWT==1.7.1

# Expose ports
EXPOSE 9100

# define the default command to run when starting the container
CMD ["gunicorn", "--worker-class","gevent","--worker-connections","1000","--workers","2","--chdir", "AMS", "--bind", ":9100", "AMS.wsgi", "--timeout", "600"]
