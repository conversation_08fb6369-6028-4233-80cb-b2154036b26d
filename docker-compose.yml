version: '3'

services:
  invoice:
    build: .
    #logging:
    #  driver: awslogs
    #  options:
    #    awslogs-region: "us-east-2"
    #    awslogs-group: "InvoiceflowLogGroup"
    #    awslogs-create-group: "true"
    volumes:
      - invoice:/invoice_volume
    networks:  # <-- here
      - nginx_network
    environment:
      SECRET_KEY: ${SECRET_KEY}
      #COMPOSE_HTTP_TIMEOUT: ${COMPOSE_HTTP_TIMEOUT}
      #AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
      #AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}


  nginx:
    image: nginx:1.13
    ports:
      - 9100:80
    volumes:
      - ./config/nginx/conf.d:/etc/nginx/conf.d
      - ./static:/static
    depends_on:
      - invoice
    #logging:
    #  driver: awslogs
    #  options:
    #    awslogs-region: "us-east-2"
    #    awslogs-group: "InvoiceflowWebServerLogGroup"
    #    awslogs-create-group: "true"
    networks:  # <-- here
      - nginx_network
volumes:
  invoice:
networks:  # <-- and here
  nginx_network:
    driver: bridge