class App2DBRouter(object):

    def db_for_read(self,model, **hints):
        if model._meta.app_label == 'metric_tracker':
            return 'counterDB'
        return None

    def db_for_write(self,model, **hints):
        if model._meta.app_label == 'metric_tracker':
            return 'counterDB'
        return None

    def allow_relation(self,obj1, obj2, **hints):
        if obj1._meta.app_label == 'metric_tracker' and \
           obj2._meta.app_label == 'metric_tracker':
           return True
        return None

    def allow_syncdb(self,db, model):
        if db == 'counterDB':
            if model._meta.app_label == 'metric_tracker':
                return True
        elif model._meta.app_label == 'metric_tracker':
            return False
        return None 
    
    def allow_migrate(self, db, app_label, model_name=None, **hints):
        if app_label == 'metric_tracker':
            return db == 'counterDB'
        return None