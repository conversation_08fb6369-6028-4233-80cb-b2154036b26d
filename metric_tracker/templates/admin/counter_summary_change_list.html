{% extends "admin/change_list.html" %}

{% load humanize %}

<!-- ... -->

{% block result_list %}

<div class="results">
    <div class="table">
    <table>

    <thead>
      <tr>
        <th>
          <div class="text">
            <a href="#">Client</a>
          </div>
        </th>
        <th>
          <div class="text">
            <a href="#">Hits</a>
          </div>
        </th>
        <th>
            <div class="text">
              <a href="#">Success Ratio</a>
            </div>
          </th>
       
      </tr>
    </thead>

    <tbody>
      {% for row in summary %}
      <tr class="{% cycle 'row1' 'row2' %}">
        <td> {{ row.client_host }} </td>
        <td>
          <strong>
          {{ row.hits }}
          </strong>
        </td>
        <td>
            {{ row.success_ratio | default:0 }}%            
          </td>
      </tr>
      {% endfor %}
      <tr style="font-weight:bold; border-top:2px solid #DDDDDD;">
        <td> Total </td>
        <td> {{ total.hits | default:0 }} </td>
        <td> - </td>

    </tr>
      
    </tbody>

  </table>
</div>
<br/><br/><br/>

<div class="chart">

  <h2> No. of hits (by {{period}}) </h2>

    <style>
    .chart,.table {
        border: 1px solid white;
        padding: 5px;
    }
    .bar-chart {
      display: flex;
      justify-content: space-around;
      height: 160px;
      padding-top: 60px;
      overflow: hidden;
    }
    .bar-chart .bar {
        flex: 100%;
        align-self: flex-end;
        margin-right: 2px;
        position: relative;
        background-color: #79aec8;
    }
    .bar-chart .bar:last-child {
        margin: 0;
    }
    .bar-chart .bar:hover {
        background-color: #417690;
    }

    .bar-chart .bar .bar-tooltip {
        position: relative;
        z-index: 999;
    }
    .bar-chart .bar .bar-tooltip {
        position: absolute;
        top: -60px;
        left: 50%;
        transform: translateX(-50%);
        text-align: center;
        font-weight: bold;
        opacity: 0;
    }
    .bar-chart .bar:hover .bar-tooltip {
        opacity: 1;
    }
    </style>

    <div class="results">
        <div class="bar-chart">
        {% for x in summary_over_time %}
            <div class="bar" style="height:{{x.pct}}%">
                <div class="bar-tooltip">
                    {{x.total | default:0 | intcomma }}<br>
                    {{x.period | date:"d/m/Y"}}
                </div>
            </div>
        {% endfor %}
        </div>
    </div>
</div>

</div>

{% endblock %}
{% block pagination %}
 

{% endblock %}