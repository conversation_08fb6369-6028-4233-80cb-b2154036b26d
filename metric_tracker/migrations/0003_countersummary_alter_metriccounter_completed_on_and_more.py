# Generated by Django 4.1.7 on 2023-07-07 04:33

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        (
            "metric_tracker",
            "0002_metriccounter_completed_on_metriccounter_message_and_more",
        ),
    ]

    operations = [
        migrations.CreateModel(
            name="CounterSummary",
            fields=[],
            options={
                "verbose_name": "Counter Summary",
                "verbose_name_plural": "Counter Summary",
                "proxy": True,
                "indexes": [],
                "constraints": [],
            },
            bases=("metric_tracker.metriccounter",),
        ),
        #migrations.AlterField(
        #    model_name="contractcounter",
        #    name="completed_on",
        #    field=models.DateTimeField(blank=True, default=None),
        #),
        #migrations.AlterField(
        #    model_name="contractcounter",
        #    name="message",
        #    field=models.TextField(blank=True, default=None),
        #),
        #migrations.AlterField(
        #    model_name="contractcounter",
        #    name="pages",
        #    field=models.IntegerField(blank=True, default=None),
        #),
        #migrations.Alter<PERSON>ield(
        #    model_name="contractcounter",
        #    name="status",
        #    field=models.IntegerField(blank=True, default=None),
        #),
    ]
