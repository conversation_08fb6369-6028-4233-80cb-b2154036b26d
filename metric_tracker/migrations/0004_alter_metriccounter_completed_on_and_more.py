# Generated by Django 4.1.7 on 2023-07-07 04:37

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        (
            "metric_tracker",
            "0003_countersummary_alter_metriccounter_completed_on_and_more",
        ),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="metriccounter",
            name="completed_on",
            field=models.DateTimeField(blank=True, default=None, null=True),
        ),
        migrations.AlterField(
            model_name="metriccounter",
            name="message",
            field=models.TextField(blank=True, default=None, null=True),
        ),
        migrations.AlterField(
            model_name="metriccounter",
            name="pages",
            field=models.IntegerField(blank=True, default=None, null=True),
        ),
        migrations.AlterField(
            model_name="metriccounter",
            name="status",
            field=models.IntegerField(blank=True, default=None, null=True),
        ),
    ]
