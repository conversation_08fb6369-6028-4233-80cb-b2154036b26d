# Generated by Django 4.1.7 on 2023-05-08 08:07

from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="MetricCounter",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("api", models.CharField(help_text="API URL", max_length=1024)),
                ("config_name", models.Char<PERSON>ield(max_length=300)),
                ("file_type", models.TextField()),
                ("client_host", models.TextField(blank=True)),
                ("agent", models.TextField(blank=True)),
                ("username", models.TextField(blank=True)),
                ("response", models.TextField(blank=True, default=None)),
                ("created_on", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "verbose_name": "Single Request Counter",
                "verbose_name_plural": "Single Request Counter",
                "db_table": "MetricCounter",
            },
        ),
    ]
