# Generated by Django 4.1.7 on 2023-07-05 07:10

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("metric_tracker", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="metriccounter",
            name="completed_on",
            field=models.DateTimeField(blank=True, null=True,default=None),
        ),
        migrations.AddField(
            model_name="metriccounter",
            name="message",
            field=models.TextField(blank=True,null=True, default=None),
        ),
        migrations.AddField(
            model_name="metriccounter",
            name="pages",
            field=models.IntegerField(blank=True, null=True,default=None),
        ),
        migrations.AddField(
            model_name="metriccounter",
            name="status",
            field=models.IntegerField(blank=True, null=True,default=None),
        ),
    ]
