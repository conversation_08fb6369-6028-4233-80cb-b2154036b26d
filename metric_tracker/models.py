from django.db import models
# Create your models here.

from django.db import models
from jsonfield import J<PERSON><PERSON><PERSON>
# Create your models here.
class MetricCounter(models.Model):
   id = models.BigAutoField(primary_key=True)
   api = models.CharField(max_length=1024, help_text='API URL')
   method = models.CharField(max_length=1024,null=True)
   config_name=models.CharField(max_length=300)
   pages = models.IntegerField(blank=True,null=True,default=None)
   file_type = models.TextField()
   client_host = models.TextField(blank=True)
   agent= models.TextField(blank=True)
   username= models.TextField(blank=True)
   response = models.TextField(default=None,blank=True)
   created_on = models.DateTimeField(auto_now_add=True)
   completed_on = models.DateTimeField(blank=True,null=True,default=None)
   status= models.IntegerField(blank=True,null=True,default=None)
   message = models.TextField(blank=True,null=True,default=None)
   
   def __str__(self):
      return self.api

   class Meta:
      db_table = 'MetricCounter'
      verbose_name = 'Single Request Counter'
      verbose_name_plural = 'Single Request Counter'


class CounterSummary(MetricCounter):
    class Meta:
        proxy = True
        verbose_name = 'Counter Summary'
        verbose_name_plural = 'Counter Summary'