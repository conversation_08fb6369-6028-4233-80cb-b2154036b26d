from django.contrib import admin

# Register your models here.
from django.contrib import admin
from metric_tracker.models import *
from django.db.models import <PERSON><PERSON><PERSON><PERSON>, ExpressionWrapper, F
from django.db.models.functions import Trunc
from django.db.models import Date<PERSON><PERSON><PERSON><PERSON>, IntegerField
from django.db.models import Avg, Count, Min, Sum, Q, Max

# Register your models here.

@admin.register(MetricCounter)
class MetricCounterAdmin(admin.ModelAdmin):
    list_display = ('id', 'api','method', 'config_name', 'file_type','client_host','agent','pages','username','response','created_on','completed_on','status','message')
    list_filter = ("api", "method","file_type","client_host","config_name","status")
    search_fields = ("client_host", "message" )
#admin.site.register(InvoiceCounter)


def get_next_in_date_hierarchy(request, date_hierarchy):
        if date_hierarchy + '__day' in request.GET:
            return 'hour'

        if date_hierarchy + '__month' in request.GET:
            return 'day'

        if date_hierarchy + '__year' in request.GET:
            return 'week'

        return 'month'

@admin.register(CounterSummary)
class MetricSummaryAdmin(admin.ModelAdmin):
    actions = None
    change_list_template = 'admin/counter_summary_change_list.html'
    date_hierarchy = 'created_on'
    show_full_result_count = False
    list_filter = ("api", "method","config_name","client_host","username","status")
    

    def changelist_view(self, request, extra_context=None):
        period = get_next_in_date_hierarchy(request, self.date_hierarchy)
        

        response = super().changelist_view(
            request,
            extra_context=extra_context,
        )
        response.context_data['period'] = period
        try:
            qs = response.context_data['cl'].queryset
        except (AttributeError, KeyError):
            return response

        metrics = {
            'hits': Count('client_host'),
            #'method': Count('api')
            'success_ratio': ExpressionWrapper((Count('status',filter=Q(status=200))*100/Count('status')*100)/100,output_field=FloatField())

        }

        response.context_data['summary'] = list(
            qs
            .values('client_host',)
            .annotate(**metrics)
            .order_by()
        )
        response.context_data['total'] = dict(
            qs.aggregate(**metrics)
        )
        summary_over_time = qs.values('client_host',).annotate(**metrics).order_by().annotate(
            period=Trunc(
                'created_on',
                period,
                output_field=DateTimeField(),
            ),
        ).values('period').annotate(total=ExpressionWrapper(F('hits'),output_field=IntegerField())).order_by('period')
        summary_range = summary_over_time.aggregate(
            low=Min('total'),
            high=Max('total'),
        )
        high = summary_range.get('high', 0)
        low = 0#summary_range.get('low', 0)

        response.context_data['summary_over_time'] = [{
            'period': x['period'],
            'total': x['total'] or 0,
            'pct': \
               ((x['total'] or 0) - low) / (high - low) * 100
               if high > low else 0,
        } for x in summary_over_time]
        
        return response