# Generated by Django 3.1.1 on 2022-05-25 15:24

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='SplitConfiguration',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(db_index=True, max_length=100, unique=True)),
                ('description', models.TextField(blank=True, max_length=2000, null=True)),
                ('max_pagescan', models.IntegerField(default=20)),
            ],
        ),
    ]
