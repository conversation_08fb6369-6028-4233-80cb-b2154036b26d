from rest_framework.views import APIView
import json
from django.http import HttpResponse
from AMS.settings import BASE_DIR
import uuid
import os 
import base64
import os.path
from os import path
from pikepdf import Pdf
import json
from InvoiceSplit.utils import *

# Create your views here.

class ManualSplit(APIView):

    def post(self,request):
        del_files=[]
        invoicesplit = []
        error = {}
        try:
            base_path = BASE_DIR+'/extract_check/'

            json_body = json.loads(request.body.decode("utf-8"))
            base64__ = json_body["data"]
            manualSplitObj = json_body["manualsplit"]
            request_id = log_to_db(request, 'InvoiceSplit', 'manual_split', None)
            
            invoice_path = base_path+str(uuid.uuid4())+".pdf"
            with open(os.path.expanduser(invoice_path), 'wb') as fout:
                fout.write(base64.b64decode(base64__))
            
            print("File Written "+invoice_path)
            fout.close()
            del_files.append(invoice_path)

            for manualSplitItem in manualSplitObj:
                from_ = int(manualSplitItem['from']) - 1
                to_ = int(manualSplitItem['to'])    
                invoice_id = manualSplitItem["invoice"]
                if(from_ <= to_ ):
                    b64 = self.getBase64OfPDF(invoice_path,list(range(from_,to_)))
                    invoicesplit.append({"invoice":invoice_id,"base64":b64})
                
                else:
                    print("Invalid bounries, From:"+from_+", To:"+to_)
            
            for item in del_files:
                    if path.exists(item):
                        os.remove(item)
            print("files cleaned...")
            update_status(request_id, 200)
            return HttpResponse(json.dumps({"invoicesplit":invoicesplit},indent=4, sort_keys=True, default=str))
        except Exception as e:
            error['error'] = e
            for item in del_files:
                if path.exists(item):
                    os.remove(item)
            print("files cleaned...")
            update_status(request_id, 500, error_msg=e)
            return HttpResponse(json.dumps(error,indent=4, sort_keys=True, default=str))
        

    def get(self, request):

        res = {"response": "ok"}
        return HttpResponse(res)
    
    def get_base64(self,filepath):
        with open(filepath, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')
    
    def getBase64OfPDF(self,input_pdf,pages):
        temp_pdf_filepath = input_pdf.replace(".pdf","")+"_splited.pdf"
        input_pdf = Pdf.open(input_pdf)
         
        output = Pdf.new()
        for page in pages:
            #output.addPage(input_pdf.getPage(page))
            output.pages.append(input_pdf.pages[page])
        output.save(temp_pdf_filepath)

        #with open(temp_pdf_filepath, "wb") as output_stream:
            #output.write(output_stream)
        base64 = self.get_base64(temp_pdf_filepath)  
        if os.path.exists(temp_pdf_filepath):
            os.remove(temp_pdf_filepath)
        
        return base64

