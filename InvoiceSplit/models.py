from django.db import models

# Create your models here.
class SplitConfiguration(models.Model):
    name = models.CharField(max_length=100,db_index=True,unique=True)
    description = models.TextField(max_length=2000,null=True,blank=True,verbose_name="Match Field Names")
    #company_names = models.TextField(max_length=500,null=True,blank=True)
    #extract_field = models.ForeignKey(ExtractField,on_delete=models.CASCADE)
    #snow_url= models.TextField(max_length=200,null=True,blank=True)
    #ai_fieldmatch_threshold=models.IntegerField(default=95)
    max_pagescan=models.IntegerField(default=20)
    def __str__(self):
        return self.name
