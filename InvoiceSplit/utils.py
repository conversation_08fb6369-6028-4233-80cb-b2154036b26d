from logging import Logger
from datetime import datetime
from botocore.exceptions import ClientError
from metric_tracker.models import MetricCounter

logger = Logger(__name__)


class TextractDocumentTextDetector:
    def __init__(self, textract_client):
        self.textract_client = textract_client

    def detect_file_text(self, document_path):
        if document_path is not None:
            with open(document_path, 'rb') as document_file:
                document_bytes = document_file.read()
        try:
            response = self.textract_client.detect_document_text(
                Document={'Bytes': document_bytes})
            logger.info(
                "Detected %s blocks.", len(response['Blocks']))
        except ClientError:
            logger.exception("Couldn't detect text.")
            raise
        else:
            blocks = response['Blocks']
            text = ""
            for block in blocks:
                if block['BlockType'] == 'LINE':
                    text += f'{block["Text"]} '
            return text


def get_hostname_from_agent(request, config_name):
    hostname = request.headers['User-Agent'].lower().strip()
    if hostname.startswith('postman'):
        hostname = 'Postman'
    elif hostname.startswith('servicenow'):
        hostname = config_name
    else:
        hostname = request.headers['User-Agent']
    return hostname


def log_to_db(request, api, method, config_name):
    hostname = None
    username = None
    agent = None
    try:
        config_split = config_name.split('|')
        config_name = config_split[0].strip()
        hostname = request.META["HTTPS_X_INSTANCE_ID"] if "HTTPS_X_INSTANCE_ID" in request.META else None
        if hostname is None:
            hostname = request.META["HTTP_X_INSTANCE_ID"] if "HTTP_X_INSTANCE_ID" in request.META else None
        if hostname is None:
            if len(config_split) > 1:
                hostname = config_split[1].strip()
            else:
                hostname = get_hostname_from_agent(request, config_name)
        username = request.user.username
        agent = request.headers['User-Agent']
        c = MetricCounter(api=api, method=method, config_name=config_name,
                          client_host=hostname, username=username, response="None", agent=agent, status=1)
        c.save()
        request_id = c.id
        return request_id
    except Exception as e:
        print(e)
        print("failing to push logs to db")

def update_page_count(request_id, page_count):
    try:
        c = MetricCounter.objects.filter(id=request_id)[0]
        c.pages=page_count
        c.save()
    except:
        print(f"failed to update page count for id: {request_id}")

def update_status(request_id, status, error_msg=None):
    try:
        c = MetricCounter.objects.filter(id=request_id)[0]
        c.status = status
        c.completed_on = datetime.now()
        if error_msg is not None:
            c.message = error_msg
        c.save()
    except:
        print(f"failed to update status for id: {request_id}")
