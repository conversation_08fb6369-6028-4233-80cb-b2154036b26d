
def getkv(r):
    fields_local={}
    if len(r["ExpenseDocuments"])>0:

        for i in r["ExpenseDocuments"][0]['SummaryFields']:
            if "LabelDetection" in i:
                #self.fields[i["LabelDetection"]["Text"]]=i["ValueDetection"]["Text"]
                if len(i["ValueDetection"]["Text"].strip())>0:  
                    fields_local[i["LabelDetection"]["Text"]]=(i["ValueDetection"]["Text"],i["LabelDetection"]["Confidence"])
            else:
                #self.fields[i["Type"]["Text"]]=i["ValueDetection"]["Text"]
                if len(i["ValueDetection"]["Text"].strip())>0:
                    fields_local[i["Type"]["Text"]]=(i["ValueDetection"]["Text"],i["Type"]["Confidence"])
    return fields_local


    

 